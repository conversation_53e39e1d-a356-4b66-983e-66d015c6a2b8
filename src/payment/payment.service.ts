import {
  BadRequestException,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';
import {
  FlutterwaveAirtimeVendor,
  PurchaseAirtimeDto,
  QuickTellerAirtimeVendor,
} from './dto/purchase-airtime.dto';
import { AuthData } from '@crednet/authmanager';
import { randomUUID } from 'crypto';
import { BillRepository } from './repository/bill.repository';
import { UserRepository } from './repository/user.repository';
import { Bill, BillStatus } from './entities/bill.entity';
import { DataBundleRepository } from 'src/provider/repository/data-bundle.repository';
import { PurchaseDataDto } from './dto/purchase-data.dto';
import { PurchaseElectricDto } from './dto/purchase-electric.dto';
import { PurchaseCableDto } from './dto/purchase-cable.dto';
import { CableBundleRepository } from 'src/provider/repository/cable-bundle.repository';
import { BillerCategory } from '@app/baxi/baxi.interface';
import { BaxiService } from '@app/baxi';
import {
  Events,
  Exchanges,
  NotificationTemplates,
  PaymentEvents,
  PaymentRequestEventTypes,
} from 'src/utils/queue';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import {
  Currency,
  PaginationQueryDto,
  PaymentCacheService,
  PaymentTransactionSource,
  PaymentTransactionWalletType,
  QueryTransactionDto,
  RabbitmqService,
  ReverseTransactionInterface,
  SendNotificationPayload,
} from '@crednet/utils';
import { FindManyOptions, Like } from 'typeorm';
import { FlutterwaveService } from '@app/flutterwave';
import { CreditSwitchService } from '@app/credit-switch';
import { Providers } from 'src/provider/entities/provider.entity';
import { ConfigurationService } from 'src/configuration/configuration.service';
import { formatPhoneNumber } from 'src/utils/utils';
import { BillerRepository } from 'src/provider/repository/billers.repository';
import { baxiErrorCodes } from 'src/utils/baxi-errors';
import {
  isCreditSwitchErrorFinal,
  shouldEscalateCreditSwitchError,
  formatCreditSwitchError,
} from 'src/utils/credit-switch-errors';
import { WalletType } from './dto/purchase-bill.dto';
import { QuickTellerService } from '@app/quickteller';

@Injectable()
export class PaymentService {
  constructor(
    private readonly billRepository: BillRepository,
    private readonly userRepository: UserRepository,
    private readonly dataBundleRepository: DataBundleRepository,
    private readonly cableBundleRepository: CableBundleRepository,
    private readonly baxiService: BaxiService,
    private readonly flutterwaveService: FlutterwaveService,
    private readonly creditSwitchService: CreditSwitchService,
    @InjectQueue(Events.REQUERY_TRANSACTION)
    private readonly requeryTransactionQueue: Queue,
    private readonly rmqService: RabbitmqService,
    private readonly configurationService: ConfigurationService,
    private readonly billerRepository: BillerRepository,
    private readonly paymentCacheService: PaymentCacheService,
    private readonly quickTellerService: QuickTellerService,
  ) {
    // setTimeout(() => {
    //   this.refundTransaction(
    //    'cd2ee1ff-6bf8-438f-b398-a5046b2c9adc'
    //   )
    // }, 10000);
  }

  /**
   * Initializes an airtime purchase transaction.
   *
   * @param purchaseAirtimeDto - Data transfer object containing the airtime purchase details, including service type, phone number, and amount.
   * @param auth - Authentication data for the current user session.
   * @returns A promise that resolves with the result of the payment initiation.
   *
   * This method generates a unique reference for the transaction and constructs a request body
   * by copying properties from the provided DTO. It deletes sensitive information such as the pin.
   * It then retrieves or creates a user based on the authentication data and saves the transaction
   * details to the bill repository. Finally, it initiates the payment process.
   */
  async initAirtimePurchase(
    purchaseAirtimeDto: PurchaseAirtimeDto,
    auth: AuthData,
  ): Promise<any> {
    this.checkPnd(auth);
    const reference = randomUUID();
    let body = {};

    const config = await this.configurationService.getConfig(
      ConfigurationService.AIRTIME_PROVIDER,
    );

    if (config == Providers.FLUTERWAVE) {
      const [billerCode, itemCode] =
        FlutterwaveAirtimeVendor[purchaseAirtimeDto.serviceType].split('::');
      body = {
        billerCode,
        itemCode,
        reference,
        amount: purchaseAirtimeDto.amount,
        customerId: formatPhoneNumber(purchaseAirtimeDto.phone),
        country: 'NG',
      };
    } else if (config == Providers.QUICKTELLER) {
      const [billerCode, itemCode] =
        QuickTellerAirtimeVendor[purchaseAirtimeDto.serviceType].split('::');

      body = {
        billerCode,
        itemCode,
        reference,
        email: auth.email,
        phoneNumber: formatPhoneNumber(purchaseAirtimeDto.phone),
        amount: purchaseAirtimeDto.amount,
        customerId: formatPhoneNumber(purchaseAirtimeDto.phone),
        country: 'NG',
      };
    } else if (config == Providers.CREDIT_SWITCH) {
      body = {
        serviceType: purchaseAirtimeDto.serviceType,
        amount: purchaseAirtimeDto.amount,
        phone: formatPhoneNumber(purchaseAirtimeDto.phone),
        reference,
      };
    } else {
      body = {
        ...purchaseAirtimeDto,
        service_type: purchaseAirtimeDto.serviceType,
        // agentId: config.biils.agentId,
        agentReference: reference,
      };
      delete body['pin'];
    }

    const user = await this.userRepository.getOrCreateUser(auth);

    const bill = await this.billRepository.save({
      reference,
      serviceCategory: 'airtime',
      provider: this.getProviderFromConfig(config),
      walletType: purchaseAirtimeDto.wallet,
      beneficiary: purchaseAirtimeDto.phone,
      amount: purchaseAirtimeDto.amount,
      description: `Airtime purchase for ${purchaseAirtimeDto.phone}`,
      request: body,
      user: { id: user.id },
    });

    return this.initiatePayment(bill, auth.id + '');
  }

  /**
   * Initializes an internet data purchase transaction.
   *
   * @param dto - Data transfer object containing the internet data purchase details, including service type, phone number, and amount.
   * @param auth - Authentication data for the current user session.
   * @returns A promise that resolves with the result of the payment initiation.
   *
   * This method generates a unique reference for the transaction and constructs a request body
   * by copying properties from the provided DTO. It deletes sensitive information such as the pin.
   * It then retrieves or creates a user based on the authentication data, and retrieves the price of the data bundle
   * from the database, if the price is invalid or not found, it throws an UnprocessableEntityException
   * Finally, it initiates the payment process.
   */
  async initDataPurchase(dto: PurchaseDataDto, auth: AuthData): Promise<any> {
    this.checkPnd(auth);
    const reference = randomUUID();
    let body = {};

    const user = await this.userRepository.getOrCreateUser(auth);

    const databundle = await this.dataBundleRepository.findOne({
      where: { datacode: dto.datacode },
    });

    const config = await this.configurationService.getConfig(
      ConfigurationService.DATA_PROVIDER,
    );

    const amount = +databundle.price;

    if (config == Providers.FLUTERWAVE) {
      const bill = await this.billerRepository.findOneBy({
        serviceCategory: 'databundle',
        serviceType: Like(`%${dto.serviceType}%`),
        provider: Providers.FLUTERWAVE,
      });
      const databundle = await this.dataBundleRepository.findOneBy({
        datacode: dto.datacode,
        serviceType: dto.serviceType,
      });
      if (!databundle) {
        throw new UnprocessableEntityException(
          'This data bundle is not available at the moment.',
        );
      }
      body = {
        billerCode: bill.serviceCode,
        itemCode: databundle.datacode,
        reference,
        amount,
        type: databundle.name,
        customerId: formatPhoneNumber(dto.phone),
        country: 'NG',
      };
    } else if (config == Providers.QUICKTELLER) {
      const bill = await this.billerRepository.findOneBy({
        serviceCategory: 'databundle',
        serviceType: Like(`%${dto.serviceType}%`),
        provider: Providers.QUICKTELLER,
      });
      const databundle = await this.dataBundleRepository.findOneBy({
        datacode: dto.datacode,
        serviceType: dto.serviceType,
      });

      body = {
        billerCode: bill.serviceCode,
        itemCode: databundle.datacode,
        reference,
        amount,
        type: databundle.name,
        customerId: formatPhoneNumber(dto.phone),
        country: 'NG',
        email: auth.email,
        phoneNumber: formatPhoneNumber(dto.phone),
      };
    } else if (config == Providers.CREDIT_SWITCH) {
      const bill = await this.billerRepository.findOne({
        where: {
          serviceCategory: 'data',
          serviceType: Like(`%${dto.serviceType}%`),
          provider: Providers.CREDIT_SWITCH,
        },
      });
      console.log(bill);

      const databundle = await this.dataBundleRepository.findOneBy({
        datacode: dto.datacode,
        serviceType: dto.serviceType,
      });
      body = {
        datacode: databundle.datacode,
        serviceType: databundle.serviceType,
        amount,
        phone: formatPhoneNumber(dto.phone),
        reference,
      };
    } else {
      body = {
        ...dto,
        service_type: dto.serviceType,
        // agentId: config.biils.agentId,
        agentReference: reference,
      };
      delete body['pin'];
    }

    if (!amount || amount <= 1) {
      throw new UnprocessableEntityException('Invalid product price');
    }
    body['amount'] = amount;

    const bill = await this.billRepository.save({
      reference,
      walletType: dto.wallet,
      serviceCategory: 'databundle',
      beneficiary: dto.phone,
      amount,
      provider: this.getProviderFromConfig(config),
      description: `Internet purchase for ${dto.phone}`,
      request: body,
      user: { id: user.id },
    });

    return this.initiatePayment(bill, auth.id + '');
  }

  async initElectricityPurchase(
    dto: PurchaseElectricDto,
    auth: AuthData,
  ): Promise<any> {
    this.checkPnd(auth);
    const reference = randomUUID();
    const body = {
      ...dto,
      service_type: dto.serviceType,
      // agentId: config.biils.agentId,
      agentReference: reference,
    };

    delete body.pin;

    const user = await this.userRepository.getOrCreateUser(auth);

    const config = await this.configurationService.getConfig(
      ConfigurationService.ELECTRICITY_PROVIDER,
    );

    const bill = await this.billRepository.save({
      reference,
      walletType: dto.wallet,
      serviceCategory: 'electricity',
      beneficiary: dto.account_number,
      amount: dto.amount,
      provider: this.getProviderFromConfig(config),
      description: `Electricity purchase for ${dto.account_number}`,
      request: body,
      user: { id: user.id },
    });

    return this.initiatePayment(bill, auth.id + '');
  }

  async initCablePurchase(dto: PurchaseCableDto, auth: AuthData): Promise<any> {
    this.checkPnd(auth);
    const reference = randomUUID();
    let body = {};

    const user = await this.userRepository.getOrCreateUser(auth);

    const bundle = await this.cableBundleRepository.findOne({
      where: { code: dto.product_code },
    });

    if (!bundle.price) {
      throw new UnprocessableEntityException('Invalid product price');
    }

    let amount: number;

    if (bundle.provider == Providers.FLUTERWAVE) {
      const biller = await this.billerRepository.findOneBy({
        serviceCategory: 'cabletv',
        serviceType: dto.serviceType,
        provider: Providers.FLUTERWAVE,
      });
      const cablebundle = await this.cableBundleRepository.findOneBy({
        code: dto.product_code,
        validity: dto.product_monthsPaidFor,
      });
      amount = +cablebundle.price + cablebundle.fee;
      body = {
        billerCode: biller.serviceCode,
        itemCode: cablebundle.code,
        reference,
        amount,
        // type: databundle.name,
        customerId: dto.smartcard_number,
        country: 'NG',
      };
    } else if (bundle.provider == Providers.QUICKTELLER) {
      const biller = await this.billerRepository.findOneBy({
        serviceCategory: 'cabletv',
        serviceType: dto.serviceType,
        provider: Providers.QUICKTELLER,
      });
      const cablebundle = await this.cableBundleRepository.findOneBy({
        code: dto.product_code,
        validity: dto.product_monthsPaidFor,
      });
      amount = +cablebundle.price + cablebundle.fee;
      body = {
        billerCode: biller.serviceCode,
        itemCode: cablebundle.code,
        reference,
        amount,
        customerId: dto.smartcard_number,
        country: 'NG',
        email: auth.email,
        phoneNumber: formatPhoneNumber(auth.phone_no),
      };
    } else if (bundle.provider == Providers.CREDIT_SWITCH) {
      const cable = await this.cableBundlesForCreditSwitch(dto, reference);
      body = cable.body;
      amount = cable.amount;
    } else {
      const amount = +bundle.price + bundle.fee;
      body = {
        ...dto,
        product_code: dto.product_code?.split('::')[0],
        service_type: dto.serviceType,
        // agentId: config.biils.agentId,
        agentReference: reference,
        total_amount: amount,
      };
      delete body['pin'];
    }

    if (!amount || amount <= 1) {
      throw new UnprocessableEntityException('Invalid product price');
    }

    const bill = await this.billRepository.save({
      reference,
      walletType: dto.wallet,
      serviceCategory: 'cabletv',
      beneficiary: dto.smartcard_number,
      amount,
      provider: bundle.provider,
      description: `CableTV purchase for ${dto.smartcard_number}`,
      request: body,
      user: { id: user.id },
    });

    return this.initiatePayment(bill, auth.id + '');
  }

  private async cableBundlesForCreditSwitch(
    dto: PurchaseCableDto,
    reference: string,
  ) {
    const biller = await this.billerRepository.findOneBy({
      serviceCategory: 'cabletv',
      serviceType: dto.serviceType,
      provider: Providers.CREDIT_SWITCH,
    });
    const cablebundle = await this.cableBundleRepository.findOneBy({
      code: dto.product_code,
    });

    const amount =
      +dto.product_monthsPaidFor == 1
        ? +cablebundle.price + cablebundle.fee
        : +(cablebundle.price * 11) + cablebundle.fee;

    let body = {};

    switch (dto.serviceType.toLocaleLowerCase()) {
      case 'dstv':
      case 'gotv':
        const validateMultiChoiceCustomer =
          await this.creditSwitchService.validateMultichoiceCustomerNumber(
            dto.smartcard_number,
            dto.serviceType,
          );
        if (validateMultiChoiceCustomer.statusCode !== '00') {
          throw new BadRequestException('Cannot find smartcard number');
        }
        body = {
          billerCode: biller.serviceCode,
          productsCodes: [cablebundle.code],
          reference,
          amount,
          customerNo: validateMultiChoiceCustomer.statusDescription.customerNo,
          customerName: validateMultiChoiceCustomer.statusDescription.firstname,
          country: 'NG',
          invoicePeriod: dto.product_monthsPaidFor,
        };
        break;
      case 'startimes':
        const validateStartimesCustomer =
          await this.creditSwitchService.validateStartimesSmartcard(
            dto.smartcard_number,
          );
        if (validateStartimesCustomer.statusCode !== '00') {
          throw new BadRequestException('Cannot find smartcard number');
        }

        body = {
          billerCode: biller.serviceCode,
          smartCardCode: cablebundle.code,
          reference,
          amount,
          country: 'NG',
        };
        break;
      case 'showmax':
        body = {
          billerCode: biller.serviceCode,
          subscriptionType: cablebundle.code,
          invoicePeriod: dto.product_monthsPaidFor,
          packageName: dto.name,
          customerNo: dto.smartcard_number,
          reference,
          amount,
          country: 'NG',
        };
        break;
      default:
        break;
    }

    return { body, amount };
  }

  async initiatePayment(bill: Bill, userId: string) {
    this.paymentCacheService.savePayment({
      source: PaymentTransactionSource.BILLS_SERVICE,
      userId,
      walletType:
        bill.walletType == WalletType.credit
          ? PaymentTransactionWalletType.CREDPAL_CREDIT
          : PaymentTransactionWalletType.CREDPAL_CASH,
      currency: Currency.NGN,
      reference: bill.reference,
      amount: +bill.amount,
      description: bill.description,
      returningRoutingKey: PaymentEvents.BILL_PAYMENT_STATUS,
      meta: {
        reference: bill.reference,
        amount: bill.amount,
        wallet: bill.walletType,
      },
    });

    await this.billRepository.update(
      { id: bill.id },
      { paymentStatus: BillStatus.PROCESSING },
    );

    return bill;
  }

  private getPurchaseUrl(serviceCategory: BillerCategory): string {
    switch (serviceCategory) {
      case 'airtime':
        return 'services/airtime/request';
      case 'cabletv':
        return 'services/multichoice/request';
      case 'databundle':
        return 'services/databundle/request';
      case 'electricity':
        return 'services/electricity/request';
      default:
        return '';
    }
  }

  async finalizePayment(bill: Bill) {
    await this.billRepository.update(
      { id: bill.id },
      { paymentStatus: BillStatus.SUCCESS },
    );

    if (bill.provider == Providers.FLUTERWAVE) {
      try {
        const response = await this.flutterwaveService.purchaseBill(
          bill.request,
        );

        if (response.status == 'error') {
          await this.billRepository.update(
            { id: bill.id },
            { errors: response },
          );
        }
      } catch (error) {
        if (error.status == 'error') {
          await this.billRepository.update({ id: bill.id }, { errors: error });
        }
      } finally {
        await this.billRepository.update(
          { id: bill.id },
          { status: BillStatus.PROCESSING },
        );
      }
    } else if (bill.provider == Providers.BAXI) {
      try {
        const response = await this.baxiService.purchase(
          this.getPurchaseUrl(bill.serviceCategory),
          bill.request,
        );

        if (response.status == 'error') {
          await this.billRepository.update(
            { id: bill.id },
            { errors: response },
          );
        }
      } catch (error) {
        console.log(error);
        await this.billRepository.update({ id: bill.id }, { errors: error });
      } finally {
        await this.billRepository.update(
          { id: bill.id },
          { status: BillStatus.PROCESSING },
        );
      }
    } else if (bill.provider == Providers.CREDIT_SWITCH) {
      try {
        console.log('I reached here!!!');
        console.log('I reached here!!!');
        console.log('I reached here!!!');

        const response = await this.processCreditSwitchPayment(bill);

        if (
          !this.creditSwitchService.isTransactionSuccessful(response.statusCode)
        ) {
          await this.billRepository.update(
            { id: bill.id },
            { errors: response },
          );
        }
      } catch (error) {
        console.log('Credit Switch payment error:', error);
        await this.billRepository.update({ id: bill.id }, { errors: error });
      } finally {
        await this.billRepository.update(
          { id: bill.id },
          { status: BillStatus.PROCESSING },
        );
      }
    }
    this.requeryTransactionQueue.add(
      'requery_transaction',
      {
        reference: bill.reference,
      },
      {
        lifo: false,
        attempts: 1,
        backoff: { type: 'exponential', delay: 2000 },
        jobId: randomUUID(),
        removeOnComplete: true,
        removeOnFail: false,
        delay: 10000,
      },
    );
  }

  async requery(reference: string): Promise<any> {
    const txn = await this.billRepository.findOneBy({
      reference,
    });

    if (!txn || txn.status == BillStatus.SUCCESS) {
      return;
    }

    switch (txn.provider) {
      case Providers.BAXI:
        this.verifyBaxiPayment(reference, txn);
        break;
      case Providers.FLUTERWAVE:
        this.verifyFutterwavePayment(reference, txn);
        break;
      case Providers.QUICKTELLER:
        this.verifyQuickTellerPayment(reference, txn);
        break;
      case Providers.CREDIT_SWITCH:
        this.verifyCreditSwitchPayment(reference, txn);
        break;
      default:
        break;
    }
  }

  async verifyFutterwavePayment(reference: any, bill: Bill) {
    try {
      const data = await this.flutterwaveService.verifyTransaction(reference);
      console.log('verifyFutterwavePayment ', data);

      switch (data?.data?.status ?? data?.status) {
        case 'error':
        case 'failed':
          // if (String(data?.message).includes('not found')) {
          //   this.finalizePayment(bill);
          //   return;
          // }
          if (String(data?.message).includes('getaddrinfo EAI_AGAIN')) {
            this.requeryTransactionQueue.add(
              'requery_transaction',
              {
                reference,
              },
              {
                lifo: false,
                attempts: 1,
                backoff: { type: 'exponential', delay: 2000 },
                jobId: randomUUID(),
                removeOnComplete: true,
                removeOnFail: false,
                delay: 10000,
              },
            );
            return;
          }
          this.handleError(
            reference,
            data,

            data?.message == 'Transaction not found' ||
              String(data?.message)?.toLowerCase() == 'failed' ||
              String(data?.status)?.toLowerCase() == 'error' ||
              String(data?.status)?.toLowerCase() == 'failed',
          );
          break;
        case 'success':
        case 'successful':
          this.handleSuccess(reference, data?.data);
          break;

        default:
          break;
      }
    } catch (error) {
      // console.log(error);
      console.log('verifyFutterwavePayment ', error);

      // if (String(error?.message).includes('not found')) {
      //   this.finalizePayment(bill);
      //   return;
      // }
      if (String(error?.message).includes('getaddrinfo EAI_AGAIN')) {
        this.requeryTransactionQueue.add(
          'requery_transaction',
          {
            reference,
          },
          {
            lifo: false,
            attempts: 1,
            backoff: { type: 'exponential', delay: 2000 },
            jobId: randomUUID(),
            removeOnComplete: true,
            removeOnFail: false,
            delay: 10000,
          },
        );
        return;
      }
      this.handleError(
        reference,
        error,
        error?.message == 'Transaction not found' ||
          String(error?.message)?.toLowerCase() == 'failed' ||
          String(error?.status)?.toLowerCase() == 'error' ||
          String(error?.status)?.toLowerCase() == 'failed',
      );
    }
  }

  async verifyQuickTellerPayment(reference: any, bill: Bill) {
    try {
      const data = await this.quickTellerService.requeryTransaction(reference);
      console.log(data);

      switch (data?.transactionStatus ?? data?.status) {
        case 'error':
          if (String(data?.message).includes('not found')) {
            this.finalizePayment(bill);
            return;
          }
          if (String(data?.message).includes('too many re-query request')) {
            return;
          }
          this.handleError(
            reference,
            data,
            String(data?.message)?.toLowerCase() == 'failed',
          );
          break;
        case 'success':
          this.handleSuccess(reference, data);
          break;

        default:
          break;
      }
    } catch (error) {
      if (String(error?.message).includes('not found')) {
        this.finalizePayment(bill);
        return;
      }
      if (String(error?.message).includes('too many re-query request')) {
        return;
      }
      this.handleError(
        reference,
        error,
        String(error?.message)?.toLowerCase() == 'failed',
      );
    }
  }

  async verifyBaxiPayment(reference: any, bill: Bill) {
    try {
      const data = await this.baxiService.requery(reference);
      console.log('verifyBaxiPayment data:', data);

      switch (data?.transactionStatus ?? data?.status) {
        case 'error':
          // if (String(data?.message).includes('not found')) {
          //   this.finalizePayment(bill);
          //   return;
          // }
          if (
            String(data?.message).includes('too many re-query request') ||
            String(data?.message).includes('504 Gateway Time-out')
          ) {
            return;
          }
          this.handleError(reference, data, baxiErrorCodes.includes(data.code));
          break;
        case 'success':
          this.handleSuccess(reference, data);
          break;

        default:
          break;
      }
    } catch (error) {
      console.log('verifyBaxiPayment error:', error);
      // if (String(error?.message).includes('not found')) {
      //   this.finalizePayment(bill);
      //   return;
      // }
      if (
        String(error?.message).includes('too many re-query request') ||
        String(error?.message).includes('504 Gateway Time-out')
      ) {
        return;
      }
      this.handleError(
        reference,
        error,
        baxiErrorCodes.includes(error.code) ||
          String(error?.message).includes('not found'),
      );
    }
  }

  async verifyCreditSwitchPayment(reference: any, bill: Bill) {
    try {
      // Extract service ID from the bill request
      const serviceId = await this.getCreditSwitchServiceId(bill);
      const data = await this.creditSwitchService.requery(reference, serviceId);
      console.log('verifyCreditSwitchPayment data:', data);

      if (this.creditSwitchService.isTransactionSuccessful(data.statusCode)) {
        this.handleSuccess(reference, data);
      } else {
        // Use Credit Switch specific error handling
        const errorMessage = formatCreditSwitchError(
          data.statusCode,
          data.statusDescription,
          { reference, serviceId },
        );
        console.log(errorMessage);

        const shouldFinalize = isCreditSwitchErrorFinal(data.statusCode);
        this.handleError(reference, data, shouldFinalize);

        // Escalate to support if needed
        if (shouldEscalateCreditSwitchError(data.statusCode)) {
          console.log(
            `Credit Switch error requires escalation: ${errorMessage}`,
          );
          // TODO: Implement support escalation logic
        }
      }
    } catch (error) {
      console.log('verifyCreditSwitchPayment error:', error);

      // Handle specific Credit Switch error scenarios
      if (
        String(error?.message).includes('too many re-query request') ||
        String(error?.message).includes('504 Gateway Time-out') ||
        String(error?.message).includes('timeout')
      ) {
        return;
      }

      this.handleError(
        reference,
        error,
        String(error?.message).includes('not found') ||
          String(error?.message).includes('invalid request'),
      );
    }
  }

  private async getCreditSwitchServiceId(bill: Bill): Promise<string> {
    const biller = await this.billerRepository.findOneBy({
      serviceType: Like(`%${bill.request.serviceType}%`),
      serviceCategory: bill.serviceCategory,
      provider: Providers.CREDIT_SWITCH,
    });
    return biller.serviceId;
  }

  private async processCreditSwitchPayment(bill: Bill): Promise<any> {
    const network = this.creditSwitchService.mapNetworkToInternal(
      bill.request.serviceType,
    );

    switch (bill.serviceCategory) {
      case 'airtime':
        return await this.creditSwitchService.vendAirtime(
          network,
          bill.amount,
          bill.beneficiary,
          bill.reference,
        );

      case 'databundle':
        return await this.creditSwitchService.vendData(
          network,
          bill.amount,
          bill.beneficiary,
          bill.reference,
          bill.request.datacode,
        );

      case 'electricity':
        const serviceId = await this.getCreditSwitchServiceId(bill);
        return await this.creditSwitchService.vendElectricity(
          serviceId,
          bill.amount,
          bill.request.account_number,
          bill.request.phone,
          bill.reference,
          bill.request?.customerAddress,
          bill.request?.customerName,
        );
      case 'cabletv':
        if (bill.request.serviceType == 'showmax') {
          return await this.creditSwitchService.payShowmaxSubscription(
            bill.beneficiary,
            bill.amount,
            bill.request.subscriptionType,
            bill.request.invoicePeriod,
            bill.request.packageName,
            bill.reference,
          );
        } else if (bill.request.serviceType == 'startimes') {
          return await this.creditSwitchService.vendStartimes(
            bill.beneficiary,
            bill.amount,
            bill.reference,
          );
        } else {
          return await this.creditSwitchService.vendMultichoice(
            bill.beneficiary,
            bill.amount,
            bill.reference,
            bill.request.serviceType,
            bill.request.customerName,
          );
        }

      default:
        throw new Error(
          `Unsupported service category: ${bill.serviceCategory}`,
        );
    }
  }

  private getProviderFromConfig(config: string): Providers {
    switch (config) {
      case Providers.FLUTERWAVE:
        return Providers.FLUTERWAVE;
      case Providers.QUICKTELLER:
        return Providers.QUICKTELLER;
      case Providers.CREDIT_SWITCH:
        return Providers.CREDIT_SWITCH;
      default:
        return Providers.BAXI;
    }
  }

  async checkLookup(bill: Bill) {
    try {
      return bill.provider == Providers.BAXI
        ? await this.baxiService.lookup(
            bill.request.serviceType,
            bill.beneficiary,
          )
        : await this.flutterwaveService.lookup(
            bill.request.itemCode,
            bill.request.billerCode,
            bill.beneficiary,
          );
    } catch (error) {
      console.log(error);
    }
  }

  private async handleSuccess(reference: string, data: any) {
    console.log('handleSuccess', data);
    await this.billRepository.update(
      { reference },
      {
        meta: data,
        status: BillStatus.SUCCESS,
      },
    );
    const bill = await this.billRepository.findOne({
      where: {
        reference,
      },
      relations: ['user'],
    });

    const lookup = await this.checkLookup(bill);
    const meta = { ...bill.meta };

    if (lookup) {
      meta['customerData'] = lookup;
    }
    await this.billRepository.update(
      { reference },
      {
        meta,
        status: BillStatus.SUCCESS,
      },
    );
    // await this.updatePayment(bill.reference, 'success', bill.walletType);

    await this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.BILLS_NOTIFICATION,
      data: {
        template:
          bill.serviceCategory == 'electricity'
            ? NotificationTemplates.ELECTRICITY_PAYMENT_SUCCESSFUL
            : NotificationTemplates.BILL_PAYMENT_SUCCESSFUL,
        userId: bill.user?.userId,
        parameter: {
          amount: bill.amount,
          date: bill.createdAt.toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
          currency: '₦',
          reference: bill.reference,
          wallet: bill.walletType,
          serviceType: bill.serviceCategory,
          customerId: bill.beneficiary,
          phoneNumer: bill.request['phone'],
          meterNumber: bill.beneficiary,
          token: bill.meta?.token ?? bill.meta?.tokenCode,
          amountOfPower: bill.meta?.amountOfPower,
          address: lookup?.user?.address,
          customerName:
            lookup?.user?.name ?? lookup?.user?.rawOutput?.customerName,
          serviceProvider: String(bill.request?.serviceType).replace('_', ' '),
          isElectricity: bill.serviceCategory == 'electricity',
        },
      } as SendNotificationPayload,
    });
  }

  private async handleError(
    reference: string,
    error: any,
    updateStatus = false,
  ) {
    const update = {
      errors: typeof error === 'string' ? { message: error } : error,
    };
    const bill = await this.billRepository.findOne({
      where: {
        reference,
      },
      relations: ['user'],
    });

    update['status'] = BillStatus.FAILED;
    if (updateStatus) {
      console.log('handleError ', updateStatus, bill.reference);
      this.refundTransaction(bill.reference);

      await this.rmqService.send(Exchanges.NOTIFICATION, {
        key: Events.BILLS_NOTIFICATION,
        data: {
          template: NotificationTemplates.BILL_PAYMENT_FAILED,
          userId: bill.user?.userId,
          parameter: {
            amount: bill.amount,
            reference: bill.reference,
            wallet: bill.walletType,
            serviceType: bill.serviceCategory,
            customerId: bill.beneficiary,
          },
        } as SendNotificationPayload,
      });
    } else {
      // mark as success, leave for manual refund
      // await this.updatePayment(bill.reference, 'success', bill.walletType);
    }
    await this.billRepository.update({ reference }, update);
  }

  async requeryProcesing(page: number) {
    console.log('running job:: requeryProcesing ', page);
    const items = await this.billRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          status: BillStatus.PROCESSING,
          paymentStatus: BillStatus.SUCCESS,
        },
      },
    );

    for (const item of items.items) {
      console.log('running job:: requeryProcesing ', item.reference);
      try {
        await this.requery(item.reference);
      } catch (error) {
        console.log(error);
      }
    }

    if (page < items.meta.totalPages) {
      return this.requeryProcesing(++page);
    }
  }

  async getBills(authData: AuthData, paginationMeta: PaginationQueryDto) {
    const user = await this.userRepository.getOrCreateUser(authData);

    const body: FindManyOptions<Bill> = {
      where: { user: { id: user.id } },
    };

    if (paginationMeta.sortColumn && paginationMeta.sortOrder) {
      body.order = {
        [paginationMeta.sortColumn ?? 'createdAt']:
          paginationMeta.sortOrder ?? 'DESC',
      };
    }
    console.log(paginationMeta, body);

    return this.billRepository.findMany(
      {
        limit: paginationMeta.limit ?? 30,
        page: paginationMeta.page,
      },
      body,
    );
  }

  async getBill(authData: AuthData, id: string) {
    const user = await this.userRepository.getOrCreateUser(authData);

    return this.billRepository.findOne({
      where: { user: { id: user.id }, id },
    });
  }

  async reprocessPendingProcesing(page: number) {
    console.log('running job:: reprocessPendingProcesing ', page);
    const items = await this.billRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          status: BillStatus.PENDING,
          paymentStatus: BillStatus.SUCCESS,
        },
      },
    );

    for (const item of items.items) {
      await this.finalizePayment(item);
    }

    if (page < items.meta.totalPages) {
      return this.reprocessPendingProcesing(++page);
    }
  }

  async requeryPendingPayment(page: number) {
    console.log('running job:: reprocessPendingProcesing ', page);
    const items = await this.billRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          // status: BillStatus.PENDING,
          paymentStatus: BillStatus.PROCESSING,
        },
      },
    );

    for (const item of items.items) {
      this.rmqService.send(Exchanges.PAYMENT, {
        key: PaymentRequestEventTypes.QUERY_TRANSACTION,
        data: {
          returningRoutingKey: PaymentEvents.BILL_PAYMENT_STATUS,
          reference: item.reference,
        } as QueryTransactionDto,
      });
    }

    if (page < items.meta.totalPages) {
      return this.requeryPendingPayment(++page);
    }
  }

  async handleRefund(page: number) {
    console.log('running job:: handleRefund ', page);
    const items = await this.billRepository.findMany(
      {
        page,
        limit: 100,
      },
      {
        where: {
          status: BillStatus.FAILED,
          paymentStatus: BillStatus.SUCCESS,
          isRefunded: false,
        },
      },
    );

    for (const item of items.items) {
      console.log('running job:: handleRefund ', item.reference);

      this.refundTransaction(item.reference);
    }

    if (page < items.meta.totalPages) {
      return this.handleRefund(++page);
    }
  }

  refundTransaction(reference: string) {
    console.log('refundTransaction ', reference);
    this.rmqService.send(Exchanges.PAYMENT, {
      key: PaymentRequestEventTypes.REVERSE_TRANSACTION,
      data: {
        source: PaymentTransactionSource.BILLS_SERVICE,
        reference,
        reason: 'Transaction failed from provider',
        returningRoutingKey: PaymentEvents.BILL_PAYMENT_STATUS,
      } as ReverseTransactionInterface,
    });
  }

  private checkPnd(auth: AuthData) {
    if (auth.pnd == 1) {
      throw new BadRequestException(
        'You are not allowed to complete this operation, please contact support',
      );
    }
  }
}
