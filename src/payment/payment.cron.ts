import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PaymentService } from './payment.service';

@Injectable()
export class PaymentCron {
  constructor(private readonly paymentService: PaymentService) {
    // setTimeout(() => {
    //   this.requeryProcesing();
    // }, 5000);
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async requeryProcesing() {
    await this.paymentService.requeryProcesing(1);
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async reprocessPendingProcesing() {
    await this.paymentService.reprocessPendingProcesing(1);
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async requeryPendingPayment() {
    await this.paymentService.requeryPendingPayment(1);
  }

  @Cron(CronExpression.EVERY_30_MINUTES)
  async handleRefund() {
    await this.paymentService.handleRefund(1);
  }
}
