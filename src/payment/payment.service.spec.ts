import { Test, TestingModule } from '@nestjs/testing';
import { PaymentService } from './payment.service';
import { BillRepository } from './repository/bill.repository';
import { UserRepository } from './repository/user.repository';
import { DataBundleRepository } from '../provider/repository/data-bundle.repository';
import { CableBundleRepository } from '../provider/repository/cable-bundle.repository';
import { BaxiService } from '@app/baxi';
import { FlutterwaveService } from '@app/flutterwave';
import { RabbitmqService } from '@crednet/utils';
import { ConfigurationService } from '../configuration/configuration.service';
import { BillerRepository } from '../provider/repository/billers.repository';
import { PaymentCacheService } from '@crednet/utils';
import { Queue } from 'bullmq';
import { Events } from '../utils/queue';

describe('PaymentService', () => {
  let service: PaymentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        {
          provide: BillRepository,
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            findMany: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: DataBundleRepository,
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: CableBundleRepository,
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: BaxiService,
          useValue: {
            purchaseAirtime: jest.fn(),
            purchaseData: jest.fn(),
            purchaseElectricity: jest.fn(),
            purchaseCable: jest.fn(),
            verifyTransaction: jest.fn(),
          },
        },
        {
          provide: FlutterwaveService,
          useValue: {
            purchaseAirtime: jest.fn(),
            purchaseData: jest.fn(),
            purchaseElectricity: jest.fn(),
            purchaseCable: jest.fn(),
            verifyTransaction: jest.fn(),
          },
        },
        {
          provide: RabbitmqService,
          useValue: {
            send: jest.fn(),
          },
        },
        {
          provide: ConfigurationService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: BillerRepository,
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: PaymentCacheService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
          },
        },
        {
          provide: `BullQueue_${Events.REQUERY_TRANSACTION}`,
          useValue: {
            add: jest.fn(),
            process: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
