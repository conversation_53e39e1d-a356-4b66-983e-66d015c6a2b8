import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { BillRepository } from './repository/bill.repository';
import { UserRepository } from './repository/user.repository';
import { DataBundleRepository } from 'src/provider/repository/data-bundle.repository';
import { CableBundleRepository } from 'src/provider/repository/cable-bundle.repository';
import { BaxiModule } from '@app/baxi';
import { VerifyBillConsumer } from './consumer/verify-bill.consumer';
import { Events } from 'src/utils/queue';
import { BullModule } from '@nestjs/bullmq';
import { ExportPaymentConsumer } from './consumer/process-payment.consumer';
import { PaymentCron } from './payment.cron';
import { WebhookConsumer } from './consumer/webhook.consumer';
import { FlutterwaveModule } from '@app/flutterwave';
import { CreditSwitchModule } from '@app/credit-switch';
import { ConfigurationModule } from 'src/configuration/configuration.module';
import { BillerRepository } from 'src/provider/repository/billers.repository';
import { PaymentCacheModule } from '@crednet/utils';
import config from 'src/config';
import { QuickTellerModule } from '@app/quickteller';

@Module({
  imports: [
    BaxiModule,
    FlutterwaveModule,
    CreditSwitchModule,
    ConfigurationModule,
    BullModule.registerQueue({ name: Events.REQUERY_TRANSACTION }),
    PaymentCacheModule,
    QuickTellerModule,
  ],
  controllers: [PaymentController],
  providers: [
    PaymentService,
    BillRepository,
    UserRepository,
    CableBundleRepository,
    DataBundleRepository,
    VerifyBillConsumer,
    ExportPaymentConsumer,
    WebhookConsumer,
    ...(config.isCronEnabled
      ? [PaymentCron, BillerRepository]
      : [BillerRepository]),
    BillerRepository,
  ],
  exports: [BillRepository, UserRepository],
})
export class PaymentModule {}
