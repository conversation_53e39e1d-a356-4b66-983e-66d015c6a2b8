import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PurchaseAirtimeDto } from './dto/purchase-airtime.dto';
import {
  AuthData,
  GetAuthData,
  JwtAuthGuard,
  PinGuard,
  PinRequirement,
} from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PurchaseDataDto, PurchaseInternetDto } from './dto/purchase-data.dto';
import { PurchaseElectricDto } from './dto/purchase-electric.dto';
import { PaginationQueryDto, VersionBuildGuard } from '@crednet/utils';
import { PurchaseCableDto } from './dto/purchase-cable.dto';

@Controller('payment')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @Post('/airtime')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  initAirtimePurchase(
    @Body() dto: PurchaseAirtimeDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.paymentService.initAirtimePurchase(dto, auth);
  }

  @Get('/transactions')
  queryBills(
    @Query() dto: PaginationQueryDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.paymentService.getBills(auth, dto);
  }

  @Get('/transactions/:id')
  getBill(
    @Param('id') id: string,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.paymentService.getBill(auth, id);
  }

  @Post('/databundle')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  initDataPurchase(
    @Body() dto: PurchaseDataDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.paymentService.initDataPurchase(dto, auth);
  }

  @Post('/internet')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  initInternetPurchase(
    @Body() dto: PurchaseInternetDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.paymentService.initDataPurchase(dto, auth);
  }

  @Post('/electricity')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  initElectricityPurchase(
    @Body() dto: PurchaseElectricDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.paymentService.initElectricityPurchase(dto, auth);
  }

  @Post('/cabletv')
  @UseGuards(PinGuard, VersionBuildGuard)
  @PinRequirement('pin')
  initCabletvPurchase(
    @Body() dto: PurchaseCableDto,
    @GetAuthData() auth: AuthData,
  ): Promise<any> {
    return this.paymentService.initCablePurchase(dto, auth);
  }
}
