import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { User } from '../entities/user.entity';
import { AuthData } from '@crednet/authmanager';

@Injectable()
export class UserRepository extends TypeOrmRepository<User> {
  constructor(private readonly dataSource: DataSource) {
    super(User, dataSource.createEntityManager());
  }

  async getOrCreateUser(auth: AuthData): Promise<User> {
    let user = await this.findOne({ where: { userId: auth.id.toString() } });

    if (!user) {
      user = await this.save({
        userId: auth.id.toString(),
        email: auth.email,
        firstName: auth.name,
        lastName: auth.last_name,
        phoneNumber: auth.phone_no,
      });
    }

    return user;
  }
}
