import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, Length } from 'class-validator';

export enum WalletType {
  cash = 'cash-wallet',
  credit = 'credit',
}

export class PinDto {
  @ApiProperty()
  @IsString()
  @Length(4, 4)
  pin: string;
}
export class PurchaseBillDto extends PinDto {
  @ApiProperty({enum: WalletType})
  @IsString()
  @IsEnum(WalletType)
  wallet: WalletType;
}
