import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsString, Matches, Max, Min } from 'class-validator';
import { PurchaseBillDto } from './purchase-bill.dto';

export enum FlutterwaveAirtimeVendor {
  mtn = 'BIL099::AT099',
  airtel = 'BIL100::AT100',
  glo = 'BIL102::AT133',
  '9mobile' = 'BIL103::AT134',
}

export enum QuickTellerAirtimeVendor {
  mtn = '109::10906',
  airtel = '108::10803',
  glo = '402::40201',
  '9mobile' = '120::12002',
}
// AT099
// export enum FlutterwaveDataVendor {
//   mtn = 'BIL108',
//   airtel = 'BIL110',
//   glo = 'BIL109',
//   '9mobile' = 'BIL111',
// }
export enum AirtimeVendor {
  MTN = 'mtn',
  AIRTEL = 'airtel',
  GLO = 'glo',
  NINE_MOBILE = '9mobile',
}

export enum AirtimePlan {
  PREPAID = 'prepaid',
  POSTPAID = 'postpaid',
}

export class PurchaseAirtimeDto extends PurchaseBillDto {
  @ApiProperty({ enum: AirtimeVendor })
  @IsString()
  @IsEnum(AirtimeVendor)
  serviceType: AirtimeVendor;

  @ApiProperty({ enum: AirtimePlan })
  @IsString()
  @IsEnum(AirtimePlan)
  plan: AirtimePlan;

  @ApiProperty()
  @IsString()
  @Matches(/^0[7-9][0-1]\d{8,9}$/, {
    message: 'Phone number must be a valid Nigerian number',
  })
  phone: string;

  @ApiProperty()
  @IsNumber({ allowNaN: false, allowInfinity: false })
  @Min(50)
  @Max(20000)
  amount: number;
}
