import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
  Matches,
  Min,
} from 'class-validator'; 
import { PurchaseBillDto } from './purchase-bill.dto';

export enum DataVendor {
  MTN = 'mtn',
  AIRTEL = 'airtel',
  GLO = 'glo',
  NINE_MOBILE = '9mobile',
  // SPECTRANET = 'spectranet',
  // SMILE = 'smile',
  // DSTV_SHOWMAX = 'dstvshowmax',
}

export enum InternetVendor { 
  SPECTRANET = 'spectranet',
  SMILE = 'smile',
  DSTV_SHOWMAX = 'dstvshowmax',
}


export class PurchaseDataDto extends PurchaseBillDto {
  @ApiProperty()
  @IsString()
  @IsEnum(DataVendor)
  serviceType: DataVendor | InternetVendor;

  @ApiProperty()
  @IsString()
  @Matches(new RegExp('^[0][7-9]{1}[0-1]{1}[0-9]{8,9}$'), {
    message: 'Phone number must be a valid Nigerian number',
  })
  phone: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  datacode: string;

  // @ApiProperty()
  // @IsNumber({ allowNaN: false, allowInfinity: false })
  // @Min(50)
  // amount: number;
}

export class PurchaseInternetDto extends PurchaseBillDto {
  @ApiProperty({enum: InternetVendor})
  @IsString()
  @IsEnum(InternetVendor)
  serviceType: InternetVendor | DataVendor;

  @ApiProperty()
  @IsString()
  // @IsLen 
  phone: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  datacode: string;

  // @ApiProperty()
  // @IsNumber({ allowNaN: false, allowInfinity: false })
  // @Min(50)
  // amount: number;
}
