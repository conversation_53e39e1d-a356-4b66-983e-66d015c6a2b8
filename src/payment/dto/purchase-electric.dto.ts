import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Matches, Min } from 'class-validator';
import { PurchaseBillDto } from './purchase-bill.dto';

export class PurchaseElectricDto extends PurchaseBillDto {
  @ApiProperty()
  @IsString()
  serviceType: string;

  @ApiProperty()
  @IsString()
  account_number: string;

  @ApiProperty()
  @IsNumber({ allowNaN: false, allowInfinity: false })
  @Min(1000)
  amount: number;

  @ApiProperty()
  @IsString()
  @Matches(new RegExp('^[0][7-9]{1}[0-1]{1}[0-9]{8,9}$'), {
    message: 'Phone number must be a valid Nigerian number',
  })
  phone: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  customerAddress?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  customerName?: string;
}
