import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { PurchaseBillDto } from './purchase-bill.dto';

export class PurchaseCableDto extends PurchaseBillDto {
  @ApiProperty()
  @IsString()
  serviceType: string;

  @ApiProperty()
  @IsString()
  smartcard_number: string;

  @ApiProperty()
  @IsNumber({ allowNaN: false, allowInfinity: false })
  @Min(100)
  total_amount: number;

  @ApiProperty()
  @IsString()
  product_code: string;

  @ApiProperty()
  @IsOptional()
  product_monthsPaidFor: string;

  @ApiPropertyOptional()
  @IsOptional()
  name: string;
}
