import { Injectable, OnModuleInit } from '@nestjs/common';
import {
  Events,
  Exchanges,
  NotificationTemplates,
  PaymentEvents,
} from 'src/utils/queue';
import { PaymentService } from '../payment.service';
import { BillRepository } from '../repository/bill.repository';
import { Bill, BillStatus } from '../entities/bill.entity';
import {
  PaymentTransaction,
  PaymentTransactionStatus,
  PaymentTransactionWalletType,
  QueryTransactionResponseDto,
  RabbitmqService,
  SendNotificationPayload,
} from '@crednet/utils';

@Injectable()
export class ExportPaymentConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    //   private readonly emailService: EmailService,
    private readonly paymentService: PaymentService,
    private readonly billRepository: BillRepository,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.PAYMENT}.${PaymentEvents.BILL_PAYMENT_STATUS}`,
      async ({ data, message, client, ack, reject }) => {
        console.log(data, data?.status, 'status');
        try {
          if (data?.status) {
            await this.handlePaymentStatus(data, ack, reject);
          } else {
            console.log('Meta not found');
            ack();
            return;
          }
        } catch (e) {
          console.log(e);
          //force the error to be thrown so that rabbitmq can nack it in the rabbitmq service
          throw e;
        }
      },
    );
  }

  async handlePaymentStatus(
    payload: QueryTransactionResponseDto,
    ack: Function,
    reject: Function,
  ) {
    try {
      const { reference, status } = payload;
      const data = await this.billRepository.findOne({
        where: { reference },
      });

      if (
        (data?.paymentStatus != BillStatus.SUCCESS &&
          data?.paymentStatus != BillStatus.FAILED) ||
        (status == PaymentTransactionStatus.REVERSED && !data?.isRefunded)
      ) {
        switch (status) {
          case PaymentTransactionStatus.SUCCESSFUL:
            this.handleSuccess(data);
            break;

          case PaymentTransactionStatus.FAILED:
            this.handleFailed(data, payload.error);
            break;

          case PaymentTransactionStatus.REVERSED:
            this.handleRefunded(data);
            break;

          case PaymentTransactionStatus.NOT_FOUND:
            if (
              data.paymentStatus == BillStatus.PENDING ||
              data.paymentStatus == BillStatus.PROCESSING
            ) {
              this.handleNotFound(data, payload.transaction);
            }
            break;

          default:
            break;
        }
      }
      ack();
    } catch (e) {
      console.log(e);
      if (String(e?.message).includes('already exist')) {
        ack();
        return;
      }

      reject();
    }
  }

  private async handleSuccess(data: Bill) {
    const update = { paymentStatus: BillStatus.SUCCESS };
    if (data.walletType) {
      update['walletType'] = data.walletType;
    }
    await this.billRepository.update({ id: data.id }, update);

    await this.paymentService.finalizePayment(data);
  }

  private async handleFailed(data: Bill, error: object) {
    const update = {
      paymentStatus: BillStatus.FAILED,
      status: BillStatus.FAILED,
      errors: error,
    };
    if (data.walletType) {
      update['walletType'] = data.walletType;
    }
    await this.billRepository.update({ id: data.id }, update);

    await this.rmqService.send(Exchanges.NOTIFICATION, {
      key: Events.BILLS_NOTIFICATION,
      data: {
        template: NotificationTemplates.BILL_PAYMENT_FAILED,
        userId: data.user?.userId,
        parameter: {
          amount: data.amount,
          reference: data.reference,
          wallet: data.walletType,
          serviceType: data.serviceCategory,
          customerId: data.beneficiary,
        },
      } as SendNotificationPayload,
    });
  }

  private async handleRefunded(data: Bill) {
    const update = { isRefunded: true };
    if (
      data.status == BillStatus.PENDING ||
      data.status == BillStatus.PROCESSING
    ) {
      update['status'] = BillStatus.FAILED;
    }

    if (
      data.paymentStatus == BillStatus.PROCESSING ||
      data.paymentStatus == BillStatus.PENDING
    ) {
      update['paymentStatus'] = BillStatus.FAILED;
    }
    await this.billRepository.update({ id: data.id }, update);
  }

  private async handleNotFound(data: Bill, transaction: PaymentTransaction) {
    const paymentWalletResponse = { ...data.meta?.paymentWalletResponse };

    if (!paymentWalletResponse[transaction.walletType]) {
      paymentWalletResponse[transaction.walletType] = transaction.status;
    }
    const meta = { ...data.meta, paymentWalletResponse };
    const billAge = Date.now() - data.createdAt.getTime();
    const tenMinutesInMs = 10 * 60 * 1000; // 10 minutes in milliseconds
    if (billAge >= tenMinutesInMs) {
      await this.billRepository.update({ id: data.id }, { meta });
    }

    if (
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CREDIT] ==
        PaymentTransactionStatus.NOT_FOUND &&
      paymentWalletResponse[PaymentTransactionWalletType.CREDPAL_CASH] ==
        PaymentTransactionStatus.NOT_FOUND
    ) {
      if (billAge >= tenMinutesInMs) {
        await this.billRepository.update(
          { id: data.id },
          {
            meta,
            status: BillStatus.ABANDONED,
            paymentStatus: BillStatus.ABANDONED,
          },
        );
      }
    }
  }
}
