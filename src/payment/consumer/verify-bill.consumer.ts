import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { PaymentService } from '../payment.service';
import { Events } from 'src/utils/queue';

@Processor(Events.REQUERY_TRANSACTION)
export class VerifyBillConsumer extends WorkerHost {
  constructor(private readonly paymentService: PaymentService) {
    super();
  }
  private readonly logger = new Logger(VerifyBillConsumer.name);

  async process(job: Job) {
    try {
      this.logger.debug(
        `processing job for ${Events.REQUERY_TRANSACTION}, `,
        job.asJSON(),
      );
      const data: { reference: string } = job.data;

      this.paymentService.requery(data.reference);
    } catch (error) {
      console.log(error);
    }
  }
}
