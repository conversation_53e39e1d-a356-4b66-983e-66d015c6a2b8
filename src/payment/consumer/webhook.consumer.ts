import { Injectable, OnModuleInit } from '@nestjs/common';
import { Events, Exchanges } from 'src/utils/queue';
import { RabbitmqService } from '@crednet/utils';
import { Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';

@Injectable()
export class <PERSON>hookConsumer implements OnModuleInit {
  constructor(
    private readonly rmqService: RabbitmqService,
    @InjectQueue(Events.REQUERY_TRANSACTION)
    private readonly requeryTransactionQueue: Queue,
  ) {}

  onModuleInit() {
    this.rmqService.subscribe(
      `${Exchanges.WEBHOOK}.${Events.FLUTTERWAVE_BILLS_EVENT}`,
      async ({ data, ack }) => {
        console.log(data);
        try {
          if (data['customer_reference']) {
            this.requeryTransactionQueue.add(
              'requery_from_webhook',
              {
                reference: data['customer_reference'],
              },
              {
                removeOnComplete: true,
                removeOnFail: false,
                backoff: { type: 'exponential', delay: 2000 },
              },
            );
            ack();
          } else {
            console.log('customer_reference not found');
            return;
          }
        } catch (e) {
          console.log(e);
          //force the error to be thrown so that rabbitmq can nack it in the rabbitmq service
          throw e;
        }
      },
    );
  }
}
