import { Test, TestingModule } from '@nestjs/testing';
import { PaymentCron } from './payment.cron';
import { PaymentService } from './payment.service';

describe('PaymentCron', () => {
  let service: PaymentCron;
  let paymentService: PaymentService;

  const mockPaymentService = {
    requeryProcesing: jest.fn(),
    reprocessPendingProcesing: jest.fn(),
    requeryPendingPayment: jest.fn(),
    handleRefund: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentCron,
        {
          provide: PaymentService,
          useValue: mockPaymentService,
        },
      ],
    }).compile();

    service = module.get<PaymentCron>(PaymentCron);
    paymentService = module.get<PaymentService>(PaymentService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('requeryProcessing', () => {
    it('should call payment service to requery processing transactions', async () => {
      await service.requeryProcesing();
      expect(mockPaymentService.requeryProcesing).toHaveBeenCalledWith(1);
    });
  });

  describe('reprocessPendingProcessing', () => {
    it('should call payment service to reprocess pending transactions', async () => {
      await service.reprocessPendingProcesing();
      expect(mockPaymentService.reprocessPendingProcesing).toHaveBeenCalledWith(1);
    });
  });

  describe('requeryPendingPayment', () => {
    it('should call payment service to requery pending payments', async () => {
      await service.requeryPendingPayment();
      expect(mockPaymentService.requeryPendingPayment).toHaveBeenCalledWith(1);
    });
  });

  describe('handleRefund', () => {
    it('should call payment service to handle refunds', async () => {
      await service.handleRefund();
      expect(mockPaymentService.handleRefund).toHaveBeenCalledWith(1);
    });
  });
}); 