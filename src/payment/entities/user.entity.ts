import { Entity, Column, Index, OneToMany } from 'typeorm';
import { Bill } from './bill.entity';
import { BaseEntity } from 'src/config/repository/base-entity';

@Entity('users')
export class User extends BaseEntity {
  @Column({ nullable: false })
  userId: string;

  @Column({ nullable: true })
  firstName: string;

  @Column({ nullable: true })
  lastName: string;

  @Column({  nullable: false })
  email: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @OneToMany(() => Bill, (bill) => bill.user)
  bills: Bill[];
}
