import { Entity, Column, Index, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm';
import { BaseEntity } from 'src/config/repository/base-entity';
import { BillerCategory, categories } from '@app/baxi/baxi.interface';
import { User } from 'src/payment/entities/user.entity';
import { WalletType } from '../dto/purchase-bill.dto';
import { Providers } from 'src/provider/entities/provider.entity';

export enum BillStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed',
  ABANDONED = 'abandoned',
}

@Entity('bills')
// @Index(['reference'], { unique: true })
@Index(['status', 'reference', 'serviceCategory', 'amount'])
export class Bill extends BaseEntity {
  @Column({ unique: true })
  reference: string;

  @Column({ type: 'enum', enum: Providers, default: Providers.BAXI })
  provider: Providers;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  beneficiary: string;

  @Column({ type: 'enum', enum: categories, nullable: true })
  serviceCategory: BillerCategory;

  @Column({
    type: 'enum',
    enum: [WalletType.cash, WalletType.credit],
    nullable: true,
  })
  walletType: WalletType;

  @Column({ type: 'float', nullable: true })
  amount: number;

  @Column({ type: 'json', nullable: true })
  request: any; // This could also be a union type if extended for other request types

  @ManyToOne(() => User, (user) => user.bills, { nullable: false })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'json', nullable: true })
  meta: Record<string, any>;

  @Column({ type: 'enum', enum: BillStatus, default: BillStatus.PENDING })
  status: BillStatus;

  @Column({ type: 'enum', enum: BillStatus, default: BillStatus.PENDING })
  paymentStatus: BillStatus;

  @Column({ type: 'json', nullable: true })
  errors: Record<string, any>;

  @Column({ default: false })
  isRefunded: boolean;
}
