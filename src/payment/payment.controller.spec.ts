import { Test, TestingModule } from '@nestjs/testing';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { BillRepository } from './repository/bill.repository';
import { UserRepository } from './repository/user.repository';
import { DataBundleRepository } from '../provider/repository/data-bundle.repository';
import { CableBundleRepository } from '../provider/repository/cable-bundle.repository';
import { BaxiService } from '@app/baxi';
import { FlutterwaveService } from '@app/flutterwave';
import { ConfigurationService } from '../configuration/configuration.service';
import { BillerRepository } from '../provider/repository/billers.repository';
import { PaymentCacheService } from '@crednet/utils';
import { RabbitmqService } from '@crednet/utils';
import { Queue } from 'bullmq';
import { Events } from '../utils/queue';

describe('PaymentController', () => {
  let controller: PaymentController;
  let paymentService: PaymentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PaymentController],
      providers: [
        PaymentService,
        {
          provide: BillRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            findMany: jest.fn(),
            update: jest.fn(),
            findOneBy: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: {
            getOrCreateUser: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: DataBundleRepository,
          useValue: {
            findOne: jest.fn(),
            findOneBy: jest.fn(),
          },
        },
        {
          provide: CableBundleRepository,
          useValue: {
            findOne: jest.fn(),
            findOneBy: jest.fn(),
          },
        },
        {
          provide: BaxiService,
          useValue: {
            purchase: jest.fn(),
            requery: jest.fn(),
          },
        },
        {
          provide: FlutterwaveService,
          useValue: {
            purchaseBill: jest.fn(),
            verifyTransaction: jest.fn(),
          },
        },
        {
          provide: ConfigurationService,
          useValue: {
            getConfig: jest.fn(),
          },
        },
        {
          provide: BillerRepository,
          useValue: {
            findOneBy: jest.fn(),
          },
        },
        {
          provide: PaymentCacheService,
          useValue: {
            savePayment: jest.fn(),
          },
        },
        {
          provide: RabbitmqService,
          useValue: {
            send: jest.fn(),
          },
        },
        {
          provide: `BullQueue_${Events.REQUERY_TRANSACTION}`,
          useValue: {
            add: jest.fn(),
          } as unknown as Queue,
        },
      ],
    }).compile();

    controller = module.get<PaymentController>(PaymentController);
    paymentService = module.get<PaymentService>(PaymentService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
