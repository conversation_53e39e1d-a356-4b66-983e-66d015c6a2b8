import { Test, TestingModule } from '@nestjs/testing';
import { ProviderService } from './provider.service';
import { BaxiService } from '@app/baxi';
import { BillerRepository } from './repository/billers.repository';
import { DataBundleRepository } from './repository/data-bundle.repository';
import { CableBundleRepository } from './repository/cable-bundle.repository';
import { FlutterwaveService } from '@app/flutterwave';
import { ConfigurationService } from '../configuration/configuration.service';

describe('ProviderService', () => {
  let service: ProviderService;
  let baxiService: BaxiService;
  let billerRepository: BillerRepository;
  let dataBundleRepository: DataBundleRepository;
  let cableBundleRepository: CableBundleRepository;
  let flutterwaveService: FlutterwaveService;
  let configurationService: ConfigurationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProviderService,
        {
          provide: BaxiService,
          useValue: {
            getBillers: jest.fn(),
            getDataBundles: jest.fn(),
            getCableBundles: jest.fn(),
          },
        },
        {
          provide: BillerRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            find: jest.fn(),
            findOneBy: jest.fn(),
            findMany: jest.fn(),
          },
        },
        {
          provide: DataBundleRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
          },
        },
        {
          provide: CableBundleRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
          },
        },
        {
          provide: FlutterwaveService,
          useValue: {
            getBillers: jest.fn(),
            getDataBundles: jest.fn(),
            getCableBundles: jest.fn(),
          },
        },
        {
          provide: ConfigurationService,
          useValue: {
            getConfig: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ProviderService>(ProviderService);
    baxiService = module.get<BaxiService>(BaxiService);
    billerRepository = module.get<BillerRepository>(BillerRepository);
    dataBundleRepository = module.get<DataBundleRepository>(DataBundleRepository);
    cableBundleRepository = module.get<CableBundleRepository>(CableBundleRepository);
    flutterwaveService = module.get<FlutterwaveService>(FlutterwaveService);
    configurationService = module.get<ConfigurationService>(ConfigurationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
