import { Module } from '@nestjs/common';
import { ProviderService } from './provider.service';
import { ProviderController } from './provider.controller';
import { BaxiModule } from '@app/baxi';
import { BillerRepository } from './repository/billers.repository';
import { ProviderCron } from './provider.cron';
import { DataBundleRepository } from './repository/data-bundle.repository';
import { CableBundleRepository } from './repository/cable-bundle.repository';
import { FlutterwaveModule } from '@app/flutterwave';
import { CreditSwitchModule } from '@app/credit-switch';
import config from 'src/config';
import { QuickTellerModule } from '@app/quickteller';

@Module({
  imports: [
    BaxiModule,
    FlutterwaveModule,
    CreditSwitchModule,
    QuickTellerModule,
  ],
  controllers: [ProviderController],
  providers: [
    ProviderService,
    BillerRepository,
    DataBundleRepository,
    ...(config.isCronEnabled
      ? [ProviderCron, CableBundleRepository]
      : [CableBundleRepository]),
  ],
})
export class ProviderModule {}
