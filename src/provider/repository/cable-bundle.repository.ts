import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { CableBundle } from '../entities/cable-bundle.entity';

@Injectable()
export class CableBundleRepository extends TypeOrmRepository<CableBundle> {
  constructor(private readonly dataSource: DataSource) {
    super(CableBundle, dataSource.createEntityManager());
  }
}

// @Injectable()
// export class CablePriceOptionRepository extends TypeOrmRepository<CablePriceOption> {
//   constructor(private readonly dataSource: DataSource) {
//     super(CablePriceOption, dataSource.createEntityManager());
//   }
// }
