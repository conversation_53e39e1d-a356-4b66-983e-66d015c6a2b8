import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { DataBundle } from '../entities/data-bundle.enity';

@Injectable()
export class DataBundleRepository extends TypeOrmRepository<DataBundle> {
  constructor(private readonly dataSource: DataSource) {
    super(DataBundle, dataSource.createEntityManager());
  }
}
