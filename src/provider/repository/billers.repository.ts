import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { Biller } from '../entities/billers.entity';

@Injectable()
export class BillerRepository extends TypeOrmRepository<Biller> {
  constructor(private readonly dataSource: DataSource) {
    super(Biller, dataSource.createEntityManager());
  }
}
