import { BaseEntity } from 'src/config/repository/base-entity';
import { Entity, Column, Index } from 'typeorm';
import { Providers } from './provider.entity';

@Entity('data_bundles')
@Index(['serviceType'])
@Index(['provider'])
@Index(['datacode'])
export class DataBundle extends BaseEntity {
  @Column({ nullable: true })
  name: string;

  @Column({ type: 'float', nullable: true })
  price: number;

  @Column({ nullable: true })
  datacode: string;

  @Column({ nullable: true })
  validity: string;

  @Column({ nullable: true })
  allowance: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: false })
  serviceType: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'enum', enum: Providers, default: Providers.BAXI })
  provider: Providers;
}
