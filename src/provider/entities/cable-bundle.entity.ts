import { BaseEntity } from 'src/config/repository/base-entity';
import { Entity, Column, Index } from 'typeorm';
import { Providers } from './provider.entity';

@Entity('cable_bundles')
@Index(['serviceType'])
export class CableBundle extends BaseEntity {
  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  serviceType: string;

  @Column({ type: 'float' })
  price: number;

  @Column({ unique: true })
  code: string;

  @Column({ nullable: true })
  codeForCreditSwitch: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  validity: string;

  @Column({ nullable: true })
  regExpression: string;

  @Column({ type: 'enum', enum: Providers, default: Providers.BAXI })
  provider: Providers;

  @Column({ type: 'float', nullable: true })
  fee: number;
}

// @Entity('cable_price_options')
// export class CablePriceOption extends BaseEntity {
//   @Column({ unique: true })
//   _id: string;

//   @Column({ type: 'int', nullable: true })
//   monthsPaidFor: number;

//   @Column({ type: 'float', nullable: true })
//   price: number;

//   @Column({ type: 'int', nullable: true })
//   invoicePeriod: number;

//   @ManyToOne(() => CableBundle, (bundle) => bundle.availablePricingOptions)
//   bundle: CableBundle;
// }
