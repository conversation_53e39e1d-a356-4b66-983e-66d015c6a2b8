import { BaseEntity } from 'src/config/repository/base-entity';
import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';
import { Providers } from './provider.entity';

@Entity('billers')
@Index(['serviceType'])
@Index(['serviceCode', 'serviceType'])
@Index(['serviceCategory'])
export class Biller extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column({ type: 'enum', enum: Providers, default: Providers.BAXI })
  provider: Providers;

  @Column({ nullable: false })
  serviceType: string;

  @Column({ nullable: true })
  serviceId?: string;

  @Column({ nullable: true })
  serviceCode?: string;

  @Column({ nullable: false })
  serviceCategory: string;

  @Column({ type: 'int', nullable: true })
  billerId?: number;

  @Column({ nullable: true })
  serviceBiller?: string;

  @Column({ nullable: true })
  serviceDescription?: string;

  @Column({ nullable: true })
  serviceHandler?: string;

  @Column({ nullable: true })
  serviceProvider?: string;

  @Column({ type: 'boolean', nullable: true })
  serviceEnabled?: boolean;

  @Column({ nullable: true })
  serviceStatus?: string;

  @Column({ nullable: true })
  serviceLogo?: string;

  @Column({ type: 'boolean', nullable: true })
  deployed?: boolean;

  @Column({ nullable: true })
  serviceName?: string;

  @Column({ type: 'boolean', default: true })
  enabled?: boolean;

  @Column({ default: 1 })
  priority?: number;

  @Column({ nullable: true })
  coverImage?: string;

  @Column({ nullable: true })
  imageHash?: string;
}
