import { Injectable } from '@nestjs/common';
import { BaxiService } from '@app/baxi';
import { BillerRepository } from './repository/billers.repository';
import { BillerCategory } from '@app/baxi/baxi.interface';
import { CableBundleRepository } from './repository/cable-bundle.repository';
import { DataBundleRepository } from './repository/data-bundle.repository';
import { DataBundle } from './entities/data-bundle.enity';
import { FlutterwaveService } from '@app/flutterwave';
import { CreditSwitchService } from '@app/credit-switch';
import { Providers } from './entities/provider.entity';
import { ConfigurationService } from 'src/configuration/configuration.service';
import { QuickTellerService } from '@app/quickteller';
import { QuickTellerBillerCategory } from '@app/quickteller/quickteller.interface';
import { creditSwitchData } from './providers.constants';
import { CableBundle } from './entities/cable-bundle.entity';

@Injectable()
export class ProviderService {
  constructor(
    private readonly baxiService: BaxiService,
    private readonly billerRepository: BillerRepository,
    private readonly dataBundleRepository: DataBundleRepository,
    // private readonly cablePriceOptionRepository: CablePriceOptionRepository,
    private readonly cableBundleRepository: CableBundleRepository,
    private readonly flutterwaveService: FlutterwaveService,
    private readonly creditSwitchService: CreditSwitchService,
    private readonly quickTellerService: QuickTellerService,
    private readonly configurationService: ConfigurationService,
  ) {}

  async getToken() {
    await this.quickTellerService.getAccessToken();
  }

  async seed(category: BillerCategory) {
    try {
      const billers = await this.baxiService.getBillers(category);
      console.log(billers);
      for (const biller of billers) {
        console.log(biller.serviceType);
        if (
          await this.billerRepository.exists({
            where: {
              serviceType: biller.serviceType,
              serviceCategory: category,
              provider: Providers.BAXI,
            },
          })
        ) {
          await this.billerRepository.update(
            {
              serviceType: biller.serviceType,
              serviceCategory: biller.serviceCategory,
              provider: Providers.BAXI,
            },
            {
              serviceEnabled: biller.serviceEnabled == 'true',
            },
          );
        } else {
          delete biller.id;
          await this.billerRepository.insert({
            serviceType: biller.serviceType,
            provider: Providers.BAXI,
            serviceCategory: biller.serviceCategory,
            serviceEnabled: biller.serviceEnabled == 'true',
            serviceBiller: biller.serviceBiller,
            serviceDescription: biller.serviceDescription,
            serviceHandler: biller.serviceHandler,
            serviceLogo: biller.serviceLogo,
            serviceProvider: biller.serviceProvider,
            serviceStatus: biller.serviceStatus,
            billerId: biller.biller_id,
            deployed: biller.deployed == 'true',
            serviceName: biller.serviceName,
            serviceId: biller.serviceId,
            serviceCode: biller.serviceCode,
            enabled: false,
          });
        }
      }
    } catch (error) {
      console.log(error);
    }
  }

  async seedQuickTeller(category: BillerCategory) {
    console.log('seedQuickTeller', category);
    try {
      const categories = await this.quickTellerService.getBillerCategories();
      const categoryId = categories.find(
        (categoryItem) =>
          categoryItem.Name.toLowerCase() ==
          QuickTellerBillerCategory[category].toLowerCase(),
      )?.Id;

      if (!categoryId) return;

      const billers = await this.quickTellerService.getBillers(categoryId);

      for (const biller of billers) {
        console.log(biller);
        if (
          await this.billerRepository.exists({
            where: {
              serviceType: biller.ShortName,
              serviceCategory: category,
              provider: Providers.QUICKTELLER,
            },
          })
        ) {
          // await this.billerRepository.update(
          //   {
          //     serviceType: biller.ShortName,
          //     serviceCategory: category,
          //     provider: Providers.QUICKTELLER,
          //   },
          //   {
          //     serviceEnabled: false,
          //   },
          // );
        } else {
          // delete biller.id;
          await this.billerRepository.insert({
            provider: Providers.QUICKTELLER,
            serviceType: biller.ShortName,
            serviceCategory: category,
            serviceEnabled: false,
            serviceBiller: biller.name,
            serviceDescription: biller.description,
            serviceHandler: biller.ShortName,
            serviceLogo: biller.LogoUrl,
            serviceProvider: biller.ShortName,
            // serviceStatus: biller.serviceStatus,
            // billerId: biller.short_name,
            deployed: true,
            serviceName: biller.Name,
            // serviceId: biller.serviceId,
            serviceCode: biller.Id,
            enabled: false,
          });
        }
      }
    } catch (error) {
      console.log(error);
    }
  }

  async seedFlutterwave(category: BillerCategory) {
    try {
      const billers = await this.flutterwaveService.getBillers(category);
      console.log(billers);
      for (const biller of billers) {
        if (
          await this.billerRepository.exists({
            where: {
              serviceType: biller.short_name,
              serviceCategory: category,
              provider: Providers.FLUTERWAVE,
            },
          })
        ) {
          // await this.billerRepository.update(
          //   {
          //     serviceType: biller.short_name,
          //     serviceCategory: category,
          //     provider: Providers.FLUTERWAVE,
          //   },
          //   {
          //     // serviceEnabled: false,
          //   },
          // );
        } else {
          delete biller.id;
          await this.billerRepository.insert({
            provider: Providers.FLUTERWAVE,
            serviceType: biller.short_name,
            serviceCategory: category,
            serviceEnabled: false,
            serviceBiller: biller.name,
            serviceDescription: biller.description,
            serviceHandler: biller.short_name,
            serviceLogo: biller.logo,
            serviceProvider: biller.short_name,
            // serviceStatus: biller.serviceStatus,
            // billerId: biller.short_name,
            deployed: true,
            serviceName: biller.name,
            // serviceId: biller.serviceId,
            serviceCode: biller.biller_code,
            enabled: false,
          });
        }
      }
    } catch (error) {
      console.log(error);
    }
  }

  async seedCreditSwitch() {
    try {
      for (const biller of creditSwitchData) {
        if (
          !(await this.billerRepository.exists({
            where: { serviceId: biller.serviceId },
          }))
        ) {
          await this.billerRepository.insert(biller);
        } else {
        }
      }
    } catch (error) {
      console.error(error);
    }
  }

  async seedDataBundle() {
    const items = await this.billerRepository.find({
      where: {
        serviceCategory: 'databundle',
        provider: Providers.BAXI,
      },
    });

    for (const item of items) {
      try {
        const bundles = await this.baxiService.getDataBundles(item.serviceType);
        // console.log(item);
        if (bundles.length > 2) {
          await this.dataBundleRepository.delete({
            serviceType: item.serviceType,
            provider: Providers.BAXI,
          });
        }
        for (const bundle of bundles) {
          console.log(bundle.datacode);
          try {
            if (
              !(await this.dataBundleRepository.exists({
                where: {
                  datacode: bundle.datacode,
                  serviceType: item.serviceType,
                },
              }))
            ) {
              console.log('new ', bundle);

              await this.dataBundleRepository.save({
                ...bundle,
                serviceType: item.serviceType,
              });
            } else {
              console.log('exists ', bundle);
              await this.dataBundleRepository.update(
                {
                  datacode: bundle.datacode,
                  serviceType: item.serviceType,
                },
                {
                  price: bundle.price,
                },
              );
            }
          } catch (error) {
            console.log(error);
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  async seedFlutterwaveDataBundle() {
    const items = await this.billerRepository.find({
      where: {
        serviceCategory: 'databundle',
        provider: Providers.FLUTERWAVE,
      },
    });

    for (const item of items) {
      console.log(item);
      try {
        if (
          item.serviceType == 'dstvshowmax' ||
          item.provider != Providers.FLUTERWAVE
        )
          continue;
        const bundles = await this.flutterwaveService.getBillerItems(
          item.serviceCode,
        );
        console.log(bundles);
        const serviceType = item.serviceType?.toLowerCase()?.split(' ')[0];
        if (bundles.length > 2) {
          await this.dataBundleRepository.delete({
            serviceType,
            provider: Providers.FLUTERWAVE,
          });
        }
        for (const bundle of bundles) {
          try {
            if (
              !(await this.dataBundleRepository.exists({
                where: {
                  datacode: bundle.item_code,
                  serviceType,
                  provider: Providers.FLUTERWAVE,
                },
              }))
            ) {
              // console.log('new ', bundle);

              await this.dataBundleRepository.save({
                price: bundle.amount,
                datacode: bundle.item_code,
                name: bundle.name,
                allowance: bundle.name,
                serviceType,
                provider: Providers.FLUTERWAVE,
                validity: bundle.validity_period,
              });
            } else {
              // console.log('exists ', bundle);
              await this.dataBundleRepository.update(
                {
                  datacode: bundle.item_code,
                  serviceType,
                  provider: Providers.FLUTERWAVE,
                },
                {
                  price: bundle.amount,
                },
              );
            }
          } catch (error) {
            console.log(error);
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  async seedFlutterwaveCableBundle() {
    const items = await this.billerRepository.find({
      where: {
        serviceCategory: 'cabletv',
        provider: Providers.FLUTERWAVE,
      },
    });

    for (const item of items) {
      try {
        const bundles = await this.flutterwaveService.getBillerItems(
          item.serviceCode,
        );

        for (const bundle of bundles) {
          console.log(bundle);
          try {
            let data = await this.cableBundleRepository.findOne({
              where: {
                code: bundle.item_code,
                serviceType: bundle.biller_code,
                provider: Providers.FLUTERWAVE,
              },
            });

            if (!data) {
              data = await this.cableBundleRepository.save({
                code: bundle.item_code,
                serviceType: bundle.biller_code,
                name: bundle.name,
                price: bundle.amount,
                fee: bundle.fee,
                regExpression: bundle.reg_expression,
                provider: Providers.FLUTERWAVE,
                validity: bundle.validity_period,
                description: bundle.biller_name,
              });
            } else {
              await this.cableBundleRepository.update(
                { id: data.id },
                {
                  price: bundle.amount,
                  fee: bundle.fee,
                  validity: bundle.validity_period,
                },
              );
            }
          } catch (error) {
            console.log(error);
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  async seedCableBundle() {
    const items = await this.billerRepository.find({
      where: {
        serviceCategory: 'cabletv',
        provider: Providers.BAXI,
      },
    });

    for (const item of items) {
      try {
        const bundles = await this.baxiService.getCableBundles(
          item.serviceType,
        );

        for (const bundle of bundles) {
          for (const d of bundle.availablePricingOptions) {
            try {
              let data = await this.cableBundleRepository.findOne({
                where: {
                  code: `${bundle.code}::${d.monthsPaidFor}`,
                  serviceType: item.serviceCode,
                  // validity: d.monthsPaidFor + '',
                  provider: Providers.BAXI,
                },
              });

              if (!data) {
                data = await this.cableBundleRepository.save({
                  code: `${bundle.code}::${d.monthsPaidFor}`,
                  serviceType: item.serviceCode,
                  name: bundle.name,
                  validity: d.monthsPaidFor + '',
                  price: d.price,
                  fee: 0,
                  provider: Providers.BAXI,
                  description: bundle.name,
                });
              } else {
                await this.cableBundleRepository.update(
                  { id: data.id },
                  {
                    price: d.price,
                    fee: 0,
                  },
                );
              }
            } catch (error) {
              console.log(error);
            }
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  async findAll() {
    const data = await this.billerRepository.find({
      where: {
        serviceEnabled: true,
        enabled: true,
      },
    });

    const cableProvider = await this.configurationService.getConfig(
      ConfigurationService.CABLE_PROVIDER,
    );

    return data.filter(
      (item) =>
        item.serviceCategory != 'cabletv' || cableProvider == item.provider,
    );
  }

  async findByCategory(category: BillerCategory) {
    if (category == 'cabletv') {
      const provider = (await this.configurationService.getConfig(
        ConfigurationService.CABLE_PROVIDER,
      )) as Providers;
      return this.billerRepository.find({
        where: {
          serviceCategory: category,
          enabled: true,
          provider: provider,
        },
      });
    }
    return this.billerRepository.find({
      where: { serviceCategory: category, enabled: true },
    });
  }

  async databundles(serviceType: string) {
    const provider = (await this.configurationService.getConfig(
      ConfigurationService.DATA_PROVIDER,
    )) as Providers;
    return this.dataBundleRepository.find({
      where: {
        serviceType,
        provider: provider,
      },
    });
  }

  async seedQuickTellerDataBundle() {
    const items = await this.billerRepository.find({
      where: {
        serviceCategory: 'databundle',
        provider: Providers.QUICKTELLER,
      },
    });

    for (const item of items) {
      console.log(item);
      try {
        if (item.provider != Providers.QUICKTELLER) continue;

        const bundles = await this.quickTellerService.getBillerItems(
          item.serviceCode,
        );
        for (const bundle of bundles) {
          try {
            const serviceType = item.serviceType?.toLowerCase()?.split(' ')[0];
            if (
              !(await this.dataBundleRepository.exists({
                where: {
                  datacode: bundle.PaymentCode,
                  serviceType,
                  provider: Providers.QUICKTELLER,
                },
              }))
            ) {
              // console.log('new ', bundle);

              await this.dataBundleRepository.insert({
                price: bundle.Amount,
                datacode: bundle.PaymentCode,
                name: bundle.Name,
                allowance: bundle.Name,
                serviceType,
                provider: Providers.QUICKTELLER,
                validity:
                  bundle.Name.includes('(') && bundle.Name.includes(')')
                    ? bundle.Name.split('(')[1].split(')')[0]
                    : '',
              });
            } else {
              // console.log('exists ', bundle);
              await this.dataBundleRepository.update(
                {
                  datacode: bundle.PaymentCode,
                  serviceType,
                  provider: Providers.QUICKTELLER,
                },
                {
                  price: bundle.Amount,
                },
              );
            }
          } catch (error) {
            console.log(error);
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  async seedQuickTellerCableBundle() {
    const items = await this.billerRepository.find({
      where: {
        serviceCategory: 'cabletv',
        provider: Providers.QUICKTELLER,
      },
    });

    for (const item of items) {
      console.log(item);
      try {
        const bundles = await this.quickTellerService.getBillerItems(
          item.serviceCode,
        );
        const serviceType = item.serviceType?.toLowerCase()?.split(' ')[0];

        for (const bundle of bundles) {
          try {
            const data = await this.cableBundleRepository.findOne({
              where: {
                code: bundle.PaymentCode,
                serviceType,
                provider: Providers.QUICKTELLER,
              },
            });

            if (!data) {
              await this.cableBundleRepository.insert({
                code: bundle.PaymentCode,
                serviceType,
                name: bundle.Name,
                price: bundle.Amount,
                fee: bundle.ItemFee,
                regExpression: bundle.ItemCurrencySymbol,
                provider: Providers.QUICKTELLER,
                validity: '1',
                description: bundle.BillerName,
              });
            } else {
              await this.cableBundleRepository.update(
                { id: data.id },
                {
                  price: bundle.Amount,
                  fee: bundle.ItemFee,
                  validity: '1',
                },
              );
            }
          } catch (error) {
            console.log(error);
          }
        }
      } catch (error) {
        console.log(error);
      }
    }
  }

  async cableBundles(serviceCode: string) {
    const biller = await this.billerRepository.findOneBy({
      serviceCode,
    });
    if (!biller) {
      return [];
    }

    return this.cableBundleRepository.find({
      where: { serviceType: serviceCode },
    });
  }

  lookup(serviceType: string, accountNumber: string) {
    return this.baxiService.lookup(serviceType, accountNumber);
  }

  lookupFlutterwave(
    accountNumber: string,
    itemCode: string,
    billerCode: string,
  ) {
    return this.flutterwaveService.lookup(itemCode, billerCode, accountNumber);
  }

  lookupQuickTeller(accountNumber: string, paymentCode: string) {
    return this.quickTellerService.lookup(paymentCode, accountNumber);
  }

  async lookupCreditSwitch(serviceType: string, accountNumber: string) {
    // Credit Switch doesn't have a separate lookup endpoint for airtime/data
    // For electricity, we would use the validation endpoint
    if (
      serviceType.includes('electricity') ||
      serviceType.includes('electric')
    ) {
      const serviceId =
        this.creditSwitchService.getElectricityServiceId(serviceType);
      return await this.creditSwitchService.validateElectricityMeter(
        serviceId,
        accountNumber,
      );
    }

    // For airtime/data, return basic validation
    return {
      statusCode: '00',
      statusDescription: 'Valid number',
      recipient: accountNumber,
    };
  }

  async getCreditSwitchMerchantInfo() {
    return await this.creditSwitchService.getMerchantInfo();
  }

  async lookupCreditSwitchPhoneNumber(phoneNumber: string) {
    // Format phone number to 11 digits (Nigerian format)
    let formattedNumber = phoneNumber;
    if (phoneNumber.startsWith('+234')) {
      formattedNumber = '0' + phoneNumber.substring(4);
    } else if (phoneNumber.startsWith('234')) {
      formattedNumber = '0' + phoneNumber.substring(3);
    }

    // Ensure it's 11 digits
    if (formattedNumber.length !== 11 || !formattedNumber.startsWith('0')) {
      throw new Error(
        'Invalid Nigerian phone number format. Must be 11 digits starting with 0.',
      );
    }

    return await this.creditSwitchService.lookupPhoneNumber(formattedNumber);
  }

  /**
   * Seed data plans for a specific network
   */
  async seedNetworkDataPlansForCreditSwitch(
    serviceId: string,
    network: string,
    retryCount = 3,
  ) {
    try {
      console.log(`Seeding Credit Switch data plans for ${network}...`);

      const response = await this.creditSwitchService.getDataPlans(serviceId);

      if (
        !this.creditSwitchService.isTransactionSuccessful(response.statusCode)
      ) {
        console.error(
          `Failed to fetch data plans for ${network}:`,
          response.statusDescription,
        );
        return;
      }

      if (!response.dataPlan || response.dataPlan.length === 0) {
        console.log(`No data plans found for ${network}`);
        return;
      }

      if (
        await this.dataBundleRepository.exists({
          where: {
            provider: Providers.CREDIT_SWITCH,
            serviceType: network,
          },
        })
      ) {
        try {
          await this.dataBundleRepository.delete({
            provider: Providers.CREDIT_SWITCH,
            serviceType: network,
          });
        } catch (error) {
          if (error?.code === 'ER_LOCK_DEADLOCK' && retryCount > 0) {
            console.log(
              `Deadlock detected for ${network}, retrying... (${retryCount} attempts left)`,
            );
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 1000),
            );
            return this.seedNetworkDataPlansForCreditSwitch(
              serviceId,
              network,
              retryCount - 1,
            );
          }
          throw error;
        }
      }

      const dataBundles: Partial<DataBundle>[] = response.dataPlan.map(
        (plan) => ({
          provider: Providers.CREDIT_SWITCH,
          serviceType: network,
          name: plan.databundle,
          price: plan.amount,
          allowance: plan.databundle,
          validity: plan.validity,
          datacode: plan.productId,
          description: `${plan.databundle} - Valid for ${plan.validity}`,
          isActive: true,
        }),
      );

      await this.dataBundleRepository.save(dataBundles);

      console.log(
        `Successfully seeded ${dataBundles.length} data plans for ${network}`,
      );
    } catch (error) {
      console.error(`Error seeding data plans for ${network}:`, error);
    }
  }

  /**
   * Get Credit Switch data plans for a specific network
   */
  async getCreditSwitchDataPlansForNetwork(
    network: string,
  ): Promise<DataBundle[]> {
    return await this.dataBundleRepository.find({
      where: {
        provider: Providers.CREDIT_SWITCH,
        serviceType: network,
        isActive: true,
      },
      order: {
        price: 'ASC',
      },
    });
  }

  /**
   * Get all Credit Switch data plans
   */
  async getCreditSwitchDataPlans(network?: string) {
    if (network) {
      return await this.getCreditSwitchDataPlansForNetwork(network);
    }

    return await this.dataBundleRepository.find({
      where: {
        provider: Providers.CREDIT_SWITCH,
        isActive: true,
      },
      order: {
        serviceType: 'ASC',
        price: 'ASC',
      },
    });
  }

  /**
   * Update data plan status
   */
  async updateCreditSwitchDataPlanStatus(
    productId: string,
    isActive: boolean,
  ): Promise<void> {
    await this.dataBundleRepository.update(
      {
        provider: Providers.CREDIT_SWITCH,
        datacode: productId,
      },
      {
        isActive,
        updatedAt: new Date(),
      },
    );
  }

  /**
   * Get data plan by product ID
   */
  async getCreditSwitchDataPlanByProductId(
    productId: string,
  ): Promise<DataBundle | null> {
    return await this.dataBundleRepository.findOne({
      where: {
        provider: Providers.CREDIT_SWITCH,
        datacode: productId,
        isActive: true,
      },
    });
  }

  /**
   * Search data plans by criteria
   */
  async searchCreditSwitchDataPlans(criteria: {
    network?: string;
    minAmount?: number;
    maxAmount?: number;
    validity?: string;
  }): Promise<DataBundle[]> {
    const queryBuilder = this.dataBundleRepository
      .createQueryBuilder('dataBundle')
      .where('dataBundle.provider = :provider', {
        provider: Providers.CREDIT_SWITCH,
      })
      .andWhere('dataBundle.isActive = :isActive', { isActive: true });

    if (criteria.network) {
      queryBuilder.andWhere('dataBundle.serviceType = :serviceType', {
        serviceType: criteria.network,
      });
    }

    if (criteria.minAmount) {
      queryBuilder.andWhere('dataBundle.price >= :minAmount', {
        minAmount: criteria.minAmount,
      });
    }

    if (criteria.maxAmount) {
      queryBuilder.andWhere('dataBundle.price <= :maxAmount', {
        maxAmount: criteria.maxAmount,
      });
    }

    if (criteria.validity) {
      queryBuilder.andWhere('dataBundle.validity LIKE :validity', {
        validity: `%${criteria.validity}%`,
      });
    }

    return await queryBuilder
      .orderBy('dataBundle.serviceType', 'ASC')
      .addOrderBy('dataBundle.price', 'ASC')
      .getMany();
  }

  /**
   * Seed Showmax packages
   */
  async seedShowmaxPackagesForCreditSwitch(retryCount = 3) {
    try {
      console.log('Seeding Credit Switch Showmax packages...');

      const response = await this.creditSwitchService.getShowmaxPackages();

      if (
        !this.creditSwitchService.isTransactionSuccessful(response.statusCode)
      ) {
        console.error(
          'Failed to fetch Showmax packages:',
          response.statusDescription,
        );
        return;
      }

      if (
        !response.statusDescription?.items ||
        response.statusDescription.items.length === 0
      ) {
        console.log('No Showmax packages found');
        return;
      }

      if (
        await this.cableBundleRepository.exists({
          where: {
            provider: Providers.CREDIT_SWITCH,
            serviceType: 'showmax',
          },
        })
      ) {
        try {
          await this.cableBundleRepository.delete({
            provider: Providers.CREDIT_SWITCH,
            serviceType: 'showmax',
          });
        } catch (error) {
          if (error?.code === 'ER_LOCK_DEADLOCK' && retryCount > 0) {
            console.log(
              `Deadlock detected for Showmax packages, retrying... (${retryCount} attempts left)`,
            );
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 1000),
            );
            return this.seedShowmaxPackagesForCreditSwitch(retryCount - 1);
          }
          throw error;
        }
      }
      console.log(response.statusDescription.items);

      const cableBundles: Partial<CableBundle>[] =
        response.statusDescription.items.map((item) => ({
          provider: Providers.CREDIT_SWITCH,
          serviceType: 'showmax',
          name: item.name,
          price: item.price,
          code: `${item.type}_${item.subscriptionPeriod}`,
          codeForCreditSwitch: item.type,
          description: `${item.name} - ${item.subscriptionPeriod} months subscription`,
          validity: `${item.subscriptionPeriod}`,
        }));

      await this.cableBundleRepository.save(cableBundles);

      console.log(
        `Successfully seeded ${cableBundles.length} Showmax packages`,
      );
    } catch (error) {
      console.error('Error seeding Showmax packages:', error);
    }
  }

  /**
   * Seed Startimes products
   */
  async seedStartimesProductsForCreditSwitch(retryCount = 3) {
    try {
      console.log('Seeding Credit Switch Startimes products...');

      const response = await this.creditSwitchService.getStartimesProducts();

      if (
        !this.creditSwitchService.isTransactionSuccessful(response.statusCode)
      ) {
        console.error(
          'Failed to fetch Startimes products:',
          response.statusDescription,
        );
        return;
      }

      if (
        !response.statusDescription?.items ||
        response.statusDescription.items.length === 0
      ) {
        console.log('No Startimes products found');
        return;
      }

      if (
        await this.cableBundleRepository.exists({
          where: {
            provider: Providers.CREDIT_SWITCH,
            serviceType: 'startimes',
          },
        })
      ) {
        try {
          await this.cableBundleRepository.delete({
            provider: Providers.CREDIT_SWITCH,
            serviceType: 'startimes',
          });
        } catch (error) {
          if (error?.code === 'ER_LOCK_DEADLOCK' && retryCount > 0) {
            console.log(
              `Deadlock detected for Startimes products, retrying... (${retryCount} attempts left)`,
            );
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 1000),
            );
            return this.seedStartimesProductsForCreditSwitch(retryCount - 1);
          }
          throw error;
        }
      }

      const cableBundles: Partial<CableBundle>[] =
        response.statusDescription.items.map((item) => ({
          provider: Providers.CREDIT_SWITCH,
          serviceType: 'startimes',
          name: item.name,
          code: item.code,
          description: item.description,
          price: Number(item.availablePricingOptions?.[0]?.price) || 0,
        }));

      await this.cableBundleRepository.save(cableBundles);

      console.log(
        `Successfully seeded ${cableBundles.length} Startimes products`,
      );
    } catch (error) {
      console.error('Error seeding Startimes products:', error);
    }
  }

  /**
   * Seed Multichoice products (DSTV or GOTV)
   */
  async seedMultichoiceProductsForCreditSwitch(
    serviceType: 'dstv' | 'gotv',
    retryCount = 3,
  ) {
    try {
      console.log(
        `Seeding Credit Switch ${serviceType.toUpperCase()} products...`,
      );

      const response =
        await this.creditSwitchService.fetchMultiChoiceProducts(serviceType);

      if (
        !this.creditSwitchService.isTransactionSuccessful(response.statusCode)
      ) {
        console.error(
          `Failed to fetch ${serviceType.toUpperCase()} products:`,
          response.statusDescription,
        );
        return;
      }

      if (
        !response.statusDescription?.items ||
        response.statusDescription.items.length === 0
      ) {
        console.log(`No ${serviceType.toUpperCase()} products found`);
        return;
      }

      if (
        await this.cableBundleRepository.exists({
          where: {
            provider: Providers.CREDIT_SWITCH,
            serviceType: serviceType,
          },
        })
      ) {
        try {
          await this.cableBundleRepository.delete({
            provider: Providers.CREDIT_SWITCH,
            serviceType: serviceType,
          });
        } catch (error) {
          if (error?.code === 'ER_LOCK_DEADLOCK' && retryCount > 0) {
            console.log(
              `Deadlock detected for ${serviceType.toUpperCase()} products, retrying... (${retryCount} attempts left)`,
            );
            await new Promise((resolve) =>
              setTimeout(resolve, Math.random() * 1000),
            );
            return this.seedMultichoiceProductsForCreditSwitch(
              serviceType,
              retryCount - 1,
            );
          }
          throw error;
        }
      }

      const bundles = response.statusDescription.items.map((item) => ({
        provider: Providers.CREDIT_SWITCH,
        serviceType: serviceType,
        name: item.name,
        code: `${item.code}`,
        price: Number(item.availablePricingOptions[0].price),
        description: item.description,
        validity: item.availablePricingOptions[0].monthsPaidFor.toString(),
        fee: 100, // note: this can change
      }));

      console.log(bundles.length);

      await this.cableBundleRepository.save(bundles);

      console.log(
        `Successfully seeded ${bundles.length} ${serviceType.toUpperCase()} products`,
      );
    } catch (error) {
      if (error?.code === 'ER_LOCK_DEADLOCK' && retryCount > 0) {
        console.log(
          `Deadlock detected for ${serviceType.toUpperCase()} products, retrying... (${retryCount} attempts left)`,
        );
        await new Promise((resolve) =>
          setTimeout(resolve, Math.random() * 10000),
        );
        return this.seedMultichoiceProductsForCreditSwitch(
          serviceType,
          retryCount - 1,
        );
      }
      throw error;
    }
  }

  async getPriceOfCablePackage(code: string, months: number): Promise<number> {
    const bundle = await this.cableBundleRepository.findOne({
      where: {
        provider: Providers.CREDIT_SWITCH,
        code,
      },
    });

    return bundle.price * months;
  }

  /**
   * Get Credit Switch cable TV packages for a specific service type
   */
  async getCreditSwitchCablePackages(
    serviceType?: string,
  ): Promise<CableBundle[]> {
    const whereCondition: any = {
      provider: Providers.CREDIT_SWITCH,
    };

    if (serviceType) {
      whereCondition.serviceType = serviceType;
    }

    return await this.cableBundleRepository.find({
      where: whereCondition,
      order: {
        serviceType: 'ASC',
        price: 'ASC',
      },
    });
  }

  /**
   * Get Credit Switch cable TV package by code
   */
  async getCreditSwitchCablePackageByCode(
    code: string,
  ): Promise<CableBundle | null> {
    return await this.cableBundleRepository.findOne({
      where: {
        provider: Providers.CREDIT_SWITCH,
        code,
      },
    });
  }

  /**
   * Search Credit Switch cable TV packages by criteria
   */
  async searchCreditSwitchCablePackages(criteria: {
    serviceType?: string;
    minPrice?: number;
    maxPrice?: number;
    name?: string;
    page?: number;
  }): Promise<CableBundle[]> {
    const queryBuilder = this.cableBundleRepository
      .createQueryBuilder('cableBundle')
      .where('cableBundle.provider = :provider', {
        provider: Providers.CREDIT_SWITCH,
      });

    if (criteria.serviceType) {
      queryBuilder.andWhere('cableBundle.serviceType = :serviceType', {
        serviceType: criteria.serviceType,
      });
    }

    if (criteria.minPrice) {
      queryBuilder.andWhere('cableBundle.price >= :minPrice', {
        minPrice: criteria.minPrice,
      });
    }

    if (criteria.maxPrice) {
      queryBuilder.andWhere('cableBundle.price <= :maxPrice', {
        maxPrice: criteria.maxPrice,
      });
    }

    if (criteria.name) {
      queryBuilder.andWhere('cableBundle.name LIKE :name', {
        name: `%${criteria.name}%`,
      });
    }

    // Pagination logic: validity ASC, price DESC, validity=1 on first pages, limit 20 per page
    const page = criteria.page && criteria.page > 0 ? criteria.page : 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    queryBuilder
      .orderBy('cableBundle.validity', 'ASC')
      .addOrderBy('cableBundle.price', 'ASC')
      .skip(offset)
      .take(limit);

    return await queryBuilder.getMany();
  }
}
