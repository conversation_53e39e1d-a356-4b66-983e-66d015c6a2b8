import { Injectable } from '@nestjs/common';
import { ProviderService } from './provider.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { categories } from '@app/baxi/baxi.interface';
import { FLutterwaveBillerCategory } from '@app/flutterwave/flutterwave.interface';
import { QuickTellerBillerCategory } from '@app/quickteller/quickteller.interface';
import { CreditSwitchServiceId } from '../../libs/credit-switch/src';

@Injectable()
export class ProviderCron {
  constructor(private readonly providerService: ProviderService) {
    // setTimeout(() => {
    //   this.providerService.seedFlutterwaveDataBundle();
    // }, 4000);
  }

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async getToken() {
    await this.providerService.getToken();
  }

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async seedProviders() {
    for (const item of categories.filter(
      (e) => e !== 'airtime', //&& e !== 'databundle',
    )) {
      console.log('Seeding providers', item);
      try {
        await this.providerService.seed(item);
        if (FLutterwaveBillerCategory[item]) {
          await this.providerService.seedFlutterwave(item);
        }
        if (QuickTellerBillerCategory[item]) {
          await this.providerService.seedQuickTeller(item);
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        await new Promise((res, rej) => {
          setTimeout(() => {
            res(null);
          }, 200);
        });
      } catch (error) {
        console.log('Seeding providers', error);
      }
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async seedBundles() {
    // await this.providerService.seedDataBundle();
    // await this.providerService.seedFlutterwaveDataBundle();
    await this.providerService.seedQuickTellerDataBundle();
    // Credit Switch data plans are seeded daily at 2 AM via CreditSwitchDataPlansService
  }

  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async seedCableBundle() {
    // await this.providerService.seedFlutterwaveCableBundle();
    // await this.providerService.seedCableBundle();
    await this.providerService.seedQuickTellerCableBundle();
  }

  @Cron(CronExpression.EVERY_WEEK)
  async seedCreditSwitch() {
    await this.providerService.seedCreditSwitch();
  }

  /**
   * Seed Credit Switch data plans - runs every day at 2 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async seedCreditSwitchDataPlans() {
    console.log('Starting Credit Switch data plans seeding...');

    try {
      // Get data plans for all supported networks
      const networks = [
        { serviceId: CreditSwitchServiceId.MTN_DATA, network: 'mtn' },
        { serviceId: CreditSwitchServiceId.AIRTEL_DATA, network: 'airtel' },
        { serviceId: CreditSwitchServiceId.GLO_DATA, network: 'glo' },
        {
          serviceId: CreditSwitchServiceId.NINE_MOBILE_DATA,
          network: '9mobile',
        },
      ];

      for (const { serviceId, network } of networks) {
        await this.providerService.seedNetworkDataPlansForCreditSwitch(
          serviceId,
          network,
        );
      }

      console.log('Credit Switch data plans seeding completed successfully');
    } catch (error) {
      console.error('Error seeding Credit Switch data plans:', error);
    }
  }

  /**
   * Seed Credit Switch cable TV services - runs every day at 2 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async seedCreditSwitchCableServices() {
    console.log('Starting Credit Switch cable TV services seeding...');

    try {
      // Seed all cable TV services
      await Promise.allSettled([
        this.providerService.seedShowmaxPackagesForCreditSwitch(),
        this.providerService.seedStartimesProductsForCreditSwitch(),
        this.providerService.seedMultichoiceProductsForCreditSwitch('dstv'),
        this.providerService.seedMultichoiceProductsForCreditSwitch('gotv'),
      ]);

      console.log(
        'Credit Switch cable TV services seeding completed successfully',
      );
    } catch (error) {
      console.error('Error seeding Credit Switch cable TV services:', error);
    }
  }

  /**
   * Manual trigger for seeding cable TV services (for testing or manual refresh)
   */
  async manualSeedCreditSwitchCableServices() {
    console.log('Manual Credit Switch cable TV services seeding triggered...');
    await this.seedCreditSwitchCableServices();
  }
}
