import { Biller } from './entities/billers.entity';
import { Providers } from './entities/provider.entity';

export const creditSwitchData: Biller[] = [
  // Airtime
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Airtel',
    serviceCategory: 'airtime',
    serviceId: 'A01E',
    serviceCode: 'A01E',
    serviceName: 'Airtel Airtime',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'MTN',
    serviceCategory: 'airtime',
    serviceId: 'A04E',
    serviceCode: 'A04E',
    serviceName: 'MTN Airtime',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Globacom',
    serviceCategory: 'airtime',
    serviceId: 'A03E',
    serviceCode: 'A03E',
    serviceName: 'Glo Airtime',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: '9mobile',
    serviceCategory: 'airtime',
    serviceId: 'A02E',
    serviceCode: 'A02E',
    serviceName: '9mobile Airtime',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Airtel',
    serviceCategory: 'data',
    serviceId: 'D01D',
    serviceCode: 'D01D',
    serviceName: 'Airtel Data',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: '9mobile',
    serviceCategory: 'data',
    serviceId: 'D02D',
    serviceCode: 'D02D',
    serviceName: '9mobile Data',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Globacom',
    serviceCategory: 'data',
    serviceId: 'D03D',
    serviceCode: 'D03D',
    serviceName: 'Glo Data',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'MTN',
    serviceCategory: 'data',
    serviceId: 'D04D',
    serviceCode: 'D04D',
    serviceName: 'MTN Data',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Smile',
    serviceCategory: 'data',
    serviceId: 'D05D',
    serviceCode: 'D05D',
    serviceName: 'Smile Data',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'NTEL',
    serviceCategory: 'data',
    serviceId: 'D06D',
    serviceCode: 'D06D',
    serviceName: 'NTEL Data',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Ikeja Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E01E',
    serviceCode: 'E01E',
    serviceName: 'Ikeja Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Ikeja Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E02E',
    serviceCode: 'E02E',
    serviceName: 'Ikeja Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Ibadan Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E03E',
    serviceCode: 'E03E',
    serviceName: 'Ibadan Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Ibadan Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E04E',
    serviceCode: 'E04E',
    serviceName: 'Ibadan Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Eko Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E05E',
    serviceCode: 'E05E',
    serviceName: 'Eko Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Eko Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E06E',
    serviceCode: 'E06E',
    serviceName: 'Eko Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Abuja Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E07E',
    serviceCode: 'E07E',
    serviceName: 'Abuja Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Abuja Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E08E',
    serviceCode: 'E08E',
    serviceName: 'Abuja Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Port Harcourt Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E09E',
    serviceCode: 'E09E',
    serviceName: 'Port Harcourt Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Port Harcourt Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E10E',
    serviceCode: 'E10E',
    serviceName: 'Port Harcourt Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Kaduna Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E11E',
    serviceCode: 'E11E',
    serviceName: 'Kaduna Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Kaduna Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E12E',
    serviceCode: 'E12E',
    serviceName: 'Kaduna Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Jos Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E13E',
    serviceCode: 'E13E',
    serviceName: 'Jos Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Jos Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E14E',
    serviceCode: 'E14E',
    serviceName: 'Jos Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Enugu Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E15E',
    serviceCode: 'E15E',
    serviceName: 'Enugu Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Enugu Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E16E',
    serviceCode: 'E16E',
    serviceName: 'Enugu Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Kano Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E17E',
    serviceCode: 'E17E',
    serviceName: 'Kano Electric Prepaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'Kano Electric Disco',
    serviceCategory: 'electricity',
    serviceId: 'E18E',
    serviceCode: 'E18E',
    serviceName: 'Kano Electric Postpaid',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'GOTV',
    serviceCategory: 'cabletv',
    serviceId: 'gotv',
    serviceCode: 'gotv',
    serviceName: 'GOTV Multichoice',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'STARTIMES',
    serviceCategory: 'cabletv',
    serviceId: 'startimes',
    serviceCode: 'startimes',
    serviceName: 'Startimes',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'SHOWMAX',
    serviceCategory: 'cabletv',
    serviceId: 'showmax',
    serviceCode: 'showmax',
    serviceName: 'Showmax',
  },
  {
    provider: Providers.CREDIT_SWITCH,
    serviceType: 'DSTV',
    serviceCategory: 'cabletv',
    serviceId: 'dstv',
    serviceCode: 'dstv',
    serviceName: 'DSTV Multichoice',
  },
];
