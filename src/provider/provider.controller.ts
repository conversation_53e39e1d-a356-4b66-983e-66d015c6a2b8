import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UnprocessableEntityException,
  UseGuards,
} from '@nestjs/common';
import { ProviderService } from './provider.service';
import { BillerCategory, categories } from '@app/baxi/baxi.interface';
import {
  LookupDto,
  LookupFlutterwaveDto,
  LookupQuicktellerDto,
  LookupCreditSwitchPhoneDto,
  CreditSwitchCablePackagesQueryDto,
} from './dto/lookup';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '@crednet/authmanager';

@Controller('provider')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('provider')
export class ProviderController {
  constructor(private readonly providerService: ProviderService) {}

  @Post('/lookup')
  lokup(@Body() dto: LookupDto) {
    return this.providerService.lookup(dto.serviceType, dto.accountNumber);
  }

  @Post('/lookup-flutterwave')
  lokupFlutterwave(@Body() dto: LookupFlutterwaveDto) {
    return this.providerService.lookupFlutterwave(
      dto.accountNumber,
      dto.itemCode,
      dto.billerCode,
    );
  }

  @Post('/lookup-quickteller')
  lokupQuickteller(@Body() dto: LookupQuicktellerDto) {
    return this.providerService.lookupQuickTeller(
      dto.accountNumber,
      dto.paymentCode,
    );
  }

  @Post('/lookup-creditswitch-phone')
  lookupCreditSwitchPhone(@Body() dto: LookupCreditSwitchPhoneDto) {
    return this.providerService.lookupCreditSwitchPhoneNumber(dto.phoneNumber);
  }

  @Get()
  findAll() {
    return this.providerService.findAll();
  }

  @Get(':category')
  findOne(@Param('category') category: string) {
    if (categories.map((e) => e.toString()).includes(category)) {
      return this.providerService.findByCategory(category as BillerCategory);
    }
    throw new UnprocessableEntityException('Category not found');
  }

  @Get('creditswitch/merchant')
  findMerchant() {
    return this.providerService.getCreditSwitchMerchantInfo();
  }

  @Get('databundle/:serviceType')
  databundles(@Param('serviceType') serviceType: string) {
    return this.providerService.databundles(serviceType);
  }

  @Get('cabletv/:serviceType')
  cabletv(@Param('serviceType') serviceType: string) {
    return this.providerService.cableBundles(serviceType);
  }

  @Get('creditswitch/dataplans')
  getCreditSwitchDataPlans() {
    return this.providerService.getCreditSwitchDataPlans();
  }

  @Get('creditswitch/dataplans/:network')
  getCreditSwitchDataPlansByNetwork(@Param('network') network: string) {
    return this.providerService.getCreditSwitchDataPlans(network);
  }

  @Get('creditswitch/cable-packages')
  getCreditSwitchCablePackages(
    @Query() query: CreditSwitchCablePackagesQueryDto,
  ) {
    return this.providerService.searchCreditSwitchCablePackages(query);
  }

  @Get('creditswitch/cable-packages/:serviceType')
  getCreditSwitchCablePackagesByService(
    @Param('serviceType') serviceType: string,
  ) {
    return this.providerService.getCreditSwitchCablePackages(serviceType);
  }
}
