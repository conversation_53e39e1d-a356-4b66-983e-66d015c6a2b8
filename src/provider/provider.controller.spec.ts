import { Test, TestingModule } from '@nestjs/testing';
import { ProviderController } from './provider.controller';
import { ProviderService } from './provider.service';
import { BaxiService } from '@app/baxi';
import { BillerRepository } from './repository/billers.repository';
import { DataBundleRepository } from './repository/data-bundle.repository';
import { CableBundleRepository } from './repository/cable-bundle.repository';
import { FlutterwaveService } from '@app/flutterwave';
import { ConfigurationService } from '../configuration/configuration.service';

describe('ProviderController', () => {
  let controller: ProviderController;
  let providerService: ProviderService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProviderController],
      providers: [
        ProviderService,
        {
          provide: BaxiService,
          useValue: {
            getBillers: jest.fn(),
            getDataBundles: jest.fn(),
            getCableBundles: jest.fn(),
          },
        },
        {
          provide: BillerRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
          },
        },
        {
          provide: DataBundleRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
          },
        },
        {
          provide: CableBundleRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            findOneBy: jest.fn(),
          },
        },
        {
          provide: FlutterwaveService,
          useValue: {
            getBillers: jest.fn(),
            getDataBundles: jest.fn(),
            getCableBundles: jest.fn(),
          },
        },
        {
          provide: ConfigurationService,
          useValue: {
            getConfig: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ProviderController>(ProviderController);
    providerService = module.get<ProviderService>(ProviderService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
