import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class LookupDto {
  @ApiProperty()
  @IsString()
  serviceType: string;

  @ApiProperty()
  @IsString()
  accountNumber: string;
}

export class LookupFlutterwaveDto {
  @ApiProperty()
  @IsString()
  itemCode: string;

  @ApiProperty()
  @IsString()
  billerCode: string;

  @ApiProperty()
  @IsString()
  accountNumber: string;
}

export class LookupQuicktellerDto {
  @ApiProperty()
  @IsString()
  accountNumber: string;

  @ApiProperty()
  @IsString()
  paymentCode: string;
}

export class LookupCreditSwitchPhoneDto {
  @ApiProperty()
  @IsString()
  phoneNumber: string;
}

export class LookupCreditSwitchCableDto {
  @ApiProperty()
  @IsString()
  smartcardNumber: string;

  @ApiProperty()
  @IsString()
  productCode: string;
}

export class CreditSwitchCablePackagesQueryDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  serviceType: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  page: number;
}
