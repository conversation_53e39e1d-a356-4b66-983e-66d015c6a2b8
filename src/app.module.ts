import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ProviderModule } from './provider/provider.module';
import { typeOrmConfig } from './config/data-source';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bullmq';
import { PaymentModule } from './payment/payment.module';
import config from './config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import {
  Events,
  Exchanges,
  PaymentEvents,
} from './utils/queue';
import { ConfigurationModule } from './configuration/configuration.module';
import { RabbitmqModule, RedisModule } from '@crednet/utils';
import { AdminModule } from './admin/admin.module';
import { CacheManagerModule } from '@crednet/authmanager';

@Module({
  imports: [
    ProviderModule,
    CacheManagerModule.register(),
    TypeOrmModule.forRoot(typeOrmConfig),
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot({
      global: true,
    }),
    RedisModule.forRoot({
      redis: {
        url: config.redis.url,
      },
    }),
    RabbitmqModule.register({
      host: config.rabbitMq.brockers[0],
      queueName: 'bills-payment.queue.dev',
      prefetchCount: 10,
      showLog: true,
      deadLetterQueueInterval: 60000,
      consumeDeadLetterQueue: false,
      producer: {
        name: Exchanges.PAYMENT,
        durable: true,
      },
      global: true,
      subscriptions: [
        `${Exchanges.PAYMENT}.${PaymentEvents.BILL_PAYMENT_STATUS}`,
        `${Exchanges.WEBHOOK}.${Events.FLUTTERWAVE_BILLS_EVENT}`,
      ],
    }),
    BullModule.forRoot({
      connection: {
        username: config.redis.user,
        password: config.redis.password,
        host: config.redis.host,
        port: config.redis.port,
      },
      defaultJobOptions: {
        removeOnComplete: {
          age: 3600,
        },
        removeOnFail: {
          age: 48 * 3600,
        },
        attempts: 5,
      },
    }),
    PaymentModule,
    ConfigurationModule,
    AdminModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
