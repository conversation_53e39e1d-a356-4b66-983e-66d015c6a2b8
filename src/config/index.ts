import * as path from 'node:path';
import * as dotenv from 'dotenv';

dotenv.config({
  path: path.resolve(process.cwd(), './.env'),
});

export default {
  port: Number.parseInt(process.env.PORT, 10),
  env: process.env.ENV,
  nodeEnv: process.env.NODE_ENV,
  baseUrl: process.env.BASE_URL || `http://127.0.0.1:${process.env.PORT}`,
  jwt: {
    publicKey: process.env.PUBLIC_KEY,
    issuer: process.env.ISSUER || 'crednet/auth', // time in seconds
  },
  db: {
    url: process.env.DATABASE_URL,
  },
  baxi: {
    baseUrl: process.env.BAXI_BASE_URL,
    apiKey: process.env.BAXI_API_KEY,
  },
  flutterwave: {
    baseUrl: 'https://api.flutterwave.com',
    apiKey: process.env.FLUTTERWAVE_SECRET_KEY,
  },
  redis: {
    url: process.env.REDIS_URL,
    host: process.env.REDIS_HOST,
    user: process.env.REDIS_USER,
    password: process.env.REDIS_PASS,
    port: Number.parseInt(process.env.REDIS_PORT, 10),
  },
  kafka: {
    sasl: {
      username: process.env.KAFKA_SASL_USERNAME,
      password: process.env.KAFKA_SASL_PASSWORD,
    },
    brokers: process.env.KAFKA_BROKERS?.split(','),
  },
  quickteller: {
    clientId: process.env.QUICKTELLER_CLIENT_ID,
    clientSecret: process.env.QUICKTELLER_CLIENT_SECRET,
    baseUrl: process.env.QUICKTELLER_BASE_URL,
    authUrl: process.env.QUICKTELLER_AUTH_URL,
    terminalId: process.env.QUICKTELLER_TERMINAL_ID,
  },
  creditSwitch: {
    baseUrl: process.env.CREDIT_SWITCH_BASE_URL,
    loginId: Number.parseInt(process.env.CREDIT_SWITCH_LOGIN_ID, 10),
    publicKey: process.env.CREDIT_SWITCH_PUBLIC_KEY,
    secretKey: process.env.CREDIT_SWITCH_SECRET_KEY,
  },
  isCronEnabled: process.env.IS_CRON_ENABLED == 'true',
  rabbitMq: {
    brockers: process.env.RABBIT_MQ_BROCKERS?.split(','),
  },
};
