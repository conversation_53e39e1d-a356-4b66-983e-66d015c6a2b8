import { Biller } from 'src/provider/entities/billers.entity';
import config from '.';
import { DataSource, DataSourceOptions } from 'typeorm';
import { CableBundle } from 'src/provider/entities/cable-bundle.entity';
import { DataBundle } from 'src/provider/entities/data-bundle.enity';
import { User } from 'src/payment/entities/user.entity';
import { Bill } from 'src/payment/entities/bill.entity';
import { Configuration } from 'src/configuration/entities/configuration.entity';

require('dotenv').config();

export const typeOrmConfig: DataSourceOptions = {
  type: 'mysql',
  url: config.db.url,
  timezone: 'Z',
  // autoLoadEntities:true,
  migrations: ['dist/db/migrations/*.js'],
  ssl: false,
  entities: [Biller, CableBundle, DataBundle, User, Bill, Configuration],
  subscribers: [],
  logging: false,
  synchronize: true,
  // synchronize: process.env.NODE_ENV === 'development',
  // connectTimeout:30000
};

export const dataSource = new DataSource(typeOrmConfig);
