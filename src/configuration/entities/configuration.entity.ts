import { BaseEntity } from 'src/config/repository/base-entity';
import { Column, Entity, Index } from 'typeorm';

@Entity({ name: 'configurations' })
@Index(['createdAt'])
export class Configuration extends BaseEntity {
  @Column({
    nullable: true,
  })
  title: string;

  @Column({
    nullable: false,
  })
  @Index()
  slug: string;

  @Column({
    nullable: false,
  })
  default: string;

  @Column({
    nullable: true,
  })
  lastEditedBy: string;

  @Column()
  value: string;

  @Column({
    nullable: true,
  })
  description: string;

  @Column('json', {
    nullable: true,
  })
  meta: any;
}
