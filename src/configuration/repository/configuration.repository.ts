import { Injectable } from '@nestjs/common';
import { TypeOrmRepository } from 'src/config/repository/typeorm.repository';
import { DataSource } from 'typeorm';
import { Configuration } from '../entities/configuration.entity';

@Injectable()
export class ConfigurationRepository extends TypeOrmRepository<Configuration> {
  constructor(private readonly dataSource: DataSource) {
    super(Configuration, dataSource.createEntityManager());
  }
}
