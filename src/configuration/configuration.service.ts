import { Injectable, OnModuleInit } from '@nestjs/common'; 
import { ConfigurationRepository } from './repository/configuration.repository';
import { Providers } from 'src/provider/entities/provider.entity';
import { RedisService } from '@crednet/utils';

@Injectable()
export class ConfigurationService implements OnModuleInit {
  constructor(
    private readonly configurationRepository: ConfigurationRepository,
    private readonly redisService: RedisService,
  ) {}
  static readonly ELECTRICITY_PROVIDER = 'electricity_provider';
  static readonly CABLE_PROVIDER = 'cable_provider';
  static readonly DATA_PROVIDER = 'data_provider';
  static readonly AIRTIME_PROVIDER = 'airtime_provider';
  async onModuleInit() {
    // setTimeout(() => {
    for (const provider of [
      ConfigurationService.ELECTRICITY_PROVIDER,
      ConfigurationService.CABLE_PROVIDER,
      ConfigurationService.DATA_PROVIDER,
      ConfigurationService.AIRTIME_PROVIDER,
    ]) {
      this.bootstrap(provider);
    }
    // }, 1000);
  }

  async bootstrap(slug: string) {
    let config = await this.configurationRepository.findOneBy({ slug });
    if (!config) {
      config = await this.configurationRepository.save({
        slug,
        default: Providers.BAXI,
        value: Providers.BAXI,
      });
    }
    if (config) {
      await this.redisService.set(slug, config.value);
    }
    return config.value;
  }

  async getConfig(slug: string) {
    const config = await this.redisService.get(slug);

    if (!config) {
      return this.bootstrap(slug);
    }
    return config;
  }
}
