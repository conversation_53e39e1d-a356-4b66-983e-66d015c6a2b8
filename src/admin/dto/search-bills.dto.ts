import { BillerCategory, categories } from '@app/baxi/baxi.interface';
import { PaginationQueryDto } from '@crednet/utils';
import { ApiProperty } from '@nestjs/swagger';
import {  IsOptional } from 'class-validator';
import { WalletType } from 'src/payment/dto/purchase-bill.dto';
import { BillStatus } from 'src/payment/entities/bill.entity';

export class SearchBillsDto extends PaginationQueryDto {
  @ApiProperty({ nullable: true, required: false, enum: BillStatus })
//   @IsEnum(BillStatus)
  @IsOptional()
  status: BillStatus;

  @ApiProperty({ nullable: true, required: false, enum: categories })
  @IsOptional()
  serviceCategory: BillerCategory;

  @ApiProperty({ nullable: true, required: false, enum: WalletType })
  @IsOptional()
  walletType: WalletType;

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  reference: string

  @ApiProperty({ nullable: true, required: false })
  @IsOptional()
  userId: string



}
