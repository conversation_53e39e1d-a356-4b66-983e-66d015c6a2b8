import { Test, TestingModule } from '@nestjs/testing';
import { AdminService } from './admin.service';
import { BillRepository } from '../payment/repository/bill.repository';
import { UserRepository } from '../payment/repository/user.repository';

describe('AdminService', () => {
  let service: AdminService;
  let billRepository: BillRepository;
  let userRepository: UserRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminService,
        {
          provide: BillRepository,
          useValue: {
            findMany: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: UserRepository,
          useValue: {
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AdminService>(AdminService);
    billRepository = module.get<BillRepository>(BillRepository);
    userRepository = module.get<UserRepository>(UserRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
