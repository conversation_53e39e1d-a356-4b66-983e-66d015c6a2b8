import { Injectable, Query } from '@nestjs/common';
import { BillRepository } from 'src/payment/repository/bill.repository';
import { SearchBillsDto } from './dto/search-bills.dto';
import { UserRepository } from 'src/payment/repository/user.repository';

@Injectable()
export class AdminService {
  constructor(
    private readonly billRepository: BillRepository,
    private readonly userRepository: UserRepository,
  ) {}

  async findAll(@Query() dto: SearchBillsDto) {
    const where = {};
    if (dto.status) where['status'] = dto.status;
    if (dto.serviceCategory) where['serviceCategory'] = dto.serviceCategory;
    if (dto.walletType) where['walletType'] = dto.walletType;
    if (dto.reference) where['reference'] = dto.reference;
    
    if (dto.userId) {
      const user = await this.userRepository.findOne({
        where: { userId: dto.userId },
        select: ['id'],
      });

      if (user) where['user'] = { id: user.id };
    }

    return await this.billRepository.findMany(
      {
        limit: dto.limit ?? 30,
        page: dto.page,
      },
      {
        order: { [dto.sortColumn ?? 'createdAt']: dto.sortOrder ?? 'DESC' },
        where,
        relations: ['user'],
      },
    );
  }
}
