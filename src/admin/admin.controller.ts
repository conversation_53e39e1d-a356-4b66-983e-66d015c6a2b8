import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AdminService } from './admin.service';
import { JwtAuthGuard } from '@crednet/authmanager';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { SearchBillsDto } from './dto/search-bills.dto';

@Controller('admin')
@Controller('provider')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT')
@ApiTags('admin')
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Get()
  findAll(@Query() dto: SearchBillsDto) {
    return this.adminService.findAll(dto);
  }
}
