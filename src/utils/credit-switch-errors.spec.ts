import {
  getCreditSwitchErrorInfo,
  isCreditSwitchErrorRetryable,
  isCreditSwitchErrorFinal,
  shouldEscalateCreditSwitchError,
  calculateCreditSwitchRetryDelay,
  shouldRetryCreditSwitchRequest,
  formatCreditSwitchError,
  creditSwitchRetryConfig,
} from './credit-switch-errors';

describe('Credit Switch Error Utilities', () => {
  describe('getCreditSwitchErrorInfo', () => {
    it('should return correct error info for known status codes', () => {
      const successInfo = getCreditSwitchErrorInfo('00');
      expect(successInfo.code).toBe('00');
      expect(successInfo.description).toBe('Successful');
      expect(successInfo.isRetryable).toBe(false);
      expect(successInfo.isFinal).toBe(false);

      const insufficientBalanceInfo = getCreditSwitchErrorInfo('02');
      expect(insufficientBalanceInfo.code).toBe('02');
      expect(insufficientBalanceInfo.description).toBe('Insufficient balance');
      expect(insufficientBalanceInfo.isRetryable).toBe(false);
      expect(insufficientBalanceInfo.isFinal).toBe(true);
      expect(insufficientBalanceInfo.shouldEscalate).toBe(true);

      const networkErrorInfo = getCreditSwitchErrorInfo('04');
      expect(networkErrorInfo.code).toBe('04');
      expect(networkErrorInfo.description).toBe('Network error');
      expect(networkErrorInfo.isRetryable).toBe(true);
      expect(networkErrorInfo.isFinal).toBe(false);
      expect(networkErrorInfo.shouldEscalate).toBe(false);
    });

    it('should return unknown error info for unknown status codes', () => {
      const unknownInfo = getCreditSwitchErrorInfo('99');
      expect(unknownInfo.code).toBe('99');
      expect(unknownInfo.description).toBe('Unknown error');
      expect(unknownInfo.isRetryable).toBe(false);
      expect(unknownInfo.isFinal).toBe(true);
      expect(unknownInfo.shouldEscalate).toBe(true);
    });
  });

  describe('isCreditSwitchErrorRetryable', () => {
    it('should correctly identify retryable errors', () => {
      expect(isCreditSwitchErrorRetryable('04')).toBe(true); // Network error
      expect(isCreditSwitchErrorRetryable('08')).toBe(true); // Maintenance
      expect(isCreditSwitchErrorRetryable('09')).toBe(true); // Timeout
      expect(isCreditSwitchErrorRetryable('01')).toBe(true); // General error
    });

    it('should correctly identify non-retryable errors', () => {
      expect(isCreditSwitchErrorRetryable('00')).toBe(false); // Success
      expect(isCreditSwitchErrorRetryable('02')).toBe(false); // Insufficient balance
      expect(isCreditSwitchErrorRetryable('03')).toBe(false); // Invalid recipient
      expect(isCreditSwitchErrorRetryable('05')).toBe(false); // Duplicate request
      expect(isCreditSwitchErrorRetryable('10')).toBe(false); // Invalid checksum
    });
  });

  describe('isCreditSwitchErrorFinal', () => {
    it('should correctly identify final errors', () => {
      expect(isCreditSwitchErrorFinal('02')).toBe(true); // Insufficient balance
      expect(isCreditSwitchErrorFinal('03')).toBe(true); // Invalid recipient
      expect(isCreditSwitchErrorFinal('05')).toBe(true); // Duplicate request
      expect(isCreditSwitchErrorFinal('06')).toBe(true); // Invalid service ID
      expect(isCreditSwitchErrorFinal('07')).toBe(true); // Invalid amount
      expect(isCreditSwitchErrorFinal('10')).toBe(true); // Invalid checksum
    });

    it('should correctly identify non-final errors', () => {
      expect(isCreditSwitchErrorFinal('00')).toBe(false); // Success
      expect(isCreditSwitchErrorFinal('01')).toBe(false); // General error
      expect(isCreditSwitchErrorFinal('04')).toBe(false); // Network error
      expect(isCreditSwitchErrorFinal('08')).toBe(false); // Maintenance
      expect(isCreditSwitchErrorFinal('09')).toBe(false); // Timeout
    });
  });

  describe('shouldEscalateCreditSwitchError', () => {
    it('should correctly identify errors that need escalation', () => {
      expect(shouldEscalateCreditSwitchError('01')).toBe(true); // General error
      expect(shouldEscalateCreditSwitchError('02')).toBe(true); // Insufficient balance
      expect(shouldEscalateCreditSwitchError('10')).toBe(true); // Invalid checksum
    });

    it('should correctly identify errors that do not need escalation', () => {
      expect(shouldEscalateCreditSwitchError('00')).toBe(false); // Success
      expect(shouldEscalateCreditSwitchError('03')).toBe(false); // Invalid recipient
      expect(shouldEscalateCreditSwitchError('04')).toBe(false); // Network error
      expect(shouldEscalateCreditSwitchError('05')).toBe(false); // Duplicate request
      expect(shouldEscalateCreditSwitchError('08')).toBe(false); // Maintenance
    });
  });

  describe('calculateCreditSwitchRetryDelay', () => {
    it('should calculate correct delay with exponential backoff', () => {
      const delay1 = calculateCreditSwitchRetryDelay(1);
      const delay2 = calculateCreditSwitchRetryDelay(2);
      const delay3 = calculateCreditSwitchRetryDelay(3);

      expect(delay1).toBeGreaterThanOrEqual(1000); // Minimum 1 second
      expect(delay2).toBeGreaterThan(delay1);
      expect(delay3).toBeGreaterThan(delay2);
    });

    it('should respect maximum delay', () => {
      const delay = calculateCreditSwitchRetryDelay(10);
      expect(delay).toBeLessThanOrEqual(creditSwitchRetryConfig.maxDelay);
    });

    it('should work with custom config', () => {
      const customConfig = {
        maxRetries: 5,
        baseDelay: 1000,
        maxDelay: 10000,
        backoffMultiplier: 3,
        jitter: false,
      };

      const delay1 = calculateCreditSwitchRetryDelay(1, customConfig);
      const delay2 = calculateCreditSwitchRetryDelay(2, customConfig);

      expect(delay1).toBe(1000);
      expect(delay2).toBe(3000);
    });

    it('should add jitter when enabled', () => {
      const config = { ...creditSwitchRetryConfig, jitter: true };
      const delays = Array.from({ length: 10 }, () => calculateCreditSwitchRetryDelay(1, config));
      
      // With jitter, delays should vary
      const uniqueDelays = new Set(delays);
      expect(uniqueDelays.size).toBeGreaterThan(1);
    });

    it('should not add jitter when disabled', () => {
      const config = { ...creditSwitchRetryConfig, jitter: false };
      const delays = Array.from({ length: 10 }, () => calculateCreditSwitchRetryDelay(1, config));
      
      // Without jitter, all delays should be the same
      const uniqueDelays = new Set(delays);
      expect(uniqueDelays.size).toBe(1);
    });
  });

  describe('shouldRetryCreditSwitchRequest', () => {
    it('should allow retry for retryable errors within limit', () => {
      expect(shouldRetryCreditSwitchRequest(1, '04')).toBe(true); // Network error, attempt 1
      expect(shouldRetryCreditSwitchRequest(2, '08')).toBe(true); // Maintenance, attempt 2
      expect(shouldRetryCreditSwitchRequest(3, '09')).toBe(true); // Timeout, attempt 3
    });

    it('should not allow retry for non-retryable errors', () => {
      expect(shouldRetryCreditSwitchRequest(1, '02')).toBe(false); // Insufficient balance
      expect(shouldRetryCreditSwitchRequest(1, '03')).toBe(false); // Invalid recipient
      expect(shouldRetryCreditSwitchRequest(1, '05')).toBe(false); // Duplicate request
    });

    it('should not allow retry when max attempts reached', () => {
      expect(shouldRetryCreditSwitchRequest(3, '04')).toBe(true); // Within limit
      expect(shouldRetryCreditSwitchRequest(4, '04')).toBe(false); // Exceeds limit (default maxRetries = 3)
    });

    it('should work with custom config', () => {
      const customConfig = { ...creditSwitchRetryConfig, maxRetries: 5 };
      
      expect(shouldRetryCreditSwitchRequest(4, '04', customConfig)).toBe(true);
      expect(shouldRetryCreditSwitchRequest(5, '04', customConfig)).toBe(true);
      expect(shouldRetryCreditSwitchRequest(6, '04', customConfig)).toBe(false);
    });
  });

  describe('formatCreditSwitchError', () => {
    it('should format error with status code and description', () => {
      const formatted = formatCreditSwitchError('02', 'Insufficient balance');
      expect(formatted).toBe('Credit Switch Error [02]: Insufficient balance');
    });

    it('should use default description when none provided', () => {
      const formatted = formatCreditSwitchError('02', '');
      expect(formatted).toBe('Credit Switch Error [02]: Insufficient balance');
    });

    it('should include context when provided', () => {
      const context = { requestId: 'test-123', amount: 100 };
      const formatted = formatCreditSwitchError('02', 'Insufficient balance', context);
      expect(formatted).toBe('Credit Switch Error [02]: Insufficient balance | Context: {"requestId":"test-123","amount":100}');
    });

    it('should handle unknown error codes', () => {
      const formatted = formatCreditSwitchError('99', 'Custom error message');
      expect(formatted).toBe('Credit Switch Error [99]: Custom error message');
    });

    it('should handle unknown error codes without description', () => {
      const formatted = formatCreditSwitchError('99', '');
      expect(formatted).toBe('Credit Switch Error [99]: Unknown error');
    });
  });
});
