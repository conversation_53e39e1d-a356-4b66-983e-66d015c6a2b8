/**
 * Credit Switch Error Codes and Handling Utilities
 * 
 * Based on the Credit Switch API documentation and integration flow,
 * this module provides error code mappings and retry logic utilities.
 */

export const creditSwitchErrorCodes = [
  '01', // General error
  '02', // Insufficient balance
  '03', // Invalid recipient
  '04', // Network error
  '05', // Duplicate request
  '06', // Invalid service ID
  '07', // Invalid amount
  '08', // Maintenance
  '09', // Timeout
  '10', // Invalid checksum
];

export const creditSwitchRetryableErrors = [
  '04', // Network error
  '08', // Maintenance
  '09', // Timeout
];

export const creditSwitchFinalErrors = [
  '02', // Insufficient balance
  '03', // Invalid recipient
  '05', // Duplicate request
  '06', // Invalid service ID
  '07', // Invalid amount
  '10', // Invalid checksum
];

export interface CreditSwitchErrorInfo {
  code: string;
  description: string;
  isRetryable: boolean;
  isFinal: boolean;
  shouldEscalate: boolean;
}

export const creditSwitchErrorMap: Record<string, CreditSwitchErrorInfo> = {
  '00': {
    code: '00',
    description: 'Successful',
    isRetryable: false,
    isFinal: false,
    shouldEscalate: false,
  },
  '01': {
    code: '01',
    description: 'General error',
    isRetryable: true,
    isFinal: false,
    shouldEscalate: true,
  },
  '02': {
    code: '02',
    description: 'Insufficient balance',
    isRetryable: false,
    isFinal: true,
    shouldEscalate: true,
  },
  '03': {
    code: '03',
    description: 'Invalid recipient',
    isRetryable: false,
    isFinal: true,
    shouldEscalate: false,
  },
  '04': {
    code: '04',
    description: 'Network error',
    isRetryable: true,
    isFinal: false,
    shouldEscalate: false,
  },
  '05': {
    code: '05',
    description: 'Duplicate request',
    isRetryable: false,
    isFinal: true,
    shouldEscalate: false,
  },
  '06': {
    code: '06',
    description: 'Invalid service ID',
    isRetryable: false,
    isFinal: true,
    shouldEscalate: false,
  },
  '07': {
    code: '07',
    description: 'Invalid amount',
    isRetryable: false,
    isFinal: true,
    shouldEscalate: false,
  },
  '08': {
    code: '08',
    description: 'System maintenance',
    isRetryable: true,
    isFinal: false,
    shouldEscalate: false,
  },
  '09': {
    code: '09',
    description: 'Request timeout',
    isRetryable: true,
    isFinal: false,
    shouldEscalate: false,
  },
  '10': {
    code: '10',
    description: 'Invalid checksum',
    isRetryable: false,
    isFinal: true,
    shouldEscalate: true,
  },
};

/**
 * Get error information for a Credit Switch status code
 */
export function getCreditSwitchErrorInfo(statusCode: string): CreditSwitchErrorInfo {
  return creditSwitchErrorMap[statusCode] || {
    code: statusCode,
    description: 'Unknown error',
    isRetryable: false,
    isFinal: true,
    shouldEscalate: true,
  };
}

/**
 * Check if a Credit Switch error is retryable
 */
export function isCreditSwitchErrorRetryable(statusCode: string): boolean {
  const errorInfo = getCreditSwitchErrorInfo(statusCode);
  return errorInfo.isRetryable;
}

/**
 * Check if a Credit Switch error is final (should not be retried)
 */
export function isCreditSwitchErrorFinal(statusCode: string): boolean {
  const errorInfo = getCreditSwitchErrorInfo(statusCode);
  return errorInfo.isFinal;
}

/**
 * Check if a Credit Switch error should be escalated to support
 */
export function shouldEscalateCreditSwitchError(statusCode: string): boolean {
  const errorInfo = getCreditSwitchErrorInfo(statusCode);
  return errorInfo.shouldEscalate;
}

/**
 * Credit Switch retry configuration
 */
export const creditSwitchRetryConfig = {
  maxRetries: 3,
  baseDelay: 2000, // 2 seconds
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2,
  jitter: true,
};

/**
 * Calculate retry delay with exponential backoff
 */
export function calculateCreditSwitchRetryDelay(
  attempt: number,
  config = creditSwitchRetryConfig,
): number {
  const delay = Math.min(
    config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1),
    config.maxDelay,
  );

  if (config.jitter) {
    // Add random jitter (±25%)
    const jitterRange = delay * 0.25;
    const jitter = (Math.random() - 0.5) * 2 * jitterRange;
    return Math.max(1000, delay + jitter); // Minimum 1 second
  }

  return delay;
}

/**
 * Check if we should retry based on attempt count and error
 */
export function shouldRetryCreditSwitchRequest(
  attempt: number,
  statusCode: string,
  config = creditSwitchRetryConfig,
): boolean {
  if (attempt >= config.maxRetries) {
    return false;
  }

  return isCreditSwitchErrorRetryable(statusCode);
}

/**
 * Format Credit Switch error for logging
 */
export function formatCreditSwitchError(
  statusCode: string,
  statusDescription: string,
  context?: any,
): string {
  const errorInfo = getCreditSwitchErrorInfo(statusCode);
  return `Credit Switch Error [${statusCode}]: ${statusDescription || errorInfo.description}${
    context ? ` | Context: ${JSON.stringify(context)}` : ''
  }`;
}
