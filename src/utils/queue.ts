export enum Queues {
  BILLS = 'bills_queue',
  // BILLS = 'bills_queue'
}

export enum Exchanges {
  PAYMENT = 'payment',
  WEBHOOK = 'webhook',
  NOTIFICATION = 'notification',
  CASH = 'cash',
  CREDIT = 'credit',
  // BILLS = 'bills_queue'
}

export enum Events {
  REQUERY_TRANSACTION = 'requery_transaction',
  FLUTTERWAVE_BILLS_EVENT = 'flutterwave.singlebillpayment.status',
  BILLS_NOTIFICATION = 'send-bills-notification',
}

export enum PaymentEvents {
  BILL_PAYMENT_STATUS = 'bill_payment_status',
  // PROCESS_PAYMENT = 'payment_processed',
  // PAYMENT_FAILED = 'payment_failed',
  // REQUERY_TRANSACTION = 'requery_transaction'
}

export enum PaymentRequestEventTypes {
  // PAYMENT_REQUEST_FUND = 'cp.payment.charge',
  // PAYMENT_REQUEST_FINALIZE = 'cp.payment.finalize',
  // PAYMENT_RESPONSE_STATUS = 'cp.payment.status',
  CREATE_CREDIT_TRANSACTION ='create-credit-transaction',

  // CREATE_DEBIT_TRANSACTION = 'create-debit-transaction',
  QUERY_TRANSACTION = 'query-transaction',

  REVERSE_TRANSACTION = 'reverse-transaction',
}

export enum NotificationTemplates { 
  BILL_PAYMENT_SUCCESSFUL = 'bill_payment_successful',
  BILL_PAYMENT_FAILED = 'bill_payment_failed',  
  ELECTRICITY_PAYMENT_SUCCESSFUL= 'electricity_payment_successful'
}