interface CablePackageOption {
  statusCode: string;
  statusDescription: {
    items: Array<{
      packageCode: string;
      packageName: string;
      description: string;
      monthsPaidFor: number;
      price: number;
      invoicePeriod: number;
    }>;
  };
}

interface OriginalCableBundle {
  statusCode: string;
  statusDescription: {
    items: Array<{
      availablePricingOptions: Array<{
        monthsPaidFor: number;
        price: number;
        invoicePeriod: number;
      }>;
      code: string;
      name: string;
      description: string;
    }>;
  };
}

export function transformCableBundles(
  originalData: OriginalCableBundle,
): CablePackageOption {
  const transformedItems = originalData.statusDescription.items.flatMap(
    (item) =>
      item.availablePricingOptions.map((option) => ({
        packageCode: item.code,
        packageName: item.name,
        description: item.description,
        monthsPaidFor: option.monthsPaidFor,
        price: option.price,
        invoicePeriod: option.invoicePeriod,
      })),
  );

  return {
    statusCode: originalData.statusCode,
    statusDescription: {
      items: transformedItems,
    },
  };
}
