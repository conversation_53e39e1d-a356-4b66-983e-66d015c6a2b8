export enum BaxiErrors {
  BX0002 = 'BX0002', // Transaction not found
  BX0003 = 'BX0020', // Invalid phone number
}

export const baxiErrorCodes = [
  'BX0002',
  'BX0020',
  'EXC00103',
  'BX0003',
  'EXC00105',
  'BX0023',
  'EXC00109',
  'EXC00130',
  'EXC00125',
  'EXC00123',
  'EXC00119',
  'EXC00116',
  'EXC00115',
  'EXC00113',
  'EXC00109',
  'EXC00102',
  'BX0025',
];

// BX0001 - Connection Timeout: Retried 3 times and no response from the provider.
// BX0019 - Request status unknown, Connection Timeout
// BX0021 - Transaction status unknown: Please re-query to confirm final status
// BX0024 - No response from the connecting service provider
// EXC00103 - Requested service is not active.
// EXC00105 - Balance could not be obtained.
// EXC00109 - Service is blocked.
// EXC00114 - Exchange is still pending
// EXC00124 - The provider platform did not respond in a timely manner.
// UNK0001 - Unknown error on the platform
// EXC00001 - Bad Gateway From Provider (Requery)

// REFUND:
// "BX0002",

// EXC00102, EXC00109

// BX0001	Connection Timeout: Retried 3 times and no response from the service provider
// BX0002	Transaction does not exist
// BX0003	Invalid request parameters data
// BX0004	An In-valid user account in authorization headers sent
// BX0005	Baxi request date is too old, use the current GMT date.
// BX0006	Hash Signature does not match request data sent.
// BX0007	Unidentified User Account
// BX0008	The request coming from an untrusted source
// BX0009	Client not permitted to access user data
// BX0010	The agent does not exist
// BX0011	Invalid Bearer String, expected keyword Baxi or API key
// BX0012	Authentication/Authorization mechanism not implemented
// BX0013	Bearer Token not provided
// BX0014	Token has expired.
// BX0015	Invalid token supplied
// BX0016	Token has been blacklisted,
// BX0017	Server Error: Unable to retrieve property
// BX0018	Request route not found,
// BX0019	Request status unknown, Connection Timeout
// BX0020	An error occurred during transaction or request processing
// BX0021	Transaction status unknown: Please query to confirm final status
// BX0022	Failed Transaction from the service provider
// BX0023	Duplicate Agent Transaction Reference
// BX0024	No response from the connecting service provider
// BX0025	Error encountered during name finder validation
// BX0026	Requested biller service does not exist
// PROG0001	Programming error on the platform. (Should contact CDL support)
// PROG0002	Programming error on the platform. (Should contact CDL support)
// PROG0003	Programming error on the platform. (Should contact CDL support)
// SEC00001	Bad credentials
// SEC00002	The user account has expired.
// SEC00003	User credentials have expired.
// SEC00004	The user is not active.
// SEC00005	The user is blocked.
// SEC00100	Unknown authentication error. (Should contact CDL support)
// SEC01001	Access is denied.
// UNK0001	Unknown error on the platform. (Should resolve through API
// EXC00100	The service code is not recognized. (Should check client implementation)
// EXC00102	Not enough funds were available to complete the exchange.
// EXC00103	The requested service is not active. (Should resolve through API)
// EXC00105	The balance could not be obtained. (Should resolve through API)
// EXC00107	The cancellation of the exchange was rejected by the provider. (Should contact CDL support)
// EXC00109	The service is blocked. (Should resolve through API)
// EXC00112	The query of exchange failed because the exchange was never registered on the platform.
// EXC00113	The exchange is already canceled by the provider.
// EXC00114	The exchange is still pending. (Should resolve through API)
// EXC00115	The exchange was not sent to the provider.
// EXC00116	The exchange request was already registered on the platform with the same ID but with different values. (Should check client implementation)
// EXC00117	There was an error while calculating commissions. (Must contact CDL support immediately)
// EXC00118	The sent service fee is invalid.
// EXC00119	The payment collector code is not recognized. (Should check client implementation)
// EXC00123	Service is not allowed.
// EXC00124	The provider platform did not respond in a timely manner. (Should resolve through API)
// EXC00125	The exchange request is not valid.
// EXC00126	The cancellation failed. (Must be resolved through 'query' API call)
// EXC00127	The exchange amount is too high.
// EXC00130	Payment method not recognized. (Should check client implementation)
// EXC00131	The exchange request is not allowed at the moment.
