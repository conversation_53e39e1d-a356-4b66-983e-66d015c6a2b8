export function formatPhoneNumber(phoneNumber) {
    // Check if the phone number already starts with '234'
    if (phoneNumber.startsWith('234')) {
      return `+${phoneNumber}`;
    }
    
    // Replace the leading '0' with '234' if it doesn't already start with '234'
    if (phoneNumber.startsWith('0')) {
      return '+234' + phoneNumber.slice(1);
    }
    return phoneNumber; // Return as is if no modification is needed
  }
  