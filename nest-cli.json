{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true}, "projects": {"baxi": {"type": "library", "root": "libs/baxi", "entryFile": "index", "sourceRoot": "libs/baxi/src", "compilerOptions": {"tsConfigPath": "libs/baxi/tsconfig.lib.json"}}, "rabbitmq": {"type": "library", "root": "libs/rabbitmq", "entryFile": "index", "sourceRoot": "libs/rabbitmq/src", "compilerOptions": {"tsConfigPath": "libs/rabbitmq/tsconfig.lib.json"}}, "flutterwave": {"type": "library", "root": "libs/flutterwave", "entryFile": "index", "sourceRoot": "libs/flutterwave/src", "compilerOptions": {"tsConfigPath": "libs/flutterwave/tsconfig.lib.json"}}, "quickteller": {"type": "library", "root": "libs/quickteller", "entryFile": "index", "sourceRoot": "libs/quickteller/src", "compilerOptions": {"tsConfigPath": "libs/quickteller/tsconfig.lib.json"}}}}