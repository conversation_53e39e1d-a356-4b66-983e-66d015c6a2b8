
 API DOCS
Overview
This document gives a description of the RESTful web services exposed on the Creditswitch Platform which enables business partners sell different products and track their account. Our RESTful web service mainly uses data in JSON format for both input and output operations.
Http request header should be set to application/json or the corresponding data type.
For technical assistance with the CSW API contact <NAME_EMAIL>.
The subject line should be CSW API:(ISSUE SNIPPET) to enable swift response and support.

Integration flow
This section contains a description of recommended integration flow which helps minimize the risk of disputing transactions and also increase customer confidence in the system.

Step One
If the service has a method for validating the customer, that method should be used first in order to make sure the customer is properly identified on provider platform and that products customer wishes to purchase are available at the moment.

Step Two
In case step 1 is successfully completed, a Vend request can be sent. If the HTTP status of the response is OK, then you can check the request status through status field which can be Successful or not. In either case, the request has been processed

Step Three
Possible alternative scenarios to an ideal step 2 include a Response timeout or a Connection error or any other error defined in our response

In case of an error, you can safely resend the vend request – however, the request id must be updated.
If a response with HTTP status OK is received, you can consider vend to have been carried out.
Step Four
In case the request returns with an error response multiple times, the client needs to contact Creditswitch support in order to resolve the issue.

Get Merchant Info
Get Allowed Services operation enables Creditswitch integrator to fetch current info about integrator’s account, such as the balance, allowed ip addresses, available services, and the discount given for each service.

Service	Merchant Info
Request URL	baseUrl/api/v1/mdetails
HTTP Method	POST
Request Headers	application/json
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps

Sample Request Body

{"loginId":1234, "key":"0688378ec7fe233","checksum":"#2y*&23UnvC19"}
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	Collection	M	An array of merchant details

"statusDescription" Body:

Parameter Name	Type	M/O	Description
name	Numeric	M	Merchant’s name supplied during production endpoint setup
balance	String	M	Merchant’s name supplied during production endpoint setup
status	String	M	Status of the Merchant’s account
email	String	M	Email of the merchant
allowedIps	Collection	M	Array of Ip’s configured for merchant
serviceDetail	Collection	M	A multidimensional array of (service ids and the corresponding commissions) for each
transactionsToday	String	M	Total daily transaction count for merchant
Sample Response Body

{"statusCode":"00",
  "statusDescription":
     {"name":" Test Test ","balance":"2855.00","status":"active","email":"<EMAIL> ",
      "allowed_ips":["127.0.0.1"],"serviceDetail":[["A01E","5.00"],["A02E","5.00"],
       ["A03E","5.00" ],["A04E","5.00"],["D01D","0.00"],["D02D","0.00"],
        ["D03D","0.00"],["D04D","0.00"]],"transac tionsToday":0}
       }
                                        
Requery
Requery is used to check the status of an existing transaction.

Service	Requery
Request URL	baseUrl/api/v1/requery
HTTP Method	GET (QUERY STRING)
Request Headers	application/json
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Requery id provided during integration
key	String	M	Requery (public) key
requestId	String	M	Unique requery Id for the request. Maxlength (36characters)
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.

Sample Request Body

"BASEURL/api/v1/requery?loginId=17899&serviceId=A01E&key=f7a2b427ded4825675b306741ff27a9243b938faa7fb2db2a5615fd&requestId=17425905"
                                        
Response Body for Airtime and Data
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	Collection	M	An array of merchant details
mReference	String	M	Unique transaction id supplied by the merchant for the request
tranxReference	Numeric	M	Creditswitch’s unique id generated for the transaction
recipient	String	M	Customer's phone number/ smartcard number/ meter number
amount	Numeric	M	Amount
confirmCode	Numeric	M	Confirmation code returned by the network. Sent only for successful transactions
network	String	M	The actual network fulfilled by the Telco
tranxDate	Numeric	M	Date at which transaction was carried out
Sample Response Body


{"statusCode": "00","statusDescription": "Successful", "mReference": "12345","tranxReference": ***************,
    "recipient": "***********","amount": "900.00","network": "MTN","tranxDate": "2020-03-10 15:54:31"
       }
                                        
Response Body for Electricity
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	Collection	M	An array of merchant details
mReference	String	M	Unique transaction id supplied by the merchant for the request
tranxReference	Numeric	M	Creditswitch’s unique id generated for the transaction
recipient	String	M	Customer's phone number/ smartcard number/ meter number
amount	Numeric	M	Amount
token	Numeric	O	Transaction token returned only for electricity
tranxDate	Numeric	M	Date at which transaction was carried out
Sample Response Body


{"statusCode": "00","statusDescription": "Successful", "mReference": "12345","tranxReference": ***************,
    "recipient": "***********","amount": "900.00","token": "01008669212634221201","tranxDate": "2020-03-10 15:54:31"
       }
                                        
Airtime Vend Request
Service to perform airtime top-up of customer’s phone number through merchant’s account

Service	Merchant Info
Request URL	baseUrl/api/v1/mvend
HTTP Method	POST
Request Headers	application/json
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
requestId	String	M	Unique transaction Id for the request. Maxlength (36characters)
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.
amount	Numeric	M	Amount with which to top-up amount customers phone Maximum 100000
recipient	Numeric	M	Customers phone number to top-up
date	String	M	Date-time at which the request was sent .
checksum	Numeric	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps

Sample Request Body

{"loginId": 1234,
  "key":"0688378ec7fe233","requestId":"11345","serviceId":"A01E", "amount":"100", "recipient":"***********",
   "date":"2-Jul-2017 18:30 GMT","checksum":"#2y*&23UnvC19" }
                                        

Sample JAMB Request Body

{"loginId": 1434,
  "key":"0688378ec7fe233","requestId":"11345","serviceId":"A01E", "amount":"4000", "recipient":"***********",
   "date":"2-Jul-2017 18:30 GMT","checksum":"#2y*&23UnvC19" , "confirm code":"kvrbv3fjrv", "email":"<EMAIL>", "jambType":"jambutme"}
                                        

Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
mReference	String	M	Unique transaction id supplied by the integrator for the request
tranxReference	Numeric	M	Creditswitch’s unique id generated for the transaction
recipient	String	M	Customers number on the network to which top-up was sent
amount	Numeric	M	Amount credited the customer
confirmCode	Numeric	M	Confirmation code returned by the network. Sent only for successful transactions
Network	String	M	The actual network fulfilled by the Telco
tranxDate	Numeric	M	Date at which transaction was carried out

Sample Response Body

{"statusCode":"00","statusDescription":"Successful","mReference":"11345",
       "tranxReference":" 123452114", "recipient":"***********", "amount":"100",
        "confirmCode":"R20170707.2150.234565","network":"MTN",tranxDate":"21-06-2017 10:25 am"}}
                                        
Data Plans
This service provides all available and active data plans based on the serviceId provided.

Service	DATA PLANS RETRIEVAL
Request URL	baseUrl/api/v1/mdataplans
HTTP Method	POST
Request Headers	application/json
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.
key	String	M	Merchants (public) key

Sample Request Body

{"loginId":1234,"serviceId":"D02D","key":"0688378ec7fe233" }
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
serviceId	String	M	The serviceId provided in the request body
productId	String	M	The corresponding productId
dataPlan	String	M	An array of all data plans available for the supplied serviceId

Sample Response Body

{"statusCode": "00","statusDescription": "successful","serviceId": "D02D","dataPlan": [
 {"amount": 50,"databundle": "25MB","validity": "1day","productId": "9MO-25MB-15"},{"amount": 100,"databundle": "100MB","validity": "1day","productId": "9MO-100MB-1"},
 {"amount": 200,"databundle": "650MB","validity": "1day","productId": "9MO-650MB-2"},{"amount":1000,"data bundle":"1GB","validity":"30days,"productId": "9MO-1GB-3"},
 {"amount":1200,"databundle":"1.5GB","validity":"30days,productId": "9MO-1.5GB-4"},{"amount": 1500,"databundle": "7GB","validity": "7days","productId": "9MO-7GB-16"},
 {"amount":2000,"databundle":"4.5GB","validity":"30days,"productId": "9MO-4.5GB-5"},{"amount":3000,"databundle":" 4GB","validity":"30days,"productId": "9MO-4GB-6"},
 {"amount":4000,"databundle":"5.5GB","validity":"30days,"productId": "9MO-5.5GB-7"},{"amount":8000,"databundle":"11.5GB","validity":"30days,"productId": "9MO-11.5GB-8"},
 {"amount":10000,"databundle":"15GB", "validity":"30days,"productId": "9MO-15GB-9"},{"amount":18000,"databundle":"27.5GB","validity":"30days,"productId": "9MO-27.5GB-10"},
 {"amount": 27500,"databundle":"30GB","validity":"90days,"productId": "9MO-30GB-11"},{"amount":55000,"databundle":"60GB","vali dity":"180days,productId": "9MO-60GB-12"},
 {"amount":84992,"databundle":"100GB","validity":"100days,"productId": "9MO-100GB-13"},{"amount":11 0000,"databundle":"120GB","validity":"365days,"productId": "9MO-120GB-14"}]}
                                        
Data Vend Request
Service to perform data top-up of customer’s phone number through merchant merchant’s account

Service	Merchant Info
Request URL	baseUrl/api/v1/dvend
HTTP Method	POST
Request Headers	application/json
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchant’s (public) key supplied during integration
requestId	String	M	Unique transaction Id for the request. Maxlength (36characters)
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.
amount	Numeric	M	Amount with which to top-up amount customers phone Maximum 100000
recipient	Numeric	M	Customers phone number to top-up
date	String	M	Date-time at which the request was sent .
productId	String	M	the identifier of each data bundle e.g MTN-25MB-10.
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps

Sample Request Body

{"loginId": "1234","key": "f7a2b427dedbdbdc4825675","requestId": "abcde1234","serviceId": "D04D",
"recipient": "080xxxxxxxx","amount": "50.00","productId": "MTN-25MB-10","date": "13-11-2019T09:28:38.273+01:00",
"checksum": "JDJ5JDEwJGRQZ0Y4UWZkUnFNNFQzaXpnOWsuOWVWTkVVRS9QNC85OU9IR2lVYVNwTUYyY05NLlB1Qjd1" }
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
mReference	String	M	Unique transaction id supplied by the integrator for the request
tranxReference	Numeric	M	Creditswitch’s unique id generated for the transaction
recipient	String	M	Customers number on the network to which top-up was sent
amount	Numeric	M	Amount credited the customer
confirmCode	Numeric	M	Confirmation code returned by the network. Sent only for successful transactions
network	String	M	The actual network fulfilled by the Telco
tranxDate	Numeric	M	Date at which transaction was carried out

Sample Response Body

{"statusCode": "00","statusDescription": "Successful","mReference": "abcde1234","tranxReference": "12345678",
"recipient": "080xxxxxx","amount": "50.00","confirmCode": "1234567890","network": "MTN",
"tranxDate": "17-01-2020 12:01 pm"}}
                                        
International Services
List International Countries
This Lists all the countries supported for international bill payments.

Service	
Request URL	baseUrl/intl_services/get_countries?key={{publicKey}}&loginId={{loginId}}
HTTP Method	GET
Request Headers	application/json
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key

Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
data	String	M	Contains the dataset of countries supported

Sample Response Body

    
                                                {
    'statusCode': '00',
    'statusDescription': 'successful',
    'data': {
        'USD': {
            'country': 'United State Of America',
            'currency': 'USD',
            'currency_code': '840'
        },
        'GBP': {
            'country': 'United Kingdom',
            'currency': 'GBP',
            'currency_code': '826'
        },
        'CAD': {
            'country': 'Canada',
            'currency': 'CAD',
            'currency_code': '124'
        }
    }
}

    
List International Services for Country
This Lists all the services supported for international bill payments by a country.

Service	
Request URL	baseUrl/intl_services/get_services?key={{publicKey}}&loginId={{loginId}}& currency={{usd}}
HTTP Method	GET
Request Headers	application/json
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
data	String	M	Contains the dataset of services supported by a particular country

Sample Response Body


  {
    'statusCode': '00',
    'statusDescription': 'successful',
    'data': [
        {
            'service_code': 'INT1',
            'service_name': 'AT&T-Mobile Top Up',
            'currency': 'USD',
            'provider_min_amount': '15',
            'provider_max_amount': '100',
            'hasPackages': 0,
            'packages': []
        },
        {
            'service_code': 'INT2',
            'service_name': 'T-Mobile-Mobile Top Up',
            'currency': 'USD',
            'provider_min_amount': '10',
            'provider_max_amount': '100',
            'hasPackages': 0,
            'packages': []
        },
        {
            'service_code': 'INT3',
            'service_name': 'Verizon Wireless-Mobile Top Up',
            'currency': 'USD',
            'provider_min_amount': '15',
            'provider_max_amount': '100',
            'hasPackages': 0,
            'packages': []
        }
    ]
}

Get Exchange Rate
Get exchange rate for a particular currency.

Service	
Request URL	baseUrl/intl_services/exchange_rate?key={{publicKey}}&loginId={{loginId}}& currency=usd&amount=2
HTTP Method	GET
Request Headers	application/json
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
currency	String	M	The currency you are converting to. it must be part of the countries from the get countries API call.
amount	Numeric	M	The currency amount you want to convert.

Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
data	String	M	Contains the dataset of the conversion

Sample Response Body


  {
    'statusCode': '00',
    'statusDescription': 'successful',
    'data': {
        'currency_name': 'USD',
        'currency_rate': 2980
    }
}

Vend International Service
Make Payment for International service.

Service	
Request URL	baseUrl/intl_services/vend
HTTP Method	POST
Request Headers	application/json
Sample Request Body


                                                {
    'loginId': '{{loginId}}',
    'key': '{{publicKey}}',
    'serviceId': 'INT5',
    'amount': '10',
    'requestId': '{{randomsomethingdigit}}',
    'recipient': '447760929652'
}
                                        
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
requestId	Numeric	M	unique id to identify transaction
key	String	M	Merchants (public) key
serviceId	String	M	The serviceId of the plan. it must be part of the services from the get services API call.
amount	Numeric	M	The currency amount of the service.

Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
detail	String	M	Contains the details of the payment
detail.extraDetails	Array	M	This can be empty, if a direct top up is done this value would be empty, if a pin recharge is done values would be returned.

Sample Response Body


  {
    "statusCode": "00",
    "statusDescription": "Successful",
    "detail": {
        "tranxReference": "3240606165442171768",
        "mReference": "27aa2545a0650287",
        "recipient": "************",
        "amount": "10",
        "converted_currency_value": 19485,
        "network": "ASDA Mobile-Mobile Top Up",
        "extraDetails": {
            "confirmCode": "***********",
            "pin": "**************",
            "serial": "************",
            "message": "To redeem code There are 3 ways to activate your ASDA Mobile-Mobile Top Up code:  Online:  1. Go to your My3 account.  2. Log in with your mobile number, Broadband number, or Username and Password.  3. Follow the instructions on the page and enter your 14-digit top up code. Calling:\n\n                            Call 7823 from the phone you wish to top up.\n                            When asked by the operator, enter your 14-digit top up code.\n                            Texting:\n                            Open a new text message to 75712.\n                            In the message, type top up + your 14-digit top up code."
        }
    }
}

Send SMS
Service to send single and bulk SMS

Single SMS


Send SMS to a single recipient



Service	Merchant Info
Request URL	baseUrl/api/v1/sendsms
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
senderId	String	M	Sender Id for the send SMS request.
msisdn	String	M	Recipient / phone number. Ex: ***********
amount	Numeric	M	Amount with which to top-up amount customers phone Maximum 100000
messageBody	Text	M	Message body for send SMS request
transactionRef	String	M	Unique transaction Id for the request. Maxlength (35characters)
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps

Sample Request Body

{"loginId":"1234","key":"f7a2b42…", "senderId":"TEST-NG",
 "msisdn":"***********", "messageBody":"Keep calm and test", "transactionRef":"008k97658891", "checksum":"JDhJk…"}
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
cost	Numeric	M	Cost of sent SMS (in naira)
tranxReference	Numeric	M	Merchant/client’s initial transaction reference
transactionId	Numeric	M	Creditswitch’s unique id generated for the transaction

Sample Response Body

{"statusCode":"00","statusDescription":"successful","cost":2.5,
 "transactionRef":"pp0p88097976 58891","transactionId":"167241535005733"}
                                        
Bulk SMS


Send SMS to a bulk recipients


Service	Merchant Info
Request URL	baseUrl/api/v1/sendsms
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
senderId	String	M	Sender Id for the send SMS request.
msisdn	String	M	An array of Recipient / phone number. Ex: ***********
amount	Numeric	M	Amount with which to top-up amount customers phone Maximum 100000
messageBody	Text	M	Message body for send SMS request
transactionRef	String	M	Unique transaction Id for the request. Maxlength (35characters)
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps

Sample Request Body

{"loginId":"60789","key":"$2y$10$J39dLRgZ5vsIhu0030YVjuaSMirHalM8b0JBIpG0Jt9RCcOH1KEEu","senderId":"Creditswtch", 
"msisdn":[09034575188","08123748177"], "messageBody":"Testing", "transactionRef":"12ddsa73sde2hsdkklo7", "checksum":"JDJ5JDEwJGNZVHUuQ2l4SkVsV0VsaTVwSi9GYnUubEM4eFdOcHR0SERRLmJ2ZFdxQko0STloNC5hSEJ1"}
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
cost	Numeric	M	Cost of sent SMS (in naira)
tranxReference	Numeric	M	Merchant/client’s initial transaction reference
transactionId	Numeric	M	Creditswitch’s unique id generated for the transaction

Sample Response Body

{"statusCode":"00","statusDescription":"successful","cost":2.5,
 "transactionRef":"pp0p88097976 58891","transactionId":"167241535005733"}
                                        
Insurance
Service to Pay for Insurance This web service is in two phases: (1) get product codes (2) vend – can take place.

1) Insurance GET PACKAGES
Service	Insurance Packages
Request URL	{{baseUrl}}insurance/packages
HTTP Method	GET
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key

Sample Request Body


{
    "loginId": "{{loginId}}",
    "key": "{{publicKey}}"
}                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body


                                                {
    "statusCode": "00",
    "statusDescription": {
        "items": {
            "insuranceName": [
                "LAW UNION & ROCK  INSURANCE"
            ],
            "insuranceType": [
                {
                    "name": "TRICYCLE",
                    "amount": "2500"
                },
                {
                    "name": "PRIVATE_MOTOR",
                    "amount": "5000"
                },
                {
                    "name": "COMMERCIAL",
                    "amount": "7500"
                }
            ],
            "vehicleColor": [
                "ASH",
                "BRONZE",
                "BEIGE",
                "BLACK",
                "BLUE",
                "BROWN",
                "CREAM",
                "CUSTOM",
                "GOLD",
                "GREEN",
                "GREY",
                "NAVY BLUE",
                "ORANGE",
                "PEACH",
                "PINK",
                "PURPLE",
                "RED",
                "SILVER",
                "VIOLET",
                "WHITE",
                "WIINE",
                "YELLOW"
            ],
            "yearOfMake": [
                "1990",
                "1991",
                "1992",
                "1993",
                "1994",
                "1995",
                "1996",
                "1997",
                "1998",
                "1999",
                "2000",
                "2001",
                "2002",
                "2003",
                "2004",
                "2005",
                "2006",
                "2007",
                "2008",
                "2009",
                "2010",
                "2011",
                "2012",
                "2013",
                "2014",
                "2015",
                "2016",
                "2017",
                "2018",
                "2019",
                "2020"
            ]
        }
    }
}
                                            
                                        
2) INSURANCE RECHARGE
Service	Insurance Account vend
Request URL	{{baseUrl}}insurance/pay
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
requestId	Numeric	M	Unique transaction Id for the request. Maxlength (36characters)
ServiceId	String	M	INS0
Insured Name	String	M	Customer Name.
Insurance Type	String	M	Insurance Type gotten from the get Insurance package API Call
chassis_number	Numeric	M	Chassis Number of the vehicle to be Insured
engine_number	String	M	Engine Number of the vehicle to be Insured
plate_number	String	M	Plate Number of the vehicle to be Insured
vehicle_make	String	M	Make of the vehicle to be Insured, e.g Toyota, Ford
vehicle_model	Numeric	M	Model of the vehicle to be Insured, e.g Camry, Matrix
vehicle_color	String	M	Color of the vehicle to be Insured, e.g Blue, Black
year_of_make	Numeric	M	Year of the vehicle to be Insured, e.g Blue, Black
email	String	M	Email of the Customer
contact_address	Numeric	M	Contact Address of the customer

Sample Request Body


{
    "loginId": "{{loginId}}",
    "key": "{{publicKey}}",
    "requestId": "{{random12digit}}",
    "serviceId": "INS0",
    "insured_name": "David OG",
    "insurance_type": "PRIVATE_MOTOR",
    "chassis_number": "7338388393951",
    "engine_number": "1234rtrg5g5g",
    "plate_number": "AS53333Rfr5",
    "customerId": "08047621639",
    "amount": "5000",
    "vehicle_make": "TOYOTA",
    "vehicle_model": "CAMRY",
    "vehicle_color": "BLACK",
    "year_of_make": "2010",
    "email": "<EMAIL>",
    "contact_address": "dkij"
}
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body


{
    "statusCode": "00",
    "statusDescription": "Successful",
    "status": true,
    "result": {
        "amount": "100.00",
        "message": "transaction Successful",
        "transId": "929281645462237",
        "date": "2022-02-21 16:50:40",
        "package": "Showmax Mobile",
        "subscriptionPeriod": "1",
        "subscriptionType": "mobile_only",
        "validUntil": "2025-02-21 16:50:39 UTC",
        "voucherCode": "788623",
        "captureUrl": "https://secure.showmax.io/payment/subscriptions/access_code"
    }
}
                                        
Showmax
Service to recharge Showmax This web service is in two phases: (1) get product codes (2) vend – can take place.

1) SHOWMAX GET PACKAGES
Service	Showmax Packages
Request URL	{{baseUrl}}showmax/packages
HTTP Method	GET
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key

Sample Request Body


{
    "loginId": "{{loginId}}",
    "key": "{{publicKey}}"
}                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body


                                                {
    "statusCode": "00",
    "statusDescription": {
        "items": [
            {
                "name": "Showmax Mobile",
                "subscriptionPeriod": 1,
                "price": 1200,
                "type": "mobile_only"
            },
            {
                "name": "Showmax Mobile",
                "subscriptionPeriod": 3,
                "price": 2499,
                "type": "mobile_only"
            },
            {
                "name": "Showmax Mobile",
                "subscriptionPeriod": 6,
                "price": 4299,
                "type": "mobile_only"
            },
            {
                "name": "Showmax Mobile",
                "subscriptionPeriod": 12,
                "price": 7200,
                "type": "mobile_only"
            },
            {
                "name": "Showmax",
                "subscriptionPeriod": 1,
                "price": 1200,
                "type": "full"
            },
            {
                "name": "Showmax",
                "subscriptionPeriod": 3,
                "price": 5999,
                "type": "full"
            },
            {
                "name": "Showmax",
                "subscriptionPeriod": 6,
                "price": 10400,
                "type": "full"
            },
            {
                "name": "Showmax",
                "subscriptionPeriod": 12,
                "price": 17400,
                "type": "full"
            },
            {
                "name": "Showmax Pro Mobile",
                "subscriptionPeriod": 1,
                "price": 3200,
                "type": "sports_mobile_only"
            },
            {
                "name": "Showmax Pro Mobile",
                "subscriptionPeriod": 3,
                "price": 9600,
                "type": "sports_mobile_only"
            },
            {
                "name": "Showmax Pro Mobile",
                "subscriptionPeriod": 6,
                "price": 19200,
                "type": "sports_mobile_only"
            },
            {
                "name": "Showmax Pro Mobile",
                "subscriptionPeriod": 12,
                "price": 38400,
                "type": "sports_mobile_only"
            },
            {
                "name": "Showmax Pro",
                "subscriptionPeriod": 1,
                "price": 6300,
                "type": "sports_full"
            },
            {
                "name": "Showmax Pro",
                "subscriptionPeriod": 3,
                "price": 18900,
                "type": "sports_full"
            },
            {
                "name": "Showmax Pro",
                "subscriptionPeriod": 6,
                "price": 37800,
                "type": "sports_full"
            },
            {
                "name": "Showmax Pro",
                "subscriptionPeriod": 12,
                "price": 75600,
                "type": "sports_full"
            }
        ]
    }
}
                                        
2) SHOWMAX RECHARGE
Service	Showmax Account vend
Request URL	{{baseUrl}}showmax/pay
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
ServiceId	String	M	SOMX
customerNo	String	M	Phone Number.
Amount	Numeric	M	Amount of the package gotten from the get SHOWMAX package API Call
requestId	Numeric	M	Unique transaction Id for the request. Maxlength (36characters)
subscriptionType	Numeric	M	Type of Subscription, gotten from the get Packages API call
invoicePeriod	Numeric	M	This is the value of subscriptionPeriod gotten from the get SHOWMAX package API call
packageName	Numeric	M	Name of package gotten from the get SHOWMAX package API call

Sample Request Body


                                                {
    "loginId": "{{loginId}}",
    "key": "{{publicKey}}",
    "serviceId" : "SOMX",
    "customerNo":"***********",
    "amount": 100,
    "requestId" : "{{random12digit}}",
    "subscriptionType" : "mobile_only",
    "invoicePeriod" : "1",
    "packageName" : "Showmax Mobile"
}
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body


{
    "statusCode": "00",
    "statusDescription": "Successful",
    "status": true,
    "result": {
        "amount": "100.00",
        "message": "transaction Successful",
        "transId": "929281645462237",
        "date": "2022-02-21 16:50:40",
        "package": "Showmax Mobile",
        "subscriptionPeriod": "1",
        "subscriptionType": "mobile_only",
        "validUntil": "2025-02-21 16:50:39 UTC",
        "voucherCode": "788623",
        "captureUrl": "https://secure.showmax.io/payment/subscriptions/access_code"
    }
}
                                        
NOTE: voucherCode is what the customer would need to activate subscription

Cable TV Startimes
Service to recharge Startimes cable TV account. This web service is in two phases: (1) validate – an endpoint to validate smart card code must be queried before (2) vend – can take place.

1) STARTIMES FETCH PRODUCTS
Service	Fetch Startimes Product
Request URL	baseUrl/api/v1/startimes/fetchProductList
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key

Sample Request Body

{
   "loginId":"1234",
   "key":"f7a2b..."
}
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body

{
    "statusCode": "00",
    "statusDescription": {
        "items": [
            {
                "availablePricingOptions": [
                    {
                        "price": "90",
                        "invoicePeriod": ""
                    }
                ],
                "code": "DTT_Nova_Daily",
                "name": "DTT_Nova_Daily"
            },
            {
                "availablePricingOptions": [
                    {
                        "price": "160",
                        "invoicePeriod": ""
                    }
                ],
                "code": "DTT_Basic_Daily",
                "name": "DTT_Basic_Daily"
            },
        ]
    }
}
1) SMARTCARD CODE VALIDATION
Service	Startimes Account validate
Request URL	baseUrl/api/v1/starvalidate1
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps
smartCardCode	String	M	Smart card code.

Sample Request Body

{"loginId":"1234","key":"f7a2b...","checksum":"JDJhJDEw…","smartCardCode":"********* 66"}
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body

{"statusCode": "00",  "statusDescription": "successful", "balance": 2644,
               "customerName": "Mr Tester CSW",   "smartCardCode": "***********" }
                                        
2) SMARTCARD RECHARGE
Service	Startimes Account vend
Request URL	baseUrl/api/v1/starvend1
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps
smartCardCode	String	M	Smart card code.
fee	Numeric	M	Amount to recharge smart card
transactionRef	Numeric	M	Unique transaction Id for the request. Maxlength (36characters)

Sample Request Body

{"loginId":"1234","key":"f7a2b4….","checksum":"JDJhJDEw…",
 "smartCardCode":"9200000 2866", "fee":"400","transactionRef":"232pxz"}
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body

{"statusCode": "00", "statusDescription": "successful",
 "details": "succeeded, smart card empty,mobile phone empty,amount NGN 1.00,balance empty,transaction id 940020180823085017270261,product name empty,product description empty .",
  "transactionNo": "940020180823085017270261"}
                                        
Cable TV: Multichoice(DSTV & GOTV)
Service to pay for an existing subscription on Multichoice (DSTV and GOTV) cable TV account. Basically, there are three (3) phases for this web service – validation of smart card number, fetching available multichoice product codes and account vending.

1) VALIDATE CUSTOMER NUMBER/SMARTCARD NUMBER
Service	Validate Multichoice SmartCard/Customer Number
Request URL	baseUrl//api/v1/cabletv/multichoice/validate
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps
customerNo	String	M	Smart card number or Customer Number.
serviceId	String	M	Multichoice service type – ‘dstv’ or ‘gotv’.

Sample Request Body

{"loginId":"1234","key":"f7a2b427de…","checksum":"JDJhJDE…","customerNo":"***********","serviceId":"dstv"}
                                        
Response Body:
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	json	M	Corresponding details retrieved after validating customer number or smartcard number. It holds information such as customer name, invoice period, next due date etc.

Sample Response Body

{
    "statusCode": "00",
    "statusDescription": {
        "customerNo": 300846162,
        "firstname": "AYOBAMI OJO",
        "lastname": "",
        "customerType": "GOTV",
        "invoicePeriod": "",
        "dueDate": "2021-10-26T13:32:11.227+01:00",
        "amount": 2500
    }
}
                                        
2) FETCH MULTICHOICE PRODUCT CODES
This can be used to retrieve available multichoice products. Each product has a code, description and price. Product codes can be fetched by either of these two methods – finding available standalone products or finding available add-ons for a selected product.

a). Find Standalone Products

Service	Find Standalone Products
Request URL	baseUrl//api/v1/cabletv/multichoice/fetchproducts
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
serviceId	String	M	Multichoice service type – ‘dstv’ or ‘gotv’.

Sample Request Body

{"loginId":"1234","key":"f7a2b427d..","serviceId":"dstv"}
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	json	M	statusDescription is a json that has embedded in it an ‘items’ object, which contains available products. items an array of composite objects – code, invoicePeriods, price, name, description
Sample Response Body

{"statusCode":"00","statusDescription":{"items":[{"availablePricingOptions":[{"monthsPaidFor":1,"price":2000,"invoicePeriod":1},
{"monthsPaidFor":2,"price":4000,"invoicePeriod":1},{"monthsPaidFor":3,"price":6000,"invoicePeriod":1},{"monthsPaidFor":4,"price":8000,"invoicePeriod":1},{"monthsPaidFor":5,"price":10000,"invoicePeriod":1},
{"monthsPaidFor":6,"price":12000,"invoicePeriod":1},{"monthsPaidFor":7,"price":14000,"invoicePeriod":1},{"monthsPaidFor":8,"price":16000,"invoicePeriod":1},{"monthsPaidFor":9,"price":18000,"invoicePeriod":1},
{"monthsPaidFor":10,"price":20000,"invoicePeriod":1},{"monthsPaidFor":12,"price":22000,"invoicePeriod":12}],"code":"ACSSE36","name":"DStv Access","description":" "},{"availablePricingOptions":[{"monthsPaidFor":1,"price":4000,"invoicePeriod":1},
{"monthsPaidFor":2,"price":8000,"invoicePeriod":1},{"monthsPaidFor":3,"price":12000,"invoicePeriod":1},{"monthsPaidFor":4,"price":16000,"invoicePeriod":1},{"monthsPaidFor":5,"price":20000,"invoicePeriod":1},
{"monthsPaidFor":6,"price":24000,"invoicePeriod":1},{"monthsPaidFor":7,"price":28000,"invoicePeriod":1},{"monthsPaidFor":8,"price":32000,"invoicePeriod":1},{"monthsPaidFor":9,"price":36000,"invoicePeriod":1},{"monthsPaidFor":10,"price":40000,"invoicePeriod":1},
{"monthsPaidFor":12,"price":44000,"invoicePeriod":12}],"code":"COFAME36","name":"DStv Family","description":" "},{"availablePricingOptions":[{"monthsPaidFor":1,"price":6800,"invoicePeriod":1},{"monthsPaidFor":2,"price":13600,"invoicePeriod":1},
{"monthsPaidFor":3,"price":20400,"invoicePeriod":1},{"monthsPaidFor":4,"price":27200,"invoicePeriod":1},{"monthsPaidFor":5,"price":34000,"invoicePeriod":1},{"monthsPaidFor":6,"price":40800,"invoicePeriod":1},{"monthsPaidFor":7,"price":47600,"invoicePeriod":1},
{"monthsPaidFor":8,"price":54400,"invoicePeriod":1},{"monthsPaidFor":9,"price":61200,"invoicePeriod":1},{"monthsPaidFor":10,"price":68000,"invoicePeriod":1},{"monthsPaidFor":12,"price":74800,"invoicePeriod":12}],"code":"COMPE36","name":"DStv Compact","description":" "},
{"availablePricingOptions":[{"monthsPaidFor":1,"price":10650,"invoicePeriod":1},{"monthsPaidFor":2,"price":21300,"invoicePeriod":1},{"monthsPaidFor":3,"price":31950,"invoicePeriod":1},{"monthsPaidFor":4,"price":42600,"invoicePeriod":1},
{"monthsPaidFor":5,"price":53250,"invoicePeriod":1},{"monthsPaidFor":6,"price":63900,"invoicePeriod":1},{"monthsPaidFor":7,"price":74550,"invoicePeriod":1},{"monthsPaidFor":8,"price":85200,"invoicePeriod":1},{"monthsPaidFor":9,"price":95850,"invoicePeriod":1},
{"monthsPaidFor":10,"price":106500,"invoicePeriod":1},{"monthsPaidFor":12,"price":117150,"invoicePeriod":12}],"code":"COMPLE36","name":"DStv Compact Plus","description":" "},{"availablePricingOptions":[{"monthsPaidFor":1,"price":15800,"invoicePeriod":1},
{"monthsPaidFor":2,"price":31600,"invoicePeriod":1},{"monthsPaidFor":3,"price":47400,"invoicePeriod":1},{"monthsPaidFor":4,"price":63200,"invoicePeriod":1},{"monthsPaidFor":5,"price":79000,"invoicePeriod":1},{"monthsPaidFor":6,"price":94800,"invoicePeriod":1},"code":"FTAE36","name":"DStv FTA Plus","description":" "}]}}
                                        
b). Find Product Add-ons for selected product

Service	Find Product Add-ons
Request URL	baseUrl//api/v1/cabletv/multichoice/productaddons
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
serviceId	String	M	Multichoice service type – ‘dstv’ or ‘gotv’.

Sample Request Body

{
    "loginId": "1234",
    "key": "f7a2b427d2…",
    "serviceId": "dstv",
    "primaryProductCode": "ACSSE36"
}
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	statusDescription is a string that has embedded in it an ‘items’ object, which contains available products. items an array of composite objects – code, price, name, description
Sample Response Body

{"statusCode":"00","statusDescription":{"items":[{"code":"ASIADDE36","price":5050,
 "name":"Asian Add-on","description":" "},{"code":"HDPVRE36","price":2200,"name":"HDPVR\/XtraView","description":" "}]}}
                                        
3) VEND MULTICHOICE ACCOUNT
Now you can either RENEW an existing subscription or CHANGE PACKAGE to another

Service	Vend Multichoice Account
Request URL	baseUrl/api/v1/cabletv/multichoice/vend
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps
serviceId	String	M	Multichoice service type – ‘dstv’ or ‘gotv’
transactionRef	String	M	Unique transaction Id for the request. Maxlength (36characters)
customerNo	String	M	Customer number retrieved at validation stage.
customerName	String	M	Customer name retrieved at validation stage.
productsCodes	Array of strings	M	(For renew option leave as empty array [] ) An array of product/addon codes selected. Codes can be fetched from the related method calls (find standalone products and find product addons)
amount	Numeric	M	(For renew option amount is gotten from the validate customer API end point)Total transaction amount – in case of a 12 month invoice period, the product prices need to be multiplied by 11
invoicePeriod	Numeric	M	Invoice period for which the payment is made. Retrieved when validating customer number. Can be either 1 or 12 (months)

Sample Request Body Renew

{
    "loginId": "{{loginId}}",
    "key": "{{publicKey}}",
    "serviceId": "gotv",
    "customerNo": "300846162",
    "customerName": "AYOBAMI OJO",
    "productsCodes": [ ],
    "amount": "800",
    "invoicePeriod": "1",
    "checksum": "{{checkSum}}",
    "transactionRef": "te333u9u9j3949"
}
                                        
Sample Request Body Change Package

{
    "loginId": "{{loginId}}",
    "key": "{{publicKey}}",
    "serviceId": "gotv",
    "customerNo": "300846162",
    "customerName": "CSW TEST",
    "productsCodes": [ "GOHAN" ],
    "amount": "800",
    "invoicePeriod": "1",
    "checksum": "{{checkSum}}",
    "transactionRef": "tester23kfkej3u943"
}
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	json	M	statusDescription is a json string that has embedded in it details of the transaction. If successful, it contains object such as amount, transactionNo (Creditswitch’s unique reference generated for the transaction)
Sample Response Body Renew

{
    "statusCode": "00",
    "statusDescription": {
        "message": "successful",
        "amount": "4",
        "type": "gotv",
        "package": "Renewal",
        "transactionRef": "test0bab9419g",
        "transactionNo": "***************"
    }
}
                                        
Sample Response Body Change Package

{
    "statusCode": "00",
    "statusDescription": {
        "message": "successful",
        "amount": "4",
        "type": "gotv",
        "package": "GOtv Smallie - monthly",
        "transactionRef": "test9j55216da",
        "transactionNo": "***************"
    }
}
                                        
Betting Services
This service will help to fund a betting account across different betting providers.

1) FETCH PROVIDERS
Service	This endpoint will return all the betting providers available
Request URL	{{baseUrl}}betting/providers?loginId={{loginId}}&key={{publicKey}}
HTTP Method	GET
Request Parameters
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key

Sample Request Body

{
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                        
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body

{
    "statusCode": "00",
    "statusDescription": "Successful",
    "result": {
        "B01T": "Bet9ja",
        "B02T": "BangBet",
        "B03T": "NairaBet",
        "B04T": "SupaBet",
        "B05T": "CloudBet",
        "B06T": "BetLion",
        "B07T": "1xBet",
        "B08T": "MerryBet",
        "B09T": "BetWay",
        "B10T": "BetLand",
        "B11T": "BetKing",
        "B12T": "LiveScoreBet",
        "B13T": "NaijaBet"
    }
}
                                        
1) BET ACCOUNT VALIDATION
Service	Betting Account validate
Request URL	{{baseUrl}}betting/validate
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
customerId	String	M	Id of Bet account
provider	String	M	name associated with fetch provider

Sample Request Body

{
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                            "customerId": "965820",
                                            "provider" : "BetKing"
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body

{"statusCode":"00","statusDescription":"Successful","result":{"status":"200","message":"Successful","name":"","username":"Arete995","type":"BetKing","customerId":"965820","reference":"***************","accountNumber":null,"phoneNumber":null,"emailAddress":null,"canVend":null,"minPayableAmount":100,"charge":0},"status":true}
                                        
2) BET ACCOUNT FUNDING
Service	Bet Account Funding
Request URL	{{baseUrl}}betting/pay
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
serviceId	String	M	B01T Is the service code for betting
customerId	String	M	Bet ID of the customer
fee	Numeric	M	Amount to fund Bet ID
Provider	string	M	Bet Provider associated with customer ID
name	string	M	name of the account associated with the Bet ID

Sample Request Body

{
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                            "serviceId" : "B01T",
                                            "customerId":"34382",
                                            "amount": 200,
                                            "name" : "OLUFEMI ISAAC BABATUNDE",
                                            "provider" : "BangBet"
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body

{"statusCode": "00",
                                             "statusDescription": "successful"
                                             {
                                            "status": true,
                                            "result": {
                                              "status": "200",
                                              "message": "Transaction Successful",
                                              "name": "Arete995",
                                              "customerId": "965820",
                                              "amount": "100.00",
                                              "transId": "**********",
                                              "date": "2021-08-24 12:23:34",
                                              "type": "BetKing"
                                            }
                                          }
                                        
Toll Services
This service will fund a user's Toll Account.

1) TOLL ACCOUNT VALIDATION
Service	Toll Account validate
Request URL	{{baseUrl}}toll/validate
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
customerId	String	M	Id of Toll account

Sample Request Body

{
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                            "customerId": "1234-00"
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body

{"statusCode":"00","statusDescription":"Successful","result":{
                                            "result": {
                                              "status": "200",
                                              "message": "Successful",
                                              "type": "LCC",
                                              "name": "TEST LCC_TESTER",
                                              "customerId": "1234-00",
                                              "reference": "***************",
                                              "accountNumber": null,
                                              "phoneNumber": null,
                                              "emailAddress": null,
                                              "canVend": true,
                                              "wallet": 2
                                            }},"status":true}
                                        
2) TOLL ACCOUNT FUNDING
Service	Toll Account Funding
Request URL	{{baseUrl}}toll/pay
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
serviceId	String	M	B01T Is the service code for betting
customerId	String	M	Bet ID of the customer
amount	Numeric	M	Amount to fund Bet ID
name	string	M	name of the account associated with the Bet ID

Sample Request Body

{
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                            "serviceId" : "B01T",
                                            "customerId":"34382",
                                            "amount": 200,
                                            "name" : "OLUFEMI ISAAC BABATUNDE",
                                            "provider" : "BangBet"
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body

{"statusCode": "00",
                                             "statusDescription": "successful"
                                             {
                                                "status": true,
                                                "result": {
                                                  "status": "200",
                                                  "message": "Successfully processed the payment request",
                                                  "name": "OLUFEMI ISAAC BABATUNDE",
                                                  "customerId": "1234-00",
                                                  "amount": "100.00",
                                                  "transId": "**********",
                                                  "date": "2021-07-19 12:55:10",
                                                  "type": "LCC"
                                                }
                                          }
                                        
Re-Query Transaction
This is to retrieve detailed information or confirm the status of a previous transaction. This service is useful for retrial implementation. Using the reference ID of an already initiated request, the final response of that transaction can be called back – in order to determine whether a retry process should be triggered or not.
Note: The first requery should be done within a minimum of three (3) minutes after initiating the transaction.

Service	Re-query Transaction
Request URL	baseUrl/api/v1/requery
HTTP Method	GET
Request Parameters
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
requestId	String	M	Unique transaction Id for the request. Maxlength (36characters)
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.

Sample Request Body

BASE_URL?loginId=17777&key=Ttdedbdc482f277243b&requestId=10B2E6F123&serviceId=A01E
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
MReference	Numeric	M	Unique transaction id supplied by the integrator for the request
tranxReference	Numeric	M	Merchant/client’s initial transaction reference
recepient	Numeric	M	Customers phone number to top-up
amount	Numeric	M	Amount with which to top-up amount customers phone Maximum 100000
network	String	M	the actual network fulfilled by the telco

Sample Response Body

{ "statusCode": "00","statusDescription": "Successful","tranxReference": 2052544278,"recipient": "08069314541",
    "amount": "50.00","network": "MTN","mReference": "06773950c8ab","transactionDate": "2018-11-26 22:17:18"}
                                        
Logical PINS
This service will purchase a logical pin for the service ID provided. NOTE : Payload may differ depending on service ID. Logical Pin services cover the following AIRTEL,GLO,9MOBILE,MTN,JAMB,NECO,WAEC,NABTECH,SPECTRANET The respective service ID's can be found in the service ID table below.

1) FETCH PINS
Service	Fetch All Pins
Request URL	{{baseUrl}}get_avail_pin_packages
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
serviceId	String	M	Corresponding service id for the request type

Sample Request Body


                                        {
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                            "serviceId": "PO4N"
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body


                                        {
                                                "statusCode": "00",
                                                "statusDescription": "successful",
                                                "pinDetail": {
                                                    "pinValues": [
                                                        {
                                                            "amount": 100
                                                        },
                                                        {
                                                            "amount": 200
                                                        },
                                                        {
                                                            "amount": 500
                                                        },
                                                        {
                                                            "amount": 1000
                                                        },
                                                        {
                                                            "amount": 1500
                                                        }
                                                    ]
                                                }
                                        }
                                        
2) LOGICAL PIN VEND
Service	Logical PINS
Request URL	baseUrl/api/v1/vend_pins
HTTP Method	POST
Request Headers	application/json
Request Parameters
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.
recipient	Numeric	M	Phone number of the merchant
requestId	Numeric	M	Merchants unique id/reference for the transaction
amount	Numeric	M	Face Value of the PIN
key	String	M	Merchants (public) key
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps

Sample Request Body

{"loginId":"38457","key":"***","checksum":"******","serviceId":"P01N",
"recipient":"080", "amount":"100","requestId":"4792sccfkdkfd8fdbxxclxcxg"}
                                        
--> -->
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
tranxReference	String	M	Creditswitch transaction reference
qty	String	M	Quantity of PINs generated (usually 1)
amount	String	M	The face value of the generated pin
network	String	M	The actual network of the PIN
detail	String	M	Contains array of pin and serial number generated by the network operator

Sample Response Body

{"statusCode": "00","statusDescription": "successful","detail": {"pin": ["614821599037390"],
"serialNo": ["903736148215990"],"network": "Airtel","qty": 1,"amount": "100","tranxReference": "354391599124667"}}

                                        
3) Validate Jamb
Service	Validate Jamb Confirm Code
Request URL	{{baseUrl}}verify_jamb_candidate
HTTP Method	GET
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
Confirm Code	String	M	Individual Student Code gotten from Jamb Board

Sample Request Body


                                        {
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                            "ConfirmCode": "a8h793b4f35f"
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body


                                        {
                                            "statusCode": "00",
                                            "statusDescription": "Successful",
                                            "result": {
                                                "gsmNo": "08000000000",
                                                "lastName": "Amunike",
                                                "firstName": "Emmanuel",
                                                "middleName": "Michael"
                                            }
                                        }
                                        
5) Vend Jamb
Service	Vend Jamb
Request URL	{{baseUrl}}vend_pins
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
Confirm Code	String	M	Individual Student Code gotten from Jamb Board
RequestId	String	M	Merchants unique id/reference for the transaction
ServiceId	String	M	Merchants id provided during integration
Amount	String	M	Face Value of the pin
Recipients	String	M	Phone number of the merchant
Checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps
email	String	M	Email of the candidate
productType	String	M	Type of Jamb service

Sample Request Body


                                        {
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                            "requestId": "{{requestId}}",
                                            "serviceId": "P07N",
                                            "amount": 4,
                                            "recipient": "08012345678",
                                            "checksum": "{{checkSum}}",
                                            "email": "<EMAIL>",
                                            "confirmCode": "1375779512",
                                            "productType": "jambutme"
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body


                                        {
                                            "statusCode": "00",
                                            "statusDescription": "successful",
                                            "detail": {
                                                "pin": "30138820169806991",
                                                "serial_no": "094089700 1698060076",
                                                "network": "JAMB",
                                                "qty": 1,
                                                "amount": 4,
                                                "tranxReference": "2310231221161190695"
                                            }
                                        }
                                        
4) Vend Other Educational Services
Service	Vend Other Educational Services
Request URL	{{baseUrl}}vend_pins
HTTP Method	POST
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
key	String	M	Merchants (public) key
RequestId	String	M	Merchants unique id/reference for the transaction
ServiceId	String	M	Merchants id provided during integration
Amount	String	M	Face Value of the pin
Recipients	String	M	Phone number of the merchant
Checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps
productType	String	M	Type of Jamb service

Sample Request Body


                                        {
                                            "loginId": "{{loginId}}",
                                            "key": "{{publicKey}}",
                                            "requestId": "{{requestId}}",
                                            "serviceId": "P06N",
                                            "amount": 4,
                                            "recipient": "08012345678",
                                            "checksum": "{{checkSum}}",
                                            "productType": "WAEC Checker"
                                        }
                                        
NOTE: ‘statusCode’ = ‘00’ in response body implies success. If otherwise, it implies failed

Sample Response Body


                                        {
                                            "statusCode": "00",
                                            "statusDescription": "successful",
                                            "detail": {
                                                "pin": "30138820169806991",
                                                "serial_no": "094089700 1698060076",
                                                "network": "WAEC",
                                                "qty": 1,
                                                "amount": 4,
                                                "tranxReference": "2310231221161190695"
                                            }
                                        }
                                        
Electricity Validation Requests
Electricity top-up requests follow a somewhat different routine. And involves two steps.
Validation of the account/meter number to which top-up should be made is first made. Only after validation of the account/meter number is Vending possible.
The validation logic, input parameters, and responses is somewhat similar for the all distribution companies, however, responses to the vending request differs for different distribution companies.
Security checksum generation is covered in the Security: Checksum section

Service	ELECTRICITY VALIDATION
Request URL	baseUrl/api/v1/evalidate
HTTP Method	POST
Request Headers	application/json
Request Parameters
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.
customerAccountId	Numeric	M	Customers unique identifier on distributors platform e.g Account Number or Meter Number
key	String	M	Merchants (public) key
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps

Sample Request Body

{"loginId":1234,"serviceId":"E02E","customerAccountId":"**********",
    "key":"0688378ec7fe233", checksum":"#2y*&23UnvC19" }
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
detail	Collection	M	An array of merchant customer’s detail
"detail" Body:

Parameter Name	Type	M/O	Description
name	String	M	Name of customer, as supplied by electric disco
address	String	M	Address to which the meter number belongs
accountId	String	M	Customer’s Account/Meter number
providerRef	String	M	Unique reference to identify transaction. To be supplied at the vending stage

Sample Response Body

{"statusCode":"00","statusDescription":"successful",
 "detail":{"name":"MR & MRS XD MICHAEL","address":"71 XAVIER CRESCENT ",
        "accountId":"**********","providerRef":"************"}}
                                        
Electricity Vending Requests
The request parameters to the vending endpoint remains the same across distribution companies.
Pay attention though to the Vending responses. Varying responses are returned for each electric disco.
Security checksum generation is covered in the Security: Checksum section

Service	ELECTRICITY VENDING
Request URL	baseUrl/api/v1/evend
HTTP Method	POST
Request Headers	application/json
Request Body:
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.
customerAccountId	Numeric	M	Customers unique identifier on distributors platform e.g Account Number or Meter Number
amount	Numeric	M	Amount to top-up customers account/meter with
customerName	String	M	Name of Customer
requestId	Numeric	M	Merchants unique id/reference for the transaction
customerAddress	String	M	Address of the Customer
key	String	M	Merchants (public) key
checksum	String	M	Checksum computed for the request. See "Security: Checksum" section for hash computation steps

Sample Request Body

{"loginId":"1234","key":"0688378ec7fe233",
 "serviceId":"E02E","customerAccountId":"**********","requestId":**********,"amount":"1000","customerName": "MR & MRS. Test CSW",
"customerAddress": "93B, ODUDUWA CRESCENT, GRA, IKEJA, LAGOS","checksum": "$2y$10$rv89doOk0TdmGrWhyrj4gepYG6I3Toi5CSVdMV.mFmRvKBN\/\/JEE2"}
                                        
RESPONSES
PREPAID RESPONSE
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
detail	Collection	M	An array of merchant customer’s detail
"detail" Body:

Parameter Name	Type	M/O	Description
name	String	M	Name of customer, as supplied by electric disco
address	String	M	Address to which the meter number belongs
accountId	String	M	Customer’s Account/Meter number
amount	Numeric	M	Top-up amount
units	String	M	Units of power purchased
token	Numeric	M	Prepaid meter token generated on the disco’s end
tranxId	String	M	Unique Reference generated for the transaction

Sample Response Body

{"statusCode":"00","statusDescription":"successful","detail":
 {"name": "MR & MRS XD MICHAEL","address":"71 XAVIER CRESCENT ","accountId":"**********","amount":"1000",
"units":"788.14kWh","token":"01234567890123456789","tranxId":"**************"}}
                                        
POSTPAID RESPONSES
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description for the status code. 00=Successful
detail	Collection	M	An array of merchant customer’s detail
"detail" Body:

Parameter Name	Type	M/O	Description
name	String	M	Name of customer, as supplied by electric disco
address	String	M	Address to which the meter number belongs
accountId	String	M	Customer’s Account/Meter number
amount	String	M	Top Up amount
tranxId	String	M	Unique Reference generated for the transaction

Sample Response Body

{"statusCode":"00","statusDescription":"successful",
 "detail":{"name":"MR & MRS XD MICHAEL","address":"71 XAVIER CRESCENT ",
 "accountId":"**********","amount":"1000","tranxId":"************"}}
                                        
Phone Number Look Up
Service to verify the network operator of an eleven digit Nigerian mobile number. This is a premium service.

Service	PHONE NUMBER LOOKUP VALIDATION
Request URL	endpoint
HTTP Method	POST
Request Headers	application/json
Request Body
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
msisdn	String	M	Eleven Digits Phone Number. Ex ***********
key	String	M	Merchant (public) key

Sample Request Body

{"loginId":1234,"msisdn":"***********","key":"0688378ec7fe233" }
                                        
Response Body
Parameter Name	Type	M/O	Description
statusCode	Numeric	M	Status of the transaction. 00 signifies success
statusDescription	String	M	Corresponding description of the status code. 00=Successful
network	String	M	Network operator of msisdn sent in request e.g MTN

Sample Response Body

{"statusCode":"00","statusDescription":"successful","network":"MTN"}
                                        
Services
This section covers the services available to merchants, and the corresponding service codes for each service

Service	Provider	Service Code
Airtime		
Airtel	A01E
9 Mobile	A02E
Globacom	A03E
Mtn	A04E
Data		
Airtel	D01D
9 Mobile	D02D
Globacom	D03D
Mtn	D04D
Smile	D05D
NTEL	D06D
LOGICAL PINS		
Airtel	P01N
9 Mobile	P02N
Globacom	P03N
Mtn	P04N
Spectranet	P05N
WAEC	P06N
JAMB	P07N
NECO	P08N
NABTECH	P09N
Electricity		
Ikeja Electric Disco (Prepaid Accounts)	E01E
Ikeja Electric Disco (Post Accounts)	E02E
Ibadan Electric Disco (Prepaid Accounts)	E03E
Ibadan Electric Disco (Postpaid Accounts)	E04E
Eko Electric Disco (Prepaid Accounts)	E05E
Eko Electric Disco (Postpaid Accounts)	E06E
Abuja Electric Disco (Prepaid Accounts)	E07E
Abuja Electric Disco (Postpaid Accounts)	E08E
Port Harcourt Electric Disco (Prepaid Accounts)	E09E
Port Harcourt Electric Disco (Postpaid Accounts)	E10E
Kaduna Electric Disco (Prepaid Accounts)	E11E
Kaduna Electric Disco (Postpaid Accounts)	E12E
Jos Electric Disco (Prepaid Accounts)	E13E
Jos Electric Disco (Postpaid Accounts)	E14E
Enugu Electric Disco (Prepaid Accounts)	E15E
Enugu Electric Disco (Postpaid Accounts)	E16E
Kano Electric Disco (Prepaid Accounts)	E17E
Kano Electric Disco (Postpaid Accounts)	E18E
Insurance		
INSURANCE	INS0
SHOWMAX		
SHOWMAX	S0MX
Security: Checksum
The Creditswitch REST API uses an authentication scheme which utilizes the Bcrypt hashing algorithm. Each merchant has a private and public key. To authenticate a request, you first concatenate certain selected elements of the request (including the merchant’s private key) to form a string.
Next you generate the hash of the concatenated string in the previous step using the Bcrypt algorithm. Finally, get the base-64 encoding of the hash. When the system receives an authenticated request, it fetches the Creditswitch’s secret access key you claim to have, and uses it in computing the checksum in the same way you should have. If the two checksums match, the system concludes the requester must have access to the secret key, and therefore acts with the authority of the integrator to whom the key was issued.
If there is a mismatch, the request is dropped, and the system responds with an error message.

The following illustrates the construction of the requests’ checksum. The output of the Bcrypt hash is a string.

Airtime/Data Request
ConcatString = loginId + "|" + requestId + "|" + serviceId + "|" + requestAmount + "|" + privateKey + "|" + recipient;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Electric Validate Request
ConcatString = loginId + "|" + serviceId + "|" + privateKey + "|" + customerAccountId;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Electric Vend Request
ConcatString = loginId + "|" + serviceId + "|" + privateKey + "|" + customerAccountId + "|" + requestId + "|" + amount;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Merchant Details Request
ConcatString = loginId + "|" + privateKey;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Deduct Mobile Subscriber's Airtime Request
 loginId + "|" + privateKey + "|" + msisdn+ "|" + amount;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Send SMS
ConcatString = loginId + "|" + privateKey + "|" + transactionRef;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Cable TV: Startimes Validate
ConcatString = loginId + "|" + privateKey + "|" + smartCardCode;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Cable TV: Startimes Vend
ConcatString = loginId + "|" + privateKey + "|" + smartCardCode + "|" + fee;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Cable TV: Validate Multichoice SmartCard/Customer Number
ConcatString = loginId + "|" + privateKey + "|" + customerNo;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Cable TV: Vend Multichoice Account
ConcatString = loginId + "|" + privateKey + "|" + customerNo + "|" + transactionRef + "|" + amount;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Logical Pins: Vend logical Pins
ConcatString = loginId + "|" + serviceId + "|" + privateKey + "|" + requestId + "|" + amount;
Checksum = Base64(Bcyrpt(ConcatString));
                                        
Security: Checksum Generation Snippets
Sample checksum generation snippets in some selected languages. These snippets are only meant to serve as a guide.

PHP
public function getChecksumAirtimeData(){

    $loginId = "1234";

    $requestId = "112345";

    $serviceId = "A04E";

    $requestAmount = "100";

    $privateKey = "78858c42aff6b2210a2fdc637f6ebe39d53c38da95633d27f52fddeace60149162";

    $recipient = "***********";

    $concatString = $loginId ."|". $requestId."|". $serviceId."|".$requestAmount."|".$privateKey."|". $recipient;

    $checksum = base64_encode(password_hash($concatString, PASSWORD_DEFAULT) ); //PASSWORD_BCRYPT

    return $checksum;

}
public function getChecksumMertDetail(){

    $loginId = "1234";

    $privateKey = "78858c42aff6b2210a2fdc637f6ebe39d53c38da95633d27f52fddeace60149162";

    $concatString = $loginId ."|".$privateKey;

    $checksum = base64_encode(password_hash($concatString, PASSWORD_DEFAULT) ); //PASSWORD_BCRYPT

    return $checksum;
}
 
Python
import bcrypt
def getChecksumAirtimeData():
    loginId = "1234"
    requestId = "112345"
    serviceId = "A04E"
    requestAmount = "100"
    privateKey = "78858c42aff6b2210a2fdc637f6ebe39d53c38da95633d27f52fddeace60149162"
    recipient = "***********"
    concatString = loginId +"|"+requestId+"|"+serviceId+"|"+requestAmount+"|"+privateKey+"|"+recipient;
    checksum = base64.urlsafe_b64encode(bcrypt.hashpw(concatString, bcrypt.gensalt()))
    return checksum


def getChecksumMertDetail():
    loginId = "1234"
    privateKey = "78858c42aff6b2210a2fdc637f6ebe39d53c38da95633d27f52fddeace60149162"

    concatString = loginId+"|"+privateKey

     checksum = base64.urlsafe_b64encode(bcrypt.hashpw(concatString, bcrypt.gensalt()))
     return checksum 
Java
static String getChecksumAirtimeData(){
    String loginId = "1234";
    String requestId = "112345";
    String serviceId = "A04E";
    int requestAmount = 100;

    String privateKey = "78858c42aff6b2210a2fdc637f6ebe39d53c38da95633d27f52fddeace60149162";
    String recipient = "***********";
    String concatString = ""+loginId +"|"+requestId+"|"+serviceId+"|"+requestAmount+"|"+privateKey+"|"+recipient+"";
    byte[] message = Bcrypt.hashpw(concatString,Bcrypt.gensalt()).getBytes(StandardCharsets.UTF_8);
    String checksum = Base64.getEncoder().encodeToString(message);
    String checksum = Base64.getEncoder().encodeToString(message);
    return checksum;

}
static String getChecksumMertDetail(){
    String loginId = "1234";
    String privateKey = "78858c42aff6b2210a2fdc637f6ebe39d53c38da95633d27f52fddeace60149162";
    String concatString = ""+loginId+"|"+privateKey;
    byte[] message = Bcrypt.hashpw(concatString,Bcrypt.gensalt()).getBytes(StandardCharsets.UTF_8);
    String checksum = Base64.getEncoder().encodeToString(message);
    return checksum;
 }
                                        
.NET C#
public string getCheckSum() {
    string loginId = "1234";
    string requestId = "bchsbhhbfdsbsdbh";
    string serviceId = "A04E";
    string amount = "100";
    string privatekey = "XY1t9Y159hWJaETD";
    string recipient = "07027829028";
    var concatText = loginId + "|" + requestId + "|" + serviceId + "|" + amount + "|" + privatekey + "|" + recipient;
    string salt = BCryptHelper.GenerateSalt();
    var hashedText = BCryptHelper.HashPassword(concatText, salt);
    return checkSum = Convert.ToBase64String(Encoding.UTF8.GetBytes(hashedText));
} 
Error Codes and Description
RESPONSE CODES	DESCRIPTION
00	Successful
C001	Processing
V001	Bad Request
V0101	Generic Validation Error
V002	Checksum Mismatch
V003	Unrecognized service code
V004	User access blocked
V005	User account is inactive
V010	Discount not configured for merchant
V011	Airtime vend method call exception
V015	User’s balance couldn’t be retrieved
V016	Not enough funds to satisfy request
V020	Provider Login not configured for merchant
EXC001	Duplicate merchant transaction reference supplied
EXC002	Unknown exception or Service Unavailable
EXC022	Connection Error
PROG001	Programming Error
C01	User Limit Exceeded
C0101	Response Parse Error
C02	Provider Duplicate Transaction Id
C03	Batch Inactive/Provider Not Responding
C04	Response Timeout
C11	Fraud Limit on Multiple Recharges within 90 seconds
C12	Provider Side Invalid Subscriber Number
C13	Provider Side Platform Error
C20	User Account Error , Connection Error
C21	Vendor Side Authentication Error
C100	Other Errors
Response code C001 indicates that the transaction is processing, and it should be taken as successful because such transaction will be fulfilled later, in an event of service disruption at the Mobile Network Operators.

USSD SOLUTIONS
Overview
This document gives a description of the USSD web service exposed on the Creditswitch Platform which enables business partners’ display their USSD menu.
For technical assistance with the CSW API contact <NAME_EMAIL> to enable swift response and support.

Integration
This section contains a description of recommended integration flow which helps minimize the risk of disputing transactions and also increase customer confidence in the system


For a secure transfer of data between Creditswitch server and client’s server, a secured connection is required. This can be achieved via a VPN a connection, https or other secured protocols.
External USSD Application Service
This service allows a client to be able to display their preferred menu via USSD. Creditswitch will append user’s request parameters and push to the provided endpoint. A JSON response is expected for this request.

Service	Merchant Info
Request URL	To be provided by the client
HTTP Method	GET
Request Body
Parameter Name	Type	M/O	Description
msisdn	Numeric	M	Customer’s 13 digit phone number e.g. 2347055699312
input	String	M	For first dial, this fields comes with the short (without #) e.g *444 subsequently, with the user’s input e.g. 1
sessionid	String	M	This displays a unique numeric ID for each request
network	String	M	It identifies the network providers’ which includes MTN, Glo, Airtel or 9Mobile
action	String	M	This could either be begin, request, or end begin– means a new request request – means session should be continued end – means session should be ended
allinput	String	M	For first dial, this fields comes with the short (without #) e.g .*444 subsequently, the concatenation of the initial input and other user input e.g.*444*1

Sample Request Body


                                        http://CLIENT_FULL_BASEURL?msisdn=23478599658965&input=*444&sessionid=123555522&net work=MTN&action=begin&allinput=*444*1
                                            
Response Body
Parameter Name	Type	M/O	Description
message	String	M	String of message (140 characters) to be displayed to the user
action	String	M	This could either be request or end request – means session should be continued end – means session should be ended

Sample Response Body


                                        {"message": "Welcome to Creditswitch\n\n1.Airtime/Data\n2.Airtime Swap\n3.Fund Wallet\n4.Balance\n5.Transactions\n6.Change PIN",  "action":"request" }
                                             
USSD Push Notification Application Service
This API describe how a third party can initiate USSD push to subscriber mobile phone number from CreditSwitch Platform.

Service	Merchant Info
Request URL	http://BASEURL:PORT
HTTP Method	GET
Request Body
Parameter Name	Type	M/O	Description
msisdn	Numeric	M	13 digit Phone number of the subscriber that will receive the push
network	String	M	Mobile network of the subscriber
username	String	M	The username that was assigned to the third-party user during integration
password	String	M	The Password that was assigned to the third-party user during integration
msg	String	M	A 130-character text that will be display to subscribers
msg_type	String	M	This could either be begin, continue, or end begin– means apass as instruction to create new session. continue-will allow subscriber response back. End – means terminate the sessiong
sessionid	String	O	set this to empty string when msg_type is begin, for continue and end request you should return back the generated sessionid

Sample Request Body


                                        "http://BASEURL:PORT/ussd_push?msg= welcome to Creditswitch
Pro\n\n1.Win 10,000 airtime\n2.Refer a friend&msisdn=***********&username=xyz&password=xzy&msg_type=continue&network=MTN&sessionid=111192029228292"
                                            
Response Body
Parameter Name	Type	M/O	Description
msisdn	String	M	msisdn provided in the payload
sessionid	String	M	Session Id for the request
network	String	M	Network of the msisdn provided
input	String	M	user input (it could be empty if it does not require input from the user)

Sample Response Body


                                        {"msisdn":" ***********","sessionid":"111192029228292","network":"MTN","input":"1"}
                                             
UTILITY ADVANCE SERVICE
Overview
This document gives an overview of the Creditswitch Utility Advance service. This service enables business partners to consume existing services on the Creditswitch Platform in a buy now pay later fashion.
For technical assistance with the CSW API contact <NAME_EMAIL> to enable swift response and support.

Integration Process Flow
This section contains a description of recommended integration flow which helps minimize the risk of disputing transactions and also increase customer confidence in the system

1) Authentication: Begin by authenticating through the Login Endpoint to retrieve a bearer token for subsequent API calls.
2) Loan Eligibility Check: Call the Loan Eligibility Endpoint with the specified payload. This step confirms eligibility and returns potential loan offers if eligible.
3) Loan Approval: If the user accepts the terms and conditions presented in step 2, the system automatically processes the loan disbursement/approval.
4) Loan Status: Check the status of an existing loan, utilize the status endpoint.
5) Loan Repayment: To close an existing loan, utilize the Repayment Endpoint as needed.

1) Authentication
Generate bearer token for subsequent API calls.

Service	Login - Generate Auth Token
Request URL	baseUrl/api/v1/login
HTTP Method	POST
Request Headers	Content-Type: application/json
X-Signature: {{encrypted_checksum}}
Sample Request Body
Parameter Name	Type	M/O	Description
email	Email	M	Configured Email Address
password	String	M	Password

Response Body
Parameter Name	Type	M/O	Description
status	String	M	Status of the request. true signifies success
code	Integer	M	Corresponding description for the status. 200 = Successful
message	String	M	Description of the api response.
data	Object	M	An object containing authentication details.

Sample Response Body

{
     "status":true,
     "code":200,
     "message":"Successful",
     "data": {
            "token_type": "Bearer",
            "access_token": "eyJygurwiyugiwbreugijbiwqhfiywefgibwa",
            "expires_at": 7200,
            "name": "John Doe",
            "email": "<EMAIL>"
     }
 }
2) Loan Eligibility Check
This action confirms recipient's eligibility using agreed metrics and returns with loan offers where eligible.
Client is required to make a POST request to this endpoint.

Service	Loan Eligibility Check
Request URL	baseUrl/api/v1/loan/eligibility/check
HTTP Method	POST
Request Headers	Content-Type: application/json
Bearer: {{access_token}}
X-Signature: {{encrypted_checksum}}
Sample Request Body
Parameter Name	Type	M/O	Description
loginId	String	M	Merchant Id provided during integration
serviceType	String	M	airtime or data
recipient	Numeric	M	Customers phone number

Response Body
Parameter Name	Type	M/O	Description
status	String	M	Status of the request. true signifies success
code	Integer	M	Corresponding description for the status. 200 = Successful
message	String	M	Description of the api response.
data	Object	M	An object containing check response with available offer(s).

Sample Response Body

{
    "status": true,
    "code": 200,
    "message": "Successful",
    "data": {
        "offer1": {
            "amount": "200",
            "serviceId": "A04E",
            "product": "airtime"
        },
        "offer2": {
            "amount": "100",
            "serviceId": "A04E",
            "product": "airtime"
        },
        "isEligible": true,
        "reference": "077416241021064788727052243034"
    },
    "meta": null
}
3) Disburse Loan
This action confirms recipient's consent to the loan terms and conditions as indicated by the response returned from step 2) above.
Client is required to make a POST request to this endpoint.

Service	Disburse Loan
Request URL	baseUrl/api/v1/loan/apply
HTTP Method	POST
Request Headers	Content-Type: application/json
Bearer: {{access_token}}
X-Signature: {{encrypted_checksum}}
Sample Request Body (Airtime)
Parameter Name	Type	M/O	Description
loginId	Numeric	M	Merchant id provided during integration
requestId	String	M	Unique transaction Id for the request. Maxlength (36characters)
serviceId	String	M	Corresponding service id for the request type. See "Services" section for the available services and corresponding service codes for each.
amount	Numeric	M	Amount with which to top-up amount customers phone Maximum 100000
recipient	Numeric	M	Customers phone number to top-up

Sample Response Body
Parameter Name	Type	M/O	Description
status	String	M	Status of the request. true signifies success
code	Integer	M	Corresponding description for the status. 200 = Successful, 202 = Processing, 422 = Failed
message	String	M	Description of the vend response.
data	Object	M	An object containing vend response details.

Sample Responses

{
    "status": true,
    "code": 200,
    "message": "Loan Advance Approved Successfully",
    "data": {
        "requestId": "jc1bciigdebc",
        "recipient": "08092343261",
        "amount": "90.00",
        "confirmCode": "1705997694210722624497994",
        "network": "9Mobile",
        "tranxDate": "23-01-2024 9:14 am",
        "serviceCharge": "10.00"
    },
        "meta": null
}
 
{
    "status": true,
    "code": 202,
    "message": "Your transaction is being processed... Kindly run TSQ in 5 minutes",
}

{
    "status": false,
    "code": 422,
    "message": "Failed to fulfil request",
}

4) Get Loan Status
This endpoint is used to confirm the status of a loan application.

Service	Retrieve Utility Loan Status
Request URL	baseUrl/api/v1/loan/status/{requestId}
HTTP Method	GET
Request Headers	Content-Type: application/json
Bearer: {{access_token}}
X-Signature: {{encrypted_checksum}}
Sample Response Body
Parameter Name	Type	M/O	Description
status	String	M	Status of the request. true signifies success
code	Integer	M	Corresponding description for the status. 200 = Successful
message	String	M	Description of the status check response.
data	Object	M	An object containing utility loan details.

Sample Response Body

{
    "status": true,
    "code": 200,
    "message": "Loan Request Retrieved Successfully",
    "data": {
        "requestId": "9a1d44fij437",
        "recipient": "07067729339",
        "amount": 100,
        "confirmCode": "2021063019080126701629681",
        "network": "MTN",
        "tranxDate": "17-01-2024 2:06 pm",
        "serviceCharge": "10.00",
        "status": "Open"
    },
    "meta": null
}
5) Repay Utility Loan
This endpoint is used to finalized or close out an outstanding loan. To confirm the outstanding payment, a call the the loan status endpoint will suffice

Service	Repay Utility Loan
Request URL	baseUrl/api/v1/loan/repay
HTTP Method	POST
Request Headers	Content-Type: application/json
Bearer: {{access_token}}
X-Signature: {{encrypted_checksum}}
Sample Request Body
Parameter Name	Type	M/O	Description
requestId	String	M	Unique transaction Id for the request. Maxlength (36characters)
amount	Numeric	M	Outstanding Loan Amount. Note: It must be the exact amount as indicated by get loan status.

Sample Response Body
Parameter Name	Type	M/O	Description
status	String	M	Status of the request. true signifies success
code	Integer	M	Corresponding description for the status. 200 = Successful
message	String	M	Description of the status repayment request.
data	Object	M	An object containing utility loan repayment details.

Sample Response Body

{
    "status": true,
    "code": 200,
    "message": "Loan status updated successfully",
    "data": {
        "message": "Loan status updated successfully"
    },
    "meta": null
}
LOW BALANCE SERVICE (AUTO TOP-UP)
Overview
This document gives an overview of the Creditswitch low balance service. This is a subscription based service that enables customer to top up their balance while making calls .
For technical assistance with the CSW API contact <NAME_EMAIL> to enable swift response and support.

Integration Process Flow
This section contains a description of recommended integration flow which helps minimize the risk of disputing transactions and also increase customer confidence in the system

1) Onboarding: Begin by authenticating through the onboard Endpoint to retrieve a token for subsequent API calls.
2) Subscription: Call the Auto Top up subscription Endpoint with the specified payload. This step allows user to subscribe to the service.
3) Unsubscribe: If the customer wishes to unsubscribe from the Low Balance service, Call the unsubscribe endpoint to cancel active subscription.

1) Onboarding
Generate token for subsequent API calls.

Service	Onboard - Generate Auth Token
Request URL	baseUrl/api/v1/low/balance/create/vendor
HTTP Method	POST
Request Headers	Content-Type: application/json
Sample Request Body
Parameter Name	Type	M/O	Description
Merchant Login Id	String	M	Merchant LoginId
Merchant Name	String	M	Merchant Name
Debit URL	String	M	Merchant debit callback Url
Account URL	String	M	Merchant account callback Url

Response Body
Parameter Name	Type	M/O	Description
status	String	M	Status of the request.
statusDescription	String	M	Corresponding description for the status.
data	Object	M	An object containing authentication details.

Sample Response Body

{
    "statusCode": "00",
    "statusDescription": "Account created successfully",
    "data": "srUNEuasfbs_UtcuwJHIVWO-euJHCHVcggfw"
}
2) Subscription
Call the Auto Top up subscription Endpoint with the specified payload. This step allows user to subscribe to the service.
Client is required to make a POST request to this endpoint.

Service	Subscription
Request URL	baseUrl/api/v1/low/balance/subscribe
HTTP Method	POST
Request Headers	Content-Type: application/json
Sample Request Body
Parameter Name	Type	M/O	Description
msisdn	String	M	Customers Msisdn
amount	String	M	amount to top up
AirtimeThreshold	String	O	airtime threshold at which service should trigger
DataThreshold	String	O	data threshold at which service should trigger
Product	String	M	trigger type weather for airtime or data
network	String	M	customers msisdn network provider
createdAt	String	O	request timestamp

Sample Response Body

{ "network": "MTN", "product": "airtime", "airtimeThreshold": "200", "dataThreshold": "500MB", "amount": "100", "msisdn": "string", "createdAt": "2025-07-18T18:37:12.779576" }
Response Body
Parameter Name	Type	M/O	Description
status	String	M	Status of the request.
statusDescription	String	M	Corresponding description for the status.
data	String	O	response data if any.

Sample Response Body "statusCode": "string", "statusDescription": "string", "data": "string"

{ "statusCode": "00", "statusDescription": "Subscription is successful", "data": "udgsigiub_bjadvb" }
3) Unsubscribe
If the customer wishes to unsubscribe from the Low Balance service, Call the unsubscribe endpoint to cancel active subscription.
Client is required to make a DELETE request to this endpoint.

Service	Unsubscribe
Request URL	baseUrl/api/v1/low/balance/{{msisdn}}/{{thresholdType}}/unsubscrible
HTTP Method	DELETE
Request Headers	Content-Type: application/json
Sample Request Body
Parameter Name	Type	M/O	Description
msisdn	String	M	Customers Msisdn
Threshold Type	String	M	trigger type weather for airtime or data

Sample Response Body
Parameter Name	Type	M/O	Description
status	String	M	Status of the request.
statusDescription	String	M	Status Description of the request.

Sample Response Body

                                    {
                                        "statusCode": "00",
                                        "statusDescription": "Unsubscribed"
                                    }
                                    
                                
4) Notifications Callback
A Low balance notification is trigger to you if the subscriber airtime/data balance is below their threshold and a successful response will also trigger value to be given to the subscriber.

Callback Event -- notification request
{"subscriberMSISDN": "0803xxxxxxx", "airtimeThreshold": "50", "network": "MTN", "dataThreshold": "", "thresholdType": "airtime", "transactionId": "251068796011729491882","requestType":"OneTime/Subscription", "event": "notification"}
Callback Event -- notification Expected Response
{"status":true,"code":200,"message":"Debited Successful","data":{"isDebited":true,"transaction_reference":"931ATT6715e690775841729488528","amount":"100","recipient":"0803xxxxxxxxxxxx"}}

Callback Event -- update request
{"statusCode": "00", "statusDescription": "Successful", "confirmCode": "251068796011729491882", "tranxReference": "931ATT6715e690775841729488528", "event": "update"}
Callback Event -- update Expected Response
{ "status":true, "code":200, "message":"update Successful" }