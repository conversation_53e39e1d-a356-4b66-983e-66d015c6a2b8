# Required metadata
sonar.projectKey=credpal.bills-service
sonar.projectName=credpal.bills-service
sonar.projectVersion=1.0
sonar.sources=src

# Node.js specifics
sonar.language=js
sonar.javascript.lcov.reportPath=coverage/lcov.info
sonar.exclusions=**/node_modules/**,**/test/**,**/coverage/**,**/dist/**,**/src/**/migrations/**,**/src/**/seeds/**,**/src/**/*.spec.js,**/src/**/*.spec.ts,**/src/**/jest-setup.ts,**/*.html

# Encoding
sonar.sourceEncoding=UTF-8
