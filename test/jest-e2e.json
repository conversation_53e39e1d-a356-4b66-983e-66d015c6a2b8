{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapper": {"@app/baxi/(.*)": "<rootDir>/../libs/baxi/src/$1", "@app/baxi": "<rootDir>/../libs/baxi/src", "@app/rabbitmq/(.*)": "<rootDir>/../libs/rabbitmq/src/$1", "@app/rabbitmq": "<rootDir>/../libs/rabbitmq/src", "@app/flutterwave/(.*)": "<rootDir>/../libs/flutterwave/src/$1", "@app/flutterwave": "<rootDir>/../libs/flutterwave/src", "@app/quickteller/(.*)": "<rootDir>/../libs/quickteller/src/$1", "@app/quickteller": "<rootDir>/../libs/quickteller/src"}}