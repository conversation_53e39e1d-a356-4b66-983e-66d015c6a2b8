{"BillerList": {"Count": 257, "Category": [{"Id": 9, "Name": "Subscriptions", "Description": "Pay for your other subscriptions (like ISP) here", "Billers": [{"Type": "", "Id": 157, "PayDirectProductId": 3521, "PayDirectInstitutionId": 0, "Name": " Multichoice Dealer POS Payments", "ShortName": "MCNDLRN", "Narration": " Multichoice Dealer POS Payments", "CustomerField1": "SmartCard No", "LogoUrl": "eryttvg", "Surcharge": "10000", "CurrencyCode": "840", "CurrencySymbol": "USD", "QuickTellerSiteUrlName": "multichoice", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "SmallImageId": "399cc2f1-d1e4-4df7-829e-77a3a8033d68", "MediumImageId": "399cc2f1-d1e4-4df7-829e-77a3a8033d68", "AmountType": 0}, {"Type": "", "Id": 202, "PayDirectProductId": 3312, "PayDirectInstitutionId": 0, "Name": "CitiServe Payments", "ShortName": "CITI", "Narration": "Purchase CitiServe stock", "CustomerField1": "CNP Id", "CustomerField2": "Name", "LogoUrl": "qt.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "citiserve", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "AmountType": 0}, {"Type": "", "Id": 328, "PayDirectProductId": 3697, "PayDirectInstitutionId": 0, "Name": "Communications Trends Limited (TrendTV)", "ShortName": "CTL", "Narration": "Trend TV  Collections", "CustomerField1": "Unique Number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "trendtv", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "AmountType": 0}, {"Type": "", "Id": 952, "PayDirectProductId": 4307, "PayDirectInstitutionId": 0, "Name": "Developer Console", "ShortName": "DVC", "Narration": "Developer Console", "CustomerField1": "Developer ID", "LogoUrl": "devconsole.png", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "devconsole", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "AmountType": 0}, {"Type": "", "Id": 153, "PayDirectProductId": 3411, "PayDirectInstitutionId": 0, "Name": "Infinity TV Subscriptions", "ShortName": "INFI", "Narration": "PAYDirectEnterprise", "CustomerField1": "SmartCard No", "CustomerField2": "", "LogoUrl": "q.jpg", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "infinitytv", "SupportEmail": "", "CategoryId": 9, "CategoryName": "Subscriptions", "MediumImageId": "585c357e-e1c2-4b9f-b319-5f158d4b71f5", "AmountType": 0}, {"Type": "", "Id": 111, "PayDirectProductId": 126, "PayDirectInstitutionId": 0, "Name": "IPNX", "ShortName": "IPNX", "Narration": "Pay IPNX subscriptions", "CustomerField1": "Account Number/Wireless telephone", "CustomerField2": "Wireless Telephone", "LogoUrl": "ipnx2.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ipnx", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [{"Title": "Account Number/Wireless telephone", "IsRequired": true, "SortOrder": 0, "ElementType": "TextBox", "DataType": "String", "Length": 12, "SelectItems": [], "ElementName": "customerid"}, {"Title": "Phone No", "IsRequired": true, "SortOrder": 1, "ElementType": "TextBox", "DataType": "String", "Length": 12, "SelectItems": [], "ElementName": "Phoneno"}], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": false, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "MediumImageId": "d1e1dbde-6cbb-4882-95e8-37191d3cebe4", "AmountType": 0}, {"Type": "PHV", "Id": 154, "PayDirectProductId": 3418, "PayDirectInstitutionId": 0, "Name": "Multichioce Nigeria (ENT)", "ShortName": "MCNG", "Narration": "Multichoice Subscription", "CustomerField1": "SmartCard No", "LogoUrl": "mcn.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mcng", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "AmountType": 0}, {"Type": "", "Id": 158, "PayDirectProductId": 3521, "PayDirectInstitutionId": 0, "Name": "Multichoice Office POS", "ShortName": "MCP", "Narration": "Multichoice Office POS payments", "CustomerField1": "SmartCard No", "LogoUrl": "mcn.jpeg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "MCNPOS", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "MediumImageId": "ecf87524-1764-40d6-a7c8-b4c7c34b3a9a", "AmountType": 0}, {"Type": "", "Id": 448, "PayDirectProductId": 4080, "PayDirectInstitutionId": 0, "Name": "OMNIVU", "ShortName": "OMNIV", "Narration": "OMNIVU", "CustomerField1": "Order Code", "LogoUrl": "omnivu_white.png", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "omnivu", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "MediumImageId": "ab02511e-8d68-4bc9-9958-b5c6ace5319b", "AmountType": 0}, {"Type": "", "Id": 951, "PayDirectProductId": 4293, "PayDirectInstitutionId": 0, "Name": "RetailPay", "ShortName": "RTP", "Narration": "Retail Pay", "CustomerField1": "Merchant Code", "LogoUrl": "retailpay.png", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "retailpay", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "AmountType": 0}, {"Type": "", "Id": 17561, "PayDirectProductId": 4556, "PayDirectInstitutionId": 0, "Name": "spectranet", "ShortName": "SPEC", "CustomerField1": "User ID", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "spectranet", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "AmountType": 0}, {"Type": "", "Id": 17599, "PayDirectProductId": 4563, "PayDirectInstitutionId": 0, "Name": "Uber 4 diesel", "ShortName": "U4D", "Narration": "Uber 4 diesel", "CustomerField1": "Customer Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "uber", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 9, "CategoryName": "Subscriptions", "AmountType": 0}]}, {"Id": 4, "Name": "Mobile/Recharge", "Description": "Recharge your phone", "Billers": [{"Type": "MO", "Id": 17305, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "09test", "ShortName": "09test", "CustomerField1": "Phone Number", "LogoUrl": "09test.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "09test", "NetworkId": "*********3", "ProductCode": "*********3", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "SmallImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "LargeImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "MediumImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "AmountType": 0}, {"Type": "MP", "Id": 687, "PayDirectProductId": 3361, "PayDirectInstitutionId": 0, "Name": "Airtel Data Bundles", "ShortName": "Airtel", "Narration": "Airtel Data", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "3500", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "airtel", "SupportEmail": "<EMAIL>", "NetworkId": "*********5", "ProductCode": "*********5", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "MP", "Id": 17570, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Airtel OTA", "ShortName": "AirtelOTA", "Narration": "Airtel OTA", "CustomerField1": "Enter Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "AirtelOTA", "NetworkId": "**********", "ProductCode": "**********", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "MP", "Id": 108, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Airtel Recharge Pins", "ShortName": "ZainPIN", "Narration": "Buy Airtel recharge PINs here", "CustomerField1": "Phone Number", "LogoUrl": "zain.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "zainrechargepin", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16201", "NetworkId": "*********", "ProductCode": "*********", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "SmallImageId": "c1b7dde0-cc9d-46a5-947a-186abf95a09c", "LargeImageId": "c1b7dde0-cc9d-46a5-947a-186abf95a09c", "MediumImageId": "c1b7dde0-cc9d-46a5-947a-186abf95a09c", "AmountType": 1}, {"Type": "MO", "Id": 17608, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "<PERSON>mmy <PERSON>", "ShortName": "Demmy", "Narration": "International airtime vending ", "CustomerField1": "CustomerId", "CustomerField2": "MobileNumber", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "demmy", "SupportEmail": "<EMAIL>", "NetworkId": "1078694590", "ProductCode": "107869459002", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "MO", "Id": 17606, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "DtOne", "ShortName": "DTO", "Narration": "International telco", "CustomerField1": "Mobile Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "dtone", "SupportEmail": "<EMAIL>", "NetworkId": "1078694588", "ProductCode": "107869458801", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "MO", "Id": 120, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Etisalat Recharge Top-Up", "ShortName": "ETILAT", "Narration": "Buy Etisalat recharge", "CustomerField1": "Phone No", "LogoUrl": "9mobile.jpg", "Surcharge": "900", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "et<PERSON><PERSON>", "SupportEmail": "<EMAIL>", "NetworkId": "*********5", "ProductCode": "6280510490", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "SmallImageId": "c132842d-9cb4-4c34-9d56-8dafb75959d8", "LargeImageId": "c132842d-9cb4-4c34-9d56-8dafb75959d8", "MediumImageId": "c132842d-9cb4-4c34-9d56-8dafb75959d8", "AmountType": 0}, {"Type": "PHV", "Id": 15944, "PayDirectProductId": 4431, "PayDirectInstitutionId": 0, "Name": "GLO", "ShortName": "GLODATA", "Narration": "GLO", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "glodata", "SupportEmail": "<EMAIL>", "NetworkId": "**********", "ProductCode": "628051501116", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "MO", "Id": 402, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Glo QuickCharge", "ShortName": "GLOQCK", "Narration": "Mobile Top-Up", "CustomerField1": "Mobile number", "LogoUrl": "glo.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "gloquickcharge", "NetworkId": "628051056", "ProductCode": "1076565345", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "NetworkId:628051056,\r\nProductCode:1076565345", "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "", "Id": 322, "PayDirectProductId": 3663, "PayDirectInstitutionId": 0, "Name": "Idevworks Tech Ltd", "ShortName": "IDEVW", "Narration": "SmsLIive247 Services", "CustomerField1": "Phone Number", "LogoUrl": "idevworks.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "idevworks", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "MediumImageId": "904d17ba-8d33-4992-94bc-147382ffee48", "AmountType": 0}, {"Type": "MP", "Id": 17572, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "MTN Bill Stream", "ShortName": "MTNB", "Narration": "NetworkId:628051043,ProductCode:**", "CustomerField1": "CustomerId", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtnBillStream", "RiskCategoryId": "16201", "NetworkId": "628051043", "ProductCode": "**", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "MP", "Id": 4444, "PayDirectProductId": 3516, "PayDirectInstitutionId": 0, "Name": "MTN Data Bundles", "ShortName": "MTN", "Narration": "MTN Data", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "3500", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtnvtu", "SupportEmail": "<EMAIL>", "NetworkId": "628051043", "ProductCode": "6280510431", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "MediumImageId": "7389beb8-36fe-4c1d-bba0-56635ff4bfe0", "AmountType": 0}, {"Type": "MP", "Id": 17571, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "MTN Dynamic Data Bundle", "ShortName": "MTN2", "Narration": "MTN Recharge Test", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtn2", "NetworkId": "628051043", "ProductCode": "6280510431", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "SmallImageId": "399cc2f1-d1e4-4df7-829e-77a3a8033d68", "LargeImageId": "399cc2f1-d1e4-4df7-829e-77a3a8033d68", "MediumImageId": "399cc2f1-d1e4-4df7-829e-77a3a8033d68", "AmountType": 0}, {"Type": "MO", "Id": 109, "PayDirectProductId": 3900, "PayDirectInstitutionId": 0, "Name": "MTN e-Charge Prepaid", "ShortName": "MTNVTU1", "Narration": "Purchase MTN Virtual Aitime", "CustomerField1": "Phone Number", "LogoUrl": "mtn3.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtnvtu1", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16201", "NetworkId": "628051043", "ProductCode": "628051043", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "SmallImageId": "e31c2f89-0d45-4b69-9d79-352352b80b85", "LargeImageId": "e31c2f89-0d45-4b69-9d79-352352b80b85", "MediumImageId": "e31c2f89-0d45-4b69-9d79-352352b80b85", "AmountType": 2}, {"Type": "", "Id": 16909, "PayDirectProductId": 4498, "PayDirectInstitutionId": 0, "Name": "MTN Postpaid2", "ShortName": "MTNPOST", "CustomerField1": "Account ID|Invoice ID", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtnpostpaid", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 3}, {"Type": "", "Id": 16910, "PayDirectProductId": 4518, "PayDirectInstitutionId": 0, "Name": "MTN POSTPAID3", "ShortName": "MTNPOST3", "CustomerField1": "Bill/Reference Id", "CustomerField2": "Service Id", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "MTNPOST3", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 5}, {"Type": "MO", "Id": 17602, "PayDirectProductId": 4610, "PayDirectInstitutionId": 0, "Name": "MTN TIG Bundles", "ShortName": "MTNT", "Narration": "MTN TIG Data", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtnvtu", "SupportEmail": "<EMAIL>", "NetworkId": "1078694577", "ProductCode": "107869457701", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 1}, {"Type": "PHV", "Id": 15607, "PayDirectProductId": 4426, "PayDirectInstitutionId": 0, "Name": "NTEL Data Bundle", "ShortName": "NTELDATA", "Narration": "NTEL Data Bundle Purchase", "CustomerField1": "Mobile Number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "nteldata", "SupportEmail": "<EMAIL>", "NetworkId": "6280510471000", "ProductCode": "628051047", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "MO", "Id": 15727, "PayDirectProductId": 4422, "PayDirectInstitutionId": 0, "Name": "Safaricom Kenya", "ShortName": "SAFKE", "Narration": "Safaricom Kenya", "CustomerField1": "Phone Number", "LogoUrl": "Safaricom dummy", "Surcharge": "0", "CurrencyCode": "404", "CurrencySymbol": "KES", "QuickTellerSiteUrlName": "safaricom", "SupportEmail": "<EMAIL> ", "NetworkId": "*********9", "ProductCode": "*********9", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "SmallImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "LargeImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "MediumImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "AmountType": 0}, {"Type": "MO", "Id": 110, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "VisaFone Data Plan", "ShortName": "VDP", "Narration": "Visafone Data Plan", "CustomerField1": "customer id", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "visafonedataplan", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "VOS", "Id": 172, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Voucher Service", "ShortName": "VOS", "Narration": "Voucher Redemption", "CustomerField1": "customerMobile", "LogoUrl": "q.gif", "Surcharge": "0", "CustomSectionUrl": "62809894", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "Voucher/create", "SupportEmail": "<EMAIL>", "NetworkId": "62809894", "ProductCode": "62809894001", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}, {"Type": "PHV", "Id": 16189, "PayDirectProductId": 4407, "PayDirectInstitutionId": 0, "Name": "WAEC Payments", "ShortName": "WEPV", "Narration": "WAEC Payments", "CustomerField1": "Mobile Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "waec", "SupportEmail": "<EMAIL>", "NetworkId": "62805188001", "ProductCode": "043076012", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 5}, {"Type": "PHV", "Id": 16480, "PayDirectProductId": 4447, "PayDirectInstitutionId": 0, "Name": "Waec Sale of Forms", "ShortName": "WFTV", "Narration": "Waec Form Tokens", "CustomerField1": "Mobile Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "waecforms", "SupportEmail": "<EMAIL>", "NetworkId": "62805188001", "ProductCode": "6280515088002", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 4, "CategoryName": "Mobile/Recharge", "AmountType": 0}]}, {"Id": 1, "Name": "Utility Bills", "Description": "Pay your utility bills here", "Billers": [{"Type": "", "Id": 14835, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "10159au8", "ShortName": "10159au8", "Narration": "10159au8", "CustomerField1": "Mobile Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "10159au8", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "MediumImageId": "b0b79428-50bf-468b-898b-883c78bc6fb1", "AmountType": 0}, {"Type": "PHV", "Id": 17589, "PayDirectProductId": 4588, "PayDirectInstitutionId": 0, "Name": "Abuja Disco BuyPower", "ShortName": "AbjBuyPwr", "CustomerField1": "CustomerRef", "CustomerField2": "CustomerRef", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "abjbuypwr", "NetworkId": "buypower-uat", "ProductCode": "*********", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 2}, {"Type": "", "Id": 16908, "PayDirectProductId": 4509, "PayDirectInstitutionId": 0, "Name": "ABUJA ELECTRIC POSTPAID", "ShortName": "ABJ", "Narration": "ABUJA ELECTRIC POSTPAID", "CustomerField1": "ACCOUNT NO test", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "abjelectric", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 4}, {"Type": "PHV", "Id": 16921, "PayDirectProductId": 4526, "PayDirectInstitutionId": 0, "Name": "ABUJA ELECTRIC PREPAID", "ShortName": "AEDC", "Narration": "ABUJA ELECTRIC PREPAID", "CustomerField1": "ACCOUNT NO", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "aedc", "SupportEmail": "<EMAIL>", "NetworkId": "**********", "ProductCode": "**********", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8740, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "AppDeveloper", "ShortName": "AppDeveloper", "Narration": "AppDeveloper", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "appdeveloper", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8761, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "asd", "ShortName": "asd", "Narration": "asd", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "asd", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8738, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "RMM8056", "Narration": "<PERSON><PERSON><PERSON><PERSON>", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "<PERSON><PERSON><PERSON>", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8803, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "bigTenterprise", "ShortName": "bigTenterprise", "Narration": "bigTenterprise", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "bigtenterprise", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 16953, "PayDirectProductId": 4538, "PayDirectInstitutionId": 0, "Name": "Bonny Utility", "ShortName": "BUC", "Narration": "Bonny Utility (Prepaid)", "CustomerField1": "Meter Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "bonnyprepaid", "NetworkId": "*********", "ProductCode": "111222333", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": false, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "NetworkId:**********,ProductCode:111222333", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 17584, "PayDirectProductId": 4571, "PayDirectInstitutionId": 0, "Name": "Disco Local Validation", "ShortName": "DiscoLocal", "Narration": "Disco Local Validation", "CustomerField1": "Customer ID", "LogoUrl": "q.gif", "Surcharge": "59", "CustomSectionUrl": "Old - NetworkId:*********,ProductCode:**********", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "discoLocal", "NetworkId": "*********", "ProductCode": "*********5", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 695, "PayDirectProductId": 3552, "PayDirectInstitutionId": 0, "Name": "Easy Access", "ShortName": "Easy", "Narration": "Easy Payments", "CustomerField1": "", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "easyaccess", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 201, "PayDirectProductId": 166, "PayDirectInstitutionId": 0, "Name": "Edo Waste Management", "ShortName": "EDOWAS", "Narration": "Pay waste disposal bills", "CustomerField1": "House code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "edowa<PERSON>", "SupportEmail": "<EMAIL>", "CategoryId": 1, "CategoryName": "Utility Bills", "MediumImageId": "a1004c8d-04e6-4ff9-8f2f-0a43e784ad15", "AmountType": 0}, {"Type": "PHV", "Id": 17601, "PayDirectProductId": 4582, "PayDirectInstitutionId": 0, "Name": "EKEDC NEW", "ShortName": "EKO", "Narration": "EKEDC NEW", "CustomerField1": "Meter No", "LogoUrl": "q.gif", "Surcharge": "0", "CustomSectionUrl": "NetworkId:43210603,ProductCode:43210603", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "eko", "NetworkId": "43210603", "ProductCode": "43210603", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 16903, "PayDirectProductId": 4614, "PayDirectInstitutionId": 0, "Name": "Enugu Electric", "ShortName": "EKED", "Narration": "Enugu Electric", "CustomerField1": "METER NO", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "enugu", "SupportEmail": "<EMAIL>", "NetworkId": "**********", "ProductCode": "6280515123", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 117, "PayDirectProductId": 142, "PayDirectInstitutionId": 0, "Name": "Etisalat Dealer Payments", "ShortName": "Etisal", "Narration": "Make your Dealer Payments", "CustomerField1": "Dealer Number", "LogoUrl": "etisalat.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "MediumImageId": "b09d4df0-e684-4a5c-8bb5-2e12bc20e9a1", "AmountType": 0}, {"Type": "", "Id": 115, "PayDirectProductId": 142, "PayDirectInstitutionId": 0, "Name": "Etisalat Postpaid", "ShortName": "Etisal", "Narration": "Pay your bills", "CustomerField1": "Phone Number", "LogoUrl": "etisalat.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "etisalat<PERSON>s", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 14501, "PayDirectProductId": 4389, "PayDirectInstitutionId": 0, "Name": "Gambia Test Biller", "ShortName": "GMTB", "Narration": "Gambia Test Biller", "CustomerField1": "customer id", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "270", "CurrencySymbol": "GMD", "QuickTellerSiteUrlName": "gmtb", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 17583, "PayDirectProductId": 4570, "PayDirectInstitutionId": 0, "Name": "IBEDC POSTPAID ESB", "ShortName": "IBEDCP", "Narration": "IBADAN ELECTRIC", "CustomerField1": "ACCOUNT NO", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "IBEDC POSTPAID", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 17580, "PayDirectProductId": 4568, "PayDirectInstitutionId": 0, "Name": "IBEDC PREPAID", "ShortName": "IBEDC", "Narration": "IBEDC PREPAID", "CustomerField1": "ACCOUNT NO", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "IBEDC PREPAID", "NetworkId": "*********", "ProductCode": "*********", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 13772, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "IdowuBby636", "ShortName": "IdowuBby636", "Narration": "IdowuBby636", "CustomerField1": "Merchant Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "idowubby636", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 16265, "PayDirectProductId": 4377, "PayDirectInstitutionId": 0, "Name": "IFIS AGENT TOPUP", "ShortName": "IFIS", "Narration": "Quickteller Paypoint Agent wallet Top-up", "CustomerField1": "Agent Mobile Number", "LogoUrl": "q.gif", "Surcharge": "5451", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "paypoint", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 17271, "PayDirectProductId": 4551, "PayDirectInstitutionId": 0, "Name": "IFIS IKEDC Postpaid", "ShortName": "IFIKE", "Narration": "IE Postpaid", "CustomerField1": "Meter No", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ikedcpostpaid", "NetworkId": "*********", "ProductCode": "6280515455", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "NetworkId:*********,ProductCode:6280515455", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 17226, "PayDirectProductId": 4552, "PayDirectInstitutionId": 0, "Name": "IFIS IKEDC Prepaid", "ShortName": "IKEPR", "Narration": "IE Prepaid", "CustomerField1": "Meter No", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ikedcprepaid", "NetworkId": "*********", "ProductCode": "6280515456", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "NetworkId:*********,ProductCode:6280515456", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8734, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "Joo5", "ShortName": "RMM8030", "Narration": "Joo5", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "joo5", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 15943, "PayDirectProductId": 4430, "PayDirectInstitutionId": 0, "Name": "Kaduna Electric Prepaid", "ShortName": "KEDC", "Narration": "Kaduna Electric Prepaid", "CustomerField1": "MeterNo", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "kadunaelectricprepaid", "SupportEmail": "<EMAIL>", "NetworkId": "628051043", "ProductCode": "628051502112001", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8769, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "KingstonStorrs", "ShortName": "KingstonStorrs", "Narration": "KingstonStorrs", "CustomerField1": "Customer Id", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "kingstonstorrs", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 17600, "PayDirectProductId": 120, "PayDirectInstitutionId": 0, "Name": "Kogi Electricity Distribution Company", "ShortName": "<PERSON><PERSON>", "Narration": "Pay Kogi Electricity", "CustomerField1": "Meter number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "kogi", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "SmallImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "LargeImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "MediumImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "AmountType": 0}, {"Type": "PHV", "Id": 16570, "PayDirectProductId": 4462, "PayDirectInstitutionId": 0, "Name": "Lagos State Collections", "ShortName": "ABC", "Narration": "LASG Payments", "CustomerField1": "PaymentRef/BillType", "LogoUrl": "q.gif", "Surcharge": "0", "CustomSectionUrl": "NetworkId:62805188001,ProductCode:6280515021120001", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "lasg", "SupportEmail": "<EMAIL>", "NetworkId": "62805188001", "ProductCode": "55667701", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 3}, {"Type": "PHV", "Id": 17604, "PayDirectProductId": 4462, "PayDirectInstitutionId": 0, "Name": "Lagos State Collections - WEBGUID", "ShortName": "WEBGUID", "Narration": "LASG Payments", "CustomerField1": "PaymentRef/BillType", "LogoUrl": "q.gif", "Surcharge": "0", "CustomSectionUrl": "NetworkId:62805188001,ProductCode:6280515021120001", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "lasgguid", "SupportEmail": "<EMAIL>", "NetworkId": "62805188001", "ProductCode": "55667701", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 4}, {"Type": "", "Id": 14521, "PayDirectProductId": 4362, "PayDirectInstitutionId": 0, "Name": "<PERSON><PERSON><PERSON>", "ShortName": "MOJEC", "Narration": "<PERSON><PERSON><PERSON>", "CustomerField1": "Meter Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mojec", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8743, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "<PERSON><PERSON><PERSON>", "ShortName": "<PERSON><PERSON><PERSON>", "Narration": "<PERSON><PERSON><PERSON>", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "m<PERSON><PERSON>", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 17598, "PayDirectProductId": 4569, "PayDirectInstitutionId": 0, "Name": "Nohe Payment", "ShortName": "Nohe", "Narration": "Nohe Payment", "CustomerField1": "Customer Number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "nohe", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 103, "PayDirectProductId": 2, "PayDirectInstitutionId": 0, "Name": "PHCN Bills", "ShortName": "PHCN", "Narration": "Pay PHCN post paid ", "CustomerField1": "Customer Account Number", "CustomerField2": "Meter Number 2", "LogoUrl": "phcn.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "phcn1", "SupportEmail": "<EMAIL>", "CustomMessageUrl": "Test product info for PHCN web view", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "CustomMessageId": "103", "AmountType": 0}, {"Type": "PHV", "Id": 137, "PayDirectProductId": 4352, "PayDirectInstitutionId": 0, "Name": "phcn ikeja", "ShortName": "phcnik", "Narration": "PHCN IKEJA", "CustomerField1": "METER NO", "LogoUrl": "phcn.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "phcnikeja", "NetworkId": "**********", "ProductCode": "*************", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PH", "Id": 905, "PayDirectProductId": 2000, "PayDirectInstitutionId": 0, "Name": "Phcn Prepaid", "ShortName": "PhcnPP", "Narration": "Buy your meter token", "CustomerField1": "Meter No", "LogoUrl": "phcn.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "phcnprepaid", "SupportEmail": "<EMAIL>", "NetworkId": "62805151", "ProductCode": "62805151", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 912, "PayDirectProductId": 3680, "PayDirectInstitutionId": 0, "Name": "PHCN Prepaid (ABUJA ZONE)", "ShortName": "PHAZ", "Narration": "PHCN Abuja - Buy your Meter Token", "CustomerField1": "Meter Number", "LogoUrl": "phcn.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "phcnprepaidabuja", "RiskCategoryId": "16201", "NetworkId": "6280510451", "ProductCode": "6280515092002", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 993, "PayDirectProductId": 4348, "PayDirectInstitutionId": 0, "Name": "PHCN Prepaid (Enterprise)", "ShortName": "PHCNENT", "CustomerField1": "Meter Number", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "phcnent", "SupportEmail": "<EMAIL>", "NetworkId": "************", "ProductCode": "************", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 910, "PayDirectProductId": 2, "PayDirectInstitutionId": 0, "Name": "PHCN prepaid new", "ShortName": "PHP", "Narration": "Phcn Prepaid via VTUcare", "CustomerField1": "Meter No", "LogoUrl": "phcn.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "phcnprepaidnew", "NetworkId": "************", "ProductCode": "************", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 16922, "PayDirectProductId": 4527, "PayDirectInstitutionId": 0, "Name": "PHEDC Postpaid", "ShortName": "PHED", "Narration": "PHEDC Postpaid", "CustomerField1": "ACCOUNT NO", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "phed", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 16919, "PayDirectProductId": 4523, "PayDirectInstitutionId": 0, "Name": "PHEDC Prepaid", "ShortName": "PHEDC", "Narration": "PHEDC Prepaid", "CustomerField1": "METER NO", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "phedc", "SupportEmail": "<EMAIL>", "NetworkId": "**********", "ProductCode": "*********", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 17067, "PayDirectProductId": 4542, "PayDirectInstitutionId": 0, "Name": "Remita Integration", "ShortName": "REM", "Narration": "Remita Integration", "CustomerField1": "RRR", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "remita", "NetworkId": "*********", "ProductCode": "1573142063", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "NetworkId:*********,ProductCode:1573142063", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 5}, {"Type": "", "Id": 8758, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "RetailPAYDirect", "ShortName": "RetailPAYDirect", "Narration": "RetailPAYDirect", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "retailpaydirect", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8759, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "RetailPAYDirect", "ShortName": "RetailPAYDirect", "Narration": "RetailPAYDirect", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "retailpaydirect", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 16119, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "sv92fgtb", "ShortName": "sv92fgtb", "Narration": "sv92fgtb", "CustomerField1": "Mobile Number", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "sv92fgtb", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "MediumImageId": "434740e7-645a-4b0f-9e5b-6e83064d84d0", "AmountType": 0}, {"Type": "", "Id": 8739, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "Tiger<PERSON>owerRanger", "ShortName": "RMM8058", "Narration": "Tiger<PERSON>owerRanger", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "tigerpowerranger", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "", "Id": 8785, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "Tmm", "ShortName": "Tmm", "Narration": "Tmm", "CustomerField1": "Order Code", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "tmm", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}, {"Type": "PHV", "Id": 17592, "PayDirectProductId": 4595, "PayDirectInstitutionId": 0, "Name": "Yola Electricity Distribution Company", "ShortName": "YEDC", "Narration": "Pay Yola Electricity", "CustomerField1": "Meter number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "yedc", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "SmallImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "LargeImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "MediumImageId": "fb44228b-199e-4ae8-a9e7-55294928b806", "AmountType": 0}, {"Type": "", "Id": 15696, "PayDirectProductId": 4420, "PayDirectInstitutionId": 0, "Name": "ZUKU", "ShortName": "ZUKU", "Narration": "ZUKU", "CustomerField1": "Account Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "404", "CurrencySymbol": "KES", "QuickTellerSiteUrlName": "Zuku", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 1, "CategoryName": "Utility Bills", "AmountType": 0}]}, {"Id": 14, "Name": "Others", "Description": "Others", "Billers": [{"Type": "", "Id": 16900, "PayDirectProductId": 4405, "PayDirectInstitutionId": 0, "Name": "7UP", "ShortName": "7UP", "Narration": "7UP", "CustomerField1": "Order ID", "LogoUrl": "q.gif", "Surcharge": "33", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "7UP", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "MP", "Id": 17569, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Airtel Data Plans", "ShortName": "AirtelData", "Narration": "Airtel Data Plans", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "airtelDataPlans", "NetworkId": "*********5", "ProductCode": "*********5", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "SmallImageId": "c1b7dde0-cc9d-46a5-947a-186abf95a09c", "LargeImageId": "c1b7dde0-cc9d-46a5-947a-186abf95a09c", "MediumImageId": "c1b7dde0-cc9d-46a5-947a-186abf95a09c", "AmountType": 0}, {"Type": "", "Id": 319, "PayDirectProductId": 3670, "PayDirectInstitutionId": 0, "Name": "ANAN", "ShortName": "ANAN", "Narration": "ANAN Fee Payment", "CustomerField1": "Membership Number", "LogoUrl": "anan.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "anan", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "4b41ef69-8019-4aaf-a956-62a48f3973ee", "AmountType": 0}, {"Type": "", "Id": 8773, "PayDirectProductId": 4334, "PayDirectInstitutionId": 0, "Name": "Api Documentation", "ShortName": "ISW", "Narration": "Api Documentation", "CustomerField1": "Mobile Number", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "api-doc", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 978, "PayDirectProductId": 3654, "PayDirectInstitutionId": 0, "Name": "ARA COLLECTION", "ShortName": "ARA", "Narration": "Ara Collection", "CustomerField1": "Dealer code", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ara", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 125, "PayDirectProductId": 3495, "PayDirectInstitutionId": 0, "Name": "Aratext", "ShortName": "AVS", "Narration": "Bulk SMS sales", "CustomerField1": "user ID", "CustomerField2": "", "LogoUrl": "aratext.jpeg", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "aratext", "SupportEmail": "<EMAIL>", "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "68bde99b-0cd1-4d15-a334-52bcfece9aad", "AmountType": 0}, {"Type": "", "Id": 122, "PayDirectProductId": 3356, "PayDirectInstitutionId": 0, "Name": "ASCON oil", "ShortName": "ASCON", "Narration": "Ascon Dealer paymentss", "CustomerField1": "Dealer No", "LogoUrl": "ascon.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ascon", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "PHV", "Id": 17573, "PayDirectProductId": 4560, "PayDirectInstitutionId": 0, "Name": "BillpaymentPaydirectVTU", "ShortName": "BPVTU", "Narration": "BillpaymentPaydirectVTU", "CustomerField1": "Cust ID", "LogoUrl": "q.gif", "Surcharge": "9999", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "BillpaymentPaydirectVTU", "NetworkId": "*********", "ProductCode": "**********", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "NetworkId:*********,ProductCode:**********", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 906, "PayDirectProductId": 3320, "PayDirectInstitutionId": 0, "Name": "<PERSON>", "ShortName": "bbrk", "Narration": "Buy your laptop bags and shoes", "CustomerField1": "tag no", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "brooksbags", "SupportEmail": "", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 136, "PayDirectProductId": 4278, "PayDirectInstitutionId": 0, "Name": "Buy Gift Card", "ShortName": "BGC", "Narration": "Pay for Gift Card", "CustomerField1": "Order No", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "payforgiftcard", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 156, "PayDirectProductId": 3438, "PayDirectInstitutionId": 0, "Name": "Card Control", "ShortName": "ISWCC", "Narration": "Card Control Registration", "CustomerField1": "Phone number", "LogoUrl": "cardcontrol.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "cardcontrol", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 520, "PayDirectProductId": 3844, "PayDirectInstitutionId": 0, "Name": "Church Of God Mission International", "ShortName": "CGM", "Narration": "Church Of God Mission International", "CustomerField1": "Customer Name", "LogoUrl": "churchofgod", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "cgmi", "SupportEmail": "For the next steps kindly proceed to; www.icanonline-ngr.com/quickteller", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Church Of God Mission International", "PaymentOptionsTitle": "Church Of God Mission International", "CategoryId": 14, "CategoryName": "Others", "AmountType": 1}, {"Type": "", "Id": 942, "PayDirectProductId": 3767, "PayDirectInstitutionId": 0, "Name": "Dancehall Party", "ShortName": "DHP", "Narration": "Dancehall Party Tickets", "CustomerField1": "Email Address of Ticket Recipient", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "dancehallparty", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 13773, "PayDirectProductId": 4371, "PayDirectInstitutionId": 0, "Name": "Daystar Church", "ShortName": "daystar", "Narration": "Daystar Church", "CustomerField1": "Mobile Number", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "daystarleadership", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 16462, "PayDirectProductId": 4443, "PayDirectInstitutionId": 0, "Name": "DirectTest", "ShortName": "Direct", "Narration": "Testing", "CustomerField1": "Phone number", "CustomerField2": "Phone number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "directtest", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "PHV", "Id": 17582, "PayDirectProductId": 4560, "PayDirectInstitutionId": 0, "Name": "Disco Test Biller", "ShortName": "Disco", "Narration": "Test Biller for Disco Payments", "CustomerField1": "Customer ID", "LogoUrl": "q.gif", "Surcharge": "3000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "discoTest", "NetworkId": "*********", "ProductCode": "**********", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "PCL", "Id": 16923, "PayDirectProductId": 4532, "PayDirectInstitutionId": 0, "Name": "EChange e-Account", "ShortName": "EEA", "Narration": "EChange e-Account", "CustomerField1": "Enter Wallet Identifier", "CustomerField2": "Phone Number or EChange ID", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "echange", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 16906, "PayDirectProductId": 4507, "PayDirectInstitutionId": 0, "Name": "EfeTest", "ShortName": "Efe", "Narration": "Testing Biller Creation", "CustomerField1": "Email", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "efe", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 222, "PayDirectProductId": 4026, "PayDirectInstitutionId": 0, "Name": "FCT Parking Ticket", "ShortName": "PT", "Narration": "FCT Parking Ticket", "CustomerField1": "Dealer ID", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "parking", "RiskCategoryId": "16202", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 16905, "PayDirectProductId": 4488, "PayDirectInstitutionId": 0, "Name": "Federal GovtTSA", "ShortName": "FGTSA", "Narration": "Federal GovtTSA", "CustomerField1": "Customer Id", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "fgtsa", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 5}, {"Type": "", "Id": 17435, "PayDirectProductId": 3844, "PayDirectInstitutionId": 0, "Name": "Fixed and Exact Test Biller", "ShortName": "FnE", "Narration": "Fixed and Exact Test Biller", "CustomerField1": "Phone", "LogoUrl": "q.gif", "Surcharge": "1000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "FandE", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 5}, {"Type": "", "Id": 16907, "PayDirectProductId": 4508, "PayDirectInstitutionId": 0, "Name": "Fuel Voucher", "ShortName": "FuelV", "Narration": "Fuel voucher", "CustomerField1": "Invoice ID", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "FuelVoucher", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "PCL", "Id": 161, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Fund Verve Prepaid Card", "ShortName": "PCL", "Narration": "Fund your Verve Prepaid Card", "CustomerField1": "Card PAN (12-19 digits)", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "VervePrepaid", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Load Card", "StartPage": "DoPayment.aspx", "UsesPaymentItems": false, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "d96f4123-c1ea-4946-898d-9e8e069413d4", "AmountType": 0}, {"Type": "PCL", "Id": 17141, "PayDirectProductId": 4543, "PayDirectInstitutionId": 0, "Name": "Generic e-Account", "ShortName": "GEA", "Narration": "Generic e-Account", "CustomerField1": "Generic identification Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "generic", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "PHV", "Id": 16902, "PayDirectProductId": 4487, "PayDirectInstitutionId": 0, "Name": "GLO VOT", "ShortName": "GLOVOT", "Narration": "GLO POS", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "glov<PERSON>", "SupportEmail": "<EMAIL>", "NetworkId": "**********", "ProductCode": "************", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 16925, "PayDirectProductId": 4533, "PayDirectInstitutionId": 0, "Name": "IFIS Wallet Topup", "ShortName": "IFISWT", "Narration": "IFIS Wallet Topup for Funding Wallet", "CustomerField1": "Agent Phone Number", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ifisWalletTopUp", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "PHV", "Id": 16913, "PayDirectProductId": 4515, "PayDirectInstitutionId": 0, "Name": "International Remittance", "ShortName": "IREM", "Narration": "International Remittance", "CustomerField1": "Account Number / Wallet Id", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "irem", "NetworkId": "********", "ProductCode": "****************", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 14516, "PayDirectProductId": 4332, "PayDirectInstitutionId": 0, "Name": "Kevwe & fSons 234", "ShortName": "QBDfff234325", "Narration": "John & Sons", "CustomerField1": "Mobile Number", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "kevfwe-sons-234", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 14513, "PayDirectProductId": 4332, "PayDirectInstitutionId": 0, "Name": "Kevwe & Sons3", "ShortName": "QBD234324", "Narration": "John & Sons", "CustomerField1": "Mobile Number", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "kevwe-sons2", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 14514, "PayDirectProductId": 4332, "PayDirectInstitutionId": 0, "Name": "Kevwe & Sons3", "ShortName": "QBD234324q", "Narration": "John & Sons", "CustomerField1": "Mobile Number", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "kevwe-sons2", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 14515, "PayDirectProductId": 4332, "PayDirectInstitutionId": 0, "Name": "Kevwe & Sons34", "ShortName": "QBD234324q", "Narration": "John & Sons", "CustomerField1": "Mobile Number", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "kevwe-sons2", "SupportEmail": "<EMAIL>", "RiskCategoryId": "", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 127, "PayDirectProductId": 3517, "PayDirectInstitutionId": 0, "Name": "Latin Dance Club", "ShortName": "LDC", "Narration": "Register to Audition", "CustomerField1": "mobile number", "CustomerField2": "", "LogoUrl": "ldc.jpeg", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "thespiritedactor", "SupportEmail": "<EMAIL>", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 167, "PayDirectProductId": 4280, "PayDirectInstitutionId": 0, "Name": "Load Gift Card", "ShortName": "LGC", "Narration": "Load Gift Card", "CustomerField1": "Order no", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "lgc", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 307, "PayDirectProductId": 163, "PayDirectInstitutionId": 0, "Name": "LUC PAYDirect", "ShortName": "LUC", "Narration": "LUC Paydirect Collections", "CustomerField1": "luc no", "CustomerField2": "", "LogoUrl": "q.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "luc", "SupportEmail": "", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 548, "PayDirectProductId": 3496, "PayDirectInstitutionId": 0, "Name": "Mo<PERSON>el", "ShortName": "MBT", "Narration": "Mobitel Internet", "CustomerField1": "customer no", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mobitel", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "aa0bdabd-ea0d-480d-a1c5-224b4aeda432", "AmountType": 0}, {"Type": "", "Id": 314, "PayDirectProductId": 3731, "PayDirectInstitutionId": 0, "Name": "MYBETCITY", "ShortName": "MBC", "Narration": "BETCITY LTD Collections", "CustomerField1": "MBC ID", "LogoUrl": "mybetcity.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mybetcity", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "468613cc-1443-4eaf-b46e-37e468390cd1", "AmountType": 0}, {"Type": "", "Id": 17574, "PayDirectProductId": 4560, "PayDirectInstitutionId": 0, "Name": "NewBillerTest", "ShortName": "NBTest", "Narration": "New Biller Testing", "CustomerField1": "NewBillerTest", "LogoUrl": "q.gif", "Surcharge": "9999", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "NewBillerTest", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "PCL", "Id": 16920, "PayDirectProductId": 4525, "PayDirectInstitutionId": 0, "Name": "Nimc e-Account", "ShortName": "NEA", "Narration": "Nimc e-Account ", "CustomerField1": "Nimc Identfication Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "nimc", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 512, "PayDirectProductId": 3778, "PayDirectInstitutionId": 0, "Name": "Oando EVAP", "ShortName": "OEVAP", "Narration": "OEVAP", "CustomerField1": "Invoice Number", "LogoUrl": "<PERSON><PERSON><PERSON>", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "oevap", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16202", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Oando EVAP", "PaymentOptionsTitle": "Oando EVAP", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 511, "PayDirectProductId": 151, "PayDirectInstitutionId": 0, "Name": "Oando Marketing Plc", "ShortName": "OML", "Narration": "OML", "LogoUrl": "Oando logo", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "oando", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16202", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Oando Marketing Plc", "PaymentOptionsTitle": "Oando Marketing Plc", "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "43ffdd9b-90d5-439c-9c81-fde1d66b75d8", "AmountType": 0}, {"Type": "PHV", "Id": 16912, "PayDirectProductId": 4513, "PayDirectInstitutionId": 0, "Name": "PayMerchant", "ShortName": "PAMX", "Narration": "PayMerchant for Merchant X", "CustomerField1": "Customer Reference", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "PayMerchant", "SupportEmail": "<EMAIL>", "NetworkId": "62805104117", "ProductCode": "628051041111", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "PHV", "Id": 16208, "PayDirectProductId": 4435, "PayDirectInstitutionId": 0, "Name": "PHCN EKEDP", "ShortName": "EKEDP", "Narration": "PHCN EKEDP", "CustomerField1": "METER NO", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ekedp", "SupportEmail": "<EMAIL>", "NetworkId": "**********", "ProductCode": "628051501113", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 151, "PayDirectProductId": 3410, "PayDirectInstitutionId": 0, "Name": "Premier Lotto Dealer Payments", "ShortName": "plotto", "Narration": "Dealer Payments", "CustomerField1": "Dealer No", "CustomerField2": "", "LogoUrl": "p.gif", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "premierlotto", "SupportEmail": "", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 941, "PayDirectProductId": 3782, "PayDirectInstitutionId": 0, "Name": "QT Event Ticketing Services", "ShortName": "QTTickets", "Narration": "Quickteller Event Tickets", "CustomerField1": "Phone Number (2348032222342, 2347025678901)", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "qteventtickets", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 940, "PayDirectProductId": 3767, "PayDirectInstitutionId": 0, "Name": "Quickteller Event Tickets", "ShortName": "QTEvents", "Narration": "Quickteller Event Tickets-Buy your Ticket", "CustomerField1": "Email Address of Ticket Recipient", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "quicktellertickets", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 16899, "PayDirectProductId": 4481, "PayDirectInstitutionId": 0, "Name": "Quickteller Luxury Mall", "ShortName": "Mall4Afric", "Narration": "Quickteller Luxury Mall", "CustomerField1": "Order Id", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "luxurymall", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 17575, "PayDirectProductId": 4562, "PayDirectInstitutionId": 0, "Name": "RetailPay Funding", "ShortName": "RPF", "Narration": "RetailPay Funding", "CustomerField1": "Dealer Code", "LogoUrl": "RetailPayLogo", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "retailPayFunding", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "SmallImageId": "06ca582b-3344-4cd8-8b24-8082d338020f", "LargeImageId": "06ca582b-3344-4cd8-8b24-8082d338020f", "MediumImageId": "06ca582b-3344-4cd8-8b24-8082d338020f", "AmountType": 0}, {"Type": "", "Id": 894, "PayDirectProductId": 4248, "PayDirectInstitutionId": 0, "Name": "Safe Token Reg", "ShortName": "STR", "Narration": "ATM Registration", "CustomerField1": "Mobile Number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "safereg", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16202", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 411, "PayDirectProductId": 3777, "PayDirectInstitutionId": 0, "Name": "Society of Petroleum Engineers Nigeria Council", "ShortName": "SPENC", "Narration": "SPE Nigeria Council", "CustomerField1": "Registration Number", "LogoUrl": "SPENC.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "spenc", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "18062f3a-b94a-4087-8e09-9704164bfd11", "AmountType": 0}, {"Type": "", "Id": 302, "PayDirectProductId": 3327, "PayDirectInstitutionId": 0, "Name": "SOFORTE Ent. Distribution Ltd", "ShortName": "SOFORT", "Narration": "SOFORTE Collections", "CustomerField1": "ORDER NO", "CustomerField2": "", "LogoUrl": "soforte.bmp", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "soforte", "SupportEmail": "<EMAIL>", "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "185a27c2-9466-4666-a209-3c0576344c3a", "AmountType": 0}, {"Type": "", "Id": 371, "PayDirectProductId": 3718, "PayDirectInstitutionId": 0, "Name": "Stanbic IBTC Pension", "ShortName": "SPEN", "Narration": "Stanbic IBTC Pension", "CustomerField1": "PIN", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "stanbicpensions", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 16463, "PayDirectProductId": 4443, "PayDirectInstitutionId": 0, "Name": "TestTest", "ShortName": "TestTest", "CustomerField1": "Phone number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "directtest", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 16631, "PayDirectProductId": 4466, "PayDirectInstitutionId": 0, "Name": "Total TomCard", "ShortName": "TotalTom", "Narration": "Wallet Top Up", "CustomerField1": "Customer Reference No", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "TotalTom", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsTitle": "Total TomCard", "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 13585, "PayDirectProductId": 4502, "PayDirectInstitutionId": 0, "Name": "TSA", "ShortName": "TSA", "Narration": "TSA", "CustomerField1": "Customer Number", "CustomerField2": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "tsa", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 16904, "PayDirectProductId": 4502, "PayDirectInstitutionId": 0, "Name": "TSABiller", "ShortName": "TSALT", "Narration": "TSA Biller Enterprises", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "<PERSON><PERSON><PERSON><PERSON>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}, {"Type": "", "Id": 982, "PayDirectProductId": 4313, "PayDirectInstitutionId": 0, "Name": "Verve eCash", "ShortName": "QTA", "Narration": "Verve eCash", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "eaccount", "CustomMessageUrl": "testing 1234", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "MediumImageId": "d96f4123-c1ea-4946-898d-9e8e069413d4", "CustomMessageId": "982", "AmountType": 0}, {"Type": "PCL", "Id": 8805, "PayDirectProductId": 4313, "PayDirectInstitutionId": 0, "Name": "Verve eCash Web", "ShortName": "VerveECash", "Narration": "Verve eCash Web", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "eaccountweb", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "SmallImageId": "d96f4123-c1ea-4946-898d-9e8e069413d4", "AmountType": 0}, {"Type": "", "Id": 16911, "PayDirectProductId": 4510, "PayDirectInstitutionId": 0, "Name": "Verve GTB", "ShortName": "VerveGTB", "CustomerField1": "CustomerID", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "vervegtb", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 14, "CategoryName": "Others", "AmountType": 0}]}, {"Id": 8, "Name": "Phone Bills", "Description": "Pay all post paid phone bills", "Billers": [{"Type": "PHV", "Id": 16554, "PayDirectProductId": 4460, "PayDirectInstitutionId": 0, "Name": "9mobile Postpaid", "ShortName": "9mobile", "Narration": "9mobile Postpaid", "CustomerField1": "Customer No", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ninemobile", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 8, "CategoryName": "Phone Bills", "AmountType": 0}, {"Type": "MO", "Id": 902, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "MTN e-Charge Postpaid", "ShortName": "MTNVTU", "Narration": "Pay some or part of your MTN bills through e-Charge", "CustomerField1": "Phone Number", "LogoUrl": "mtn.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtnvtu", "SupportEmail": "<EMAIL>", "NetworkId": "628051043", "ProductCode": "6280510431", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 8, "CategoryName": "Phone Bills", "MediumImageId": "7389beb8-36fe-4c1d-bba0-56635ff4bfe0", "AmountType": 0}, {"Type": "", "Id": 106, "PayDirectProductId": 99, "PayDirectInstitutionId": 0, "Name": "MTN via UBA", "ShortName": "UBA-MTN", "CustomerField1": "Phone Number", "CustomerField2": "Customer Name", "LogoUrl": "ubamtn.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtnuba", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 8, "CategoryName": "Phone Bills", "AmountType": 0}, {"Type": "MO", "Id": 15606, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "NTEL", "ShortName": "NTEL", "Narration": "NTEL Virtual Top-up", "CustomerField1": "Mobile Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ntelvtu", "SupportEmail": "<EMAIL>", "NetworkId": "628051045", "ProductCode": "628051047", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 8, "CategoryName": "Phone Bills", "AmountType": 0}, {"Type": "", "Id": 17581, "PayDirectProductId": 3593, "PayDirectInstitutionId": 0, "Name": "Zoom Mobile", "ShortName": "ZM", "Narration": "Zoom Payments", "CustomerField1": "Cust Id", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "zoom", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 8, "CategoryName": "Phone Bills", "AmountType": 0}]}, {"Id": 3, "Name": "State Payments", "Description": "Pay state taxes", "Billers": [{"Type": "", "Id": 303, "PayDirectProductId": 3332, "PayDirectInstitutionId": 0, "Name": "Abia State Infrastructural Development Agency", "ShortName": "ASIDA", "Narration": "Pay", "CustomerField1": "Customer No", "LogoUrl": "q.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "asida", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 3, "CategoryName": "State Payments", "AmountType": 4}, {"Type": "", "Id": 152, "PayDirectProductId": 123, "PayDirectInstitutionId": 0, "Name": "Cross River State", "ShortName": "CRSG", "Narration": "Cross river state collections", "CustomerField1": "TIN", "LogoUrl": "c.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "crsg", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 3, "CategoryName": "State Payments", "AmountType": 5}]}, {"Id": 15, "Name": "Airlines", "Description": "Airlines", "Billers": [{"Type": "", "Id": 405, "PayDirectProductId": 3492, "PayDirectInstitutionId": 0, "Name": "Aero Mobile - Book on Holds", "ShortName": "ACNMOB", "Narration": "Aero Mobile - Book on Holds", "CustomerField1": "Booking References", "LogoUrl": "aero.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "aeromobile", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 15, "CategoryName": "Airlines", "MediumImageId": "2c587096-f28b-4662-bd61-803f6cc6bf2e", "AmountType": 3}, {"Type": "", "Id": 155, "PayDirectProductId": 3345, "PayDirectInstitutionId": 0, "Name": "AirNigeria BOH", "ShortName": "AIRNG", "Narration": "AirNigeria BookOnHold", "CustomerField1": " Booking Reference No:", "CustomerField2": "", "LogoUrl": "airng.png", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "airnigeria", "SupportEmail": "<EMAIL>,<EMAIL>", "CategoryId": 15, "CategoryName": "Airlines", "AmountType": 0}, {"Type": "", "Id": 134, "PayDirectProductId": 3581, "PayDirectInstitutionId": 0, "Name": "Arik Air Book on Hold", "ShortName": "ARIK", "Narration": "Arik Air Book on Hold", "CustomerField1": "Cutomer Reference", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "arik", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 15, "CategoryName": "Airlines", "AmountType": 5}, {"Type": "", "Id": 213, "PayDirectProductId": 3568, "PayDirectInstitutionId": 0, "Name": "British Airways  GTB POS", "ShortName": "BAPOS", "Narration": "British Airways GTB POS Collections", "CustomerField1": "PNR No.", "LogoUrl": "british.JPG", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "bapos", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "CategoryId": 15, "CategoryName": "Airlines", "AmountType": 0}, {"Type": "", "Id": 406, "PayDirectProductId": 3563, "PayDirectInstitutionId": 0, "Name": "Dana Air - Book On Hold", "ShortName": "DANA", "Narration": "Dana Air Book On Hold Collections", "CustomerField1": "PNR No.", "LogoUrl": "dana air logo.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "dana<PERSON>", "SupportEmail": "chevening scholarship 2012", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 15, "CategoryName": "Airlines", "AmountType": 0}, {"Type": "PHV", "Id": 17603, "PayDirectProductId": 4582, "PayDirectInstitutionId": 0, "Name": "Kano Electricity", "ShortName": "Kedco", "Narration": "Kano Electricity", "CustomerField1": "Meter No", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "kedco", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 15, "CategoryName": "Airlines", "AmountType": 0}, {"Type": "", "Id": 403, "PayDirectProductId": 3492, "PayDirectInstitutionId": 0, "Name": "MoBishara Book on Hold", "ShortName": "BAAS", "Narration": "MoBishara Book on Hold", "CustomerField1": "Booking reference", "LogoUrl": "q.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "slimtrader", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 15, "CategoryName": "Airlines", "AmountType": 5}]}, {"Id": 13, "Name": "Insurance/Smart", "Description": "Insurance Payments", "Billers": [{"Type": "", "Id": 391, "PayDirectProductId": 3602, "PayDirectInstitutionId": 0, "Name": "Aiico Insurance Plc", "ShortName": "Aiico Insurance", "Narration": "Aiico Insurance Plc Premiunm Collections", "CustomerField1": "Policy Number", "LogoUrl": "aiico.JPG", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "aiicoinsurance", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "MediumImageId": "ec33eddc-7d33-43be-8528-1677030b13de", "AmountType": 0}, {"Type": "", "Id": 17182, "PayDirectProductId": 4547, "PayDirectInstitutionId": 0, "Name": "AXAMANSARD HEALTH", "ShortName": "AXAMANSARD", "CustomerField1": "Reference", "LogoUrl": "axa-hmo.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "a<PERSON><PERSON><PERSON>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "SmallImageId": "8895b1c6-b41f-4c1c-a562-0d564250836f", "LargeImageId": "8895b1c6-b41f-4c1c-a562-0d564250834f", "MediumImageId": "8895b1c6-b41f-4c1c-a562-0d564250835f", "AmountType": 5}, {"Type": "", "Id": 207, "PayDirectProductId": 3597, "PayDirectInstitutionId": 0, "Name": "Custodian and Allied Insurance", "ShortName": "CUSTODIAN", "CustomerField1": "Customer No", "LogoUrl": "custodian.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "custodian", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "MediumImageId": "5bb988d1-0800-4dc8-9388-fbb3c4c01d83", "AmountType": 0}, {"Type": "", "Id": 129, "PayDirectProductId": 3526, "PayDirectInstitutionId": 0, "Name": "Geo NL distributors", "ShortName": "GNLD", "Narration": "payments", "CustomerField1": "distributor ID", "CustomerField2": "", "LogoUrl": "gnld.jpg", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "gnld", "SupportEmail": "", "RiskCategoryId": "16203", "CategoryId": 13, "CategoryName": "Insurance/Smart", "MediumImageId": "ece45d17-b00d-4575-b6c6-6f52cc62f976", "AmountType": 0}, {"Type": "", "Id": 208, "PayDirectProductId": 3600, "PayDirectInstitutionId": 0, "Name": "GTB Asset Management", "ShortName": "GTBASSET", "Narration": "GTB Asset ", "CustomerField1": "Account Number", "LogoUrl": "gtbasset.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "gtbasset", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "MediumImageId": "9a77439b-a86c-415d-8eb7-1ff899ee4c14", "AmountType": 0}, {"Type": "", "Id": 900, "PayDirectProductId": 3310, "PayDirectInstitutionId": 0, "Name": "Gurantee Trust Assurance", "ShortName": "GTA", "CustomerField1": "Customer Insurance Number", "LogoUrl": "qt.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "gta", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [{"Title": "Beneficairy Number", "IsRequired": true, "SortOrder": 0, "ElementType": "TextBox", "DataType": "String", "Length": 10, "SelectItems": [], "ElementName": "Beneficiary No"}, {"Title": "Beneficairy Date of Birth(DD/MM/YYYY))", "IsRequired": true, "SortOrder": 5, "ElementType": "TextBox", "DataType": "Numeric", "Length": 10, "SelectItems": [], "ElementName": "BeneficiaryDOB"}, {"Title": "Insured Date of Birth(DD/MM/YYYY))", "IsRequired": true, "SortOrder": 4, "ElementType": "TextBox", "DataType": "Numeric", "Length": 10, "SelectItems": [], "ElementName": "InsuredDOB"}, {"Title": "Beneficiary First Name", "IsRequired": true, "SortOrder": 1, "ElementType": "TextBox", "DataType": "String", "Length": 20, "SelectItems": [], "ElementName": "BFN"}, {"Title": "Beneficiary Surname", "IsRequired": true, "SortOrder": 3, "ElementType": "TextBox", "DataType": "String", "Length": 20, "SelectItems": [], "ElementName": "BSN"}, {"Title": "Beneficairy <PERSON>", "IsRequired": true, "SortOrder": 2, "ElementType": "TextBox", "DataType": "String", "Length": 20, "SelectItems": [], "ElementName": "BMN"}], "FinishButtonName": "Pay", "StartPage": "Payment/PaymentDetails", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "MediumImageId": "4693e4bc-07e0-4f3e-805d-f6035435410c", "AmountType": 0}, {"Type": "", "Id": 206, "PayDirectProductId": 3541, "PayDirectInstitutionId": 0, "Name": "Lead Way Assurance", "ShortName": "LWAY", "Narration": "Lead Way Assurance", "CustomerField1": "Policy Number", "LogoUrl": "lway.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "leadway", "SupportEmail": "<EMAIL>, <EMAIL>", "RiskCategoryId": "16202", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "AmountType": 0}, {"Type": "", "Id": 16927, "PayDirectProductId": 4537, "PayDirectInstitutionId": 0, "Name": "METROHEALTH", "ShortName": "METRO", "CustomerField1": "Reference", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "metrohealth", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "AmountType": 5}, {"Type": "", "Id": 724, "PayDirectProductId": 4344, "PayDirectInstitutionId": 0, "Name": "quickhealth", "ShortName": "qh", "Narration": "Quickteller Health Insurance", "CustomerField1": "Pollicy No", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "quickhealthv2", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "AmountType": 0}, {"Type": "", "Id": 15515, "PayDirectProductId": 4387, "PayDirectInstitutionId": 0, "Name": "Quickteller e-Insurance", "ShortName": "QINS", "Narration": "Quickteller e-Insurance", "CustomerField1": "Policy/Quote Ref", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "qins", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "AmountType": 5}, {"Type": "", "Id": 8716, "PayDirectProductId": 4346, "PayDirectInstitutionId": 0, "Name": "quicktellerlife", "ShortName": "qtl", "Narration": "Quickteller Life Insurance", "CustomerField1": "Policy No", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "quicklife", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "AmountType": 0}, {"Type": "", "Id": 17035, "PayDirectProductId": 4541, "PayDirectInstitutionId": 0, "Name": "RELIANCE HEALTH", "ShortName": "RELIANCE", "CustomerField1": "Reference", "LogoUrl": "reliance-hmo.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "reliance", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "SmallImageId": "031ee060-67bc-460d-8847-e4a7c5e45a27", "LargeImageId": "8895b1c6-b41f-4c1c-a562-0d564250824f", "MediumImageId": "8895b1c6-b41f-4c1c-a562-0d564250815f", "AmountType": 5}, {"Type": "", "Id": 17562, "PayDirectProductId": 4557, "PayDirectInstitutionId": 0, "Name": "Royal Exchange", "ShortName": "ROYALXCHG", "CustomerField1": "Reference Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "royalexchange", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "AmountType": 5}, {"Type": "", "Id": 879, "PayDirectProductId": 4243, "PayDirectInstitutionId": 0, "Name": "SmartHMO", "ShortName": "SMH", "Narration": "Smarthealth", "CustomerField1": "Policy No", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "<PERSON><PERSON><PERSON>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 13, "CategoryName": "Insurance/Smart", "AmountType": 0}, {"Type": "", "Id": 661, "PayDirectProductId": 4017, "PayDirectInstitutionId": 0, "Name": "Stanbic IBTC Asset Management Limited", "ShortName": "SIAML", "Narration": "<PERSON><PERSON> Asset Mgt", "CustomerField1": "Account Number", "LogoUrl": "Sstanasset", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "siaml", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Stanbic IBTC Asset Management Limited", "CategoryId": 13, "CategoryName": "Insurance/Smart", "MediumImageId": "89ae091d-fde1-4b83-9dda-c31df21193c5", "AmountType": 0}]}, {"Id": 23, "Name": "Event Tickets", "Description": "Event Tickets", "Billers": [{"Type": "", "Id": 521, "PayDirectProductId": 3884, "PayDirectInstitutionId": 0, "Name": "Ariiya.Com Limited", "ShortName": "<PERSON><PERSON>", "Narration": "Ariiya.Com Limited", "CustomerField1": "Customer ID", "LogoUrl": "riya", "Surcharge": "10055", "CurrencyCode": "270", "CurrencySymbol": "GMD", "QuickTellerSiteUrlName": "<PERSON><PERSON><PERSON>", "SupportEmail": "<EMAIL>", "CustomMessageUrl": "<p>Night of a 1000 Laughs</p><p>Eko Hotel</p><p>December 1, 2014</p>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Ariiya.Com Limited", "CategoryId": 23, "CategoryName": "Event Tickets", "CustomMessageId": "521", "AmountType": 0}]}, {"Id": 17, "Name": "Products and Services", "Description": "Products and Services", "Billers": [{"Type": "", "Id": 312, "PayDirectProductId": 3628, "PayDirectInstitutionId": 0, "Name": "Arit of Africa (Eset Nigeria and Ghana)", "ShortName": "Arit", "Narration": "Arit of Africa", "CustomerField1": "Mobile Phone Number", "LogoUrl": "Arit.jpeg", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "Arit", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "f829e419-c2c1-48eb-aea8-0630c4acbc58", "AmountType": 0}, {"Type": "", "Id": 315, "PayDirectProductId": 3666, "PayDirectInstitutionId": 0, "Name": "Best D8", "ShortName": "BD8", "Narration": "Best D8", "CustomerField1": "User ID", "LogoUrl": "best_D8", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "bestd8", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "04e8fe6a-4e10-4291-8cab-72efee6e95b0", "AmountType": 0}, {"Type": "", "Id": 603, "PayDirectProductId": 3561, "PayDirectInstitutionId": 0, "Name": "BGL Plc", "ShortName": "BGLPLC", "Narration": "BGL Plc collections", "CustomerField1": "Customer <PERSON><PERSON>", "LogoUrl": "bgl.jpeg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "bgl", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "d6906fcd-1d92-4eaa-8aa9-a6402d32e5de", "AmountType": 0}, {"Type": "", "Id": 212, "PayDirectProductId": 3555, "PayDirectInstitutionId": 0, "Name": "<PERSON><PERSON>", "ShortName": "BTR2", "Narration": "<PERSON><PERSON> Others Collections", "CustomerField1": "Customer Name", "CustomerField2": "", "LogoUrl": "Nestle Logo.jpg", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "but<PERSON><PERSON><PERSON><PERSON><PERSON>", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 130, "PayDirectProductId": 3547, "PayDirectInstitutionId": 0, "Name": "Cherries Gifts", "ShortName": "CSG", "Narration": "Cherries Supermarket and Gifts Limited", "CustomerField1": "Order Number", "LogoUrl": "cherries gifts.JPG", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "cherriesgifts", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "2587f05a-2efa-4bd3-ada9-2b1724ed1591", "AmountType": 0}, {"Type": "", "Id": 343, "PayDirectProductId": 3770, "PayDirectInstitutionId": 0, "Name": "Concept Nova Limited", "ShortName": "CNL", "Narration": "Concept Nova Limited", "CustomerField1": "Payee ID", "LogoUrl": "ConceptNova.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "concept-nova", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Concept Nova Ltd", "PaymentOptionsTitle": "concept Nova Ltd", "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 445, "PayDirectProductId": 3768, "PayDirectInstitutionId": 0, "Name": "Cornerstone Savings & Loan ", "ShortName": "CSLB", "Narration": "Cornerstone Savings & Loan ", "CustomerField1": "Account Number", "LogoUrl": "cornerstone", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "cornerstone", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16204", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "7d13f35d-6df4-4785-84c5-d817c4623ab7", "AmountType": 0}, {"Type": "", "Id": 892, "PayDirectProductId": 3977, "PayDirectInstitutionId": 0, "Name": "CRSg Quarry Collections", "ShortName": "CQC", "Narration": "CRSG Quarry Collections", "CustomerField1": "Vechile Reg No", "CustomerField2": "Quarry Code", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "crsgquarry", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 341, "PayDirectProductId": 3761, "PayDirectInstitutionId": 0, "Name": "Datashores Nigeria Limited", "ShortName": "DSNL", "Narration": "Datashores Nigeria Limited", "CustomerField1": "Email Address", "LogoUrl": "DataShores.JPG", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "datashores", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "bdf41405-cfe9-45f1-9109-99f58d01d7dd", "AmountType": 0}, {"Type": "", "Id": 950, "PayDirectProductId": 142, "PayDirectInstitutionId": 0, "Name": "Etisalat Devices", "ShortName": "<PERSON><PERSON><PERSON><PERSON>", "Narration": "Etisalat Devices", "CustomerField1": "Customer Id", "CustomerField2": "Mobile No", "LogoUrl": "etisalat.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "etisalatdevices", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "39aaf452-ae0a-4ec4-93c3-e38ad6e266b2", "AmountType": 0}, {"Type": "", "Id": 450, "PayDirectProductId": 3792, "PayDirectInstitutionId": 0, "Name": "Gidi Mobile Ltd", "ShortName": "Gidi Mobile Ltd", "Narration": "Gidi Mobile Ltd", "CustomerField1": "Phone Number", "LogoUrl": "gidimobile.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "gidimobile", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 556, "PayDirectProductId": 3903, "PayDirectInstitutionId": 0, "Name": "Icadia Gift Cards", "ShortName": "Icadia ", "Narration": "Icadia Gift Cards", "CustomerField1": "Customer ID", "LogoUrl": "ICADIA", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "icadia", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Icadia Gift Cards", "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "7107f479-f189-4bf5-94e1-c4deb3a1a09e", "AmountType": 0}, {"Type": "", "Id": 124, "PayDirectProductId": 3493, "PayDirectInstitutionId": 0, "Name": "Kingdom Africa", "ShortName": "KDA", "Narration": "products", "CustomerField1": "mobile number", "LogoUrl": "kingdomafrica.jpeg", "Surcharge": "10000", "CurrencyCode": "404", "CurrencySymbol": "KES", "QuickTellerSiteUrlName": "kingdomafrica", "SupportEmail": "<EMAIL>, <EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "c2f293e9-362b-47a1-9e70-caf182686b2a", "AmountType": 0}, {"Type": "", "Id": 14663, "PayDirectProductId": 3735, "PayDirectInstitutionId": 0, "Name": "<PERSON><PERSON><PERSON>", "ShortName": "<PERSON><PERSON><PERSON>", "Narration": "Lafarge payment", "CustomerField1": "Dealer code - Tonnage - Ship to Code ", "LogoUrl": "q.gif", "Surcharge": "1000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "<PERSON><PERSON><PERSON>", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 666, "PayDirectProductId": 4237, "PayDirectInstitutionId": 0, "Name": "LucidPetite", "ShortName": "LP", "Narration": "Retail Procurement", "CustomerField1": "Order Number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "lucidpetite", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 915, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "LushiousDeals ", "ShortName": "LSD", "Narration": "LushiousDeals ", "CustomerField1": "Order Code", "LogoUrl": "lush", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "lushiousdeals", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "fe77cc2e-5207-457d-bf3c-a833c55036af", "AmountType": 0}, {"Type": "", "Id": 457, "PayDirectProductId": 3837, "PayDirectInstitutionId": 0, "Name": "Media Range Ltd", "ShortName": "Media Range Ltd", "Narration": "Media Range Ltd", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mediarange", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "c86600dc-f5f9-46fa-87db-5321a2de259e", "AmountType": 0}, {"Type": "", "Id": 557, "PayDirectProductId": 3906, "PayDirectInstitutionId": 0, "Name": "Media Trust Limited", "ShortName": "TRUST", "Narration": "Media trust Limited", "CustomerField1": "Phone Number", "LogoUrl": "daily", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "dailytrust", "SupportEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON>@dailytrust.com,<EMAIL>,yus<PERSON><PERSON><PERSON><PERSON>@dailytrust.com", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Media Trust Limited", "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "9d3c8632-b64f-4cda-9508-119820783c9c", "AmountType": 0}, {"Type": "", "Id": 442, "PayDirectProductId": 3779, "PayDirectInstitutionId": 0, "Name": "Mocality", "ShortName": "MOCALITY", "Narration": "Mocality", "CustomerField1": "Order Number", "LogoUrl": "mocality.JPG", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mocality", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 610, "PayDirectProductId": 3780, "PayDirectInstitutionId": 0, "Name": "Momall, A Division Of Media 24", "ShortName": "<PERSON><PERSON>", "Narration": "<PERSON><PERSON>", "CustomerField1": "Order Number", "LogoUrl": "momall.JPEG", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "momall", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 443, "PayDirectProductId": 3787, "PayDirectInstitutionId": 0, "Name": "MySmartCityCards for CRSG", "ShortName": "MySmartCity ", "Narration": "MysmartCity Cards", "CustomerField1": "Enrollment ID", "LogoUrl": "MySmartCity.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mysmartcity", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "26e2d359-6e29-4541-92bc-57dfd0846526", "AmountType": 0}, {"Type": "", "Id": 555, "PayDirectProductId": 3886, "PayDirectInstitutionId": 0, "Name": "NairaBet - Get Altitude Nig Ltd", "ShortName": "NairaBet", "Narration": "NairaBet - Get Altitude Nig Ltd", "CustomerField1": "Customer Reference", "LogoUrl": "naira<PERSON>", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "naira<PERSON>", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "NairaBet - Get Altitude Nig Ltd", "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "0d8294ca-9fb2-4650-a499-c98da5085893", "AmountType": 0}, {"Type": "", "Id": 914, "PayDirectProductId": 4330, "PayDirectInstitutionId": 0, "Name": "o4oxide New", "ShortName": "o40", "Narration": "o4oxide ", "CustomerField1": "Order Code", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "o4oxide_new", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "21189954-b967-46a5-8151-71950e8bd4c8", "AmountType": 0}, {"Type": "", "Id": 930, "PayDirectProductId": 3527, "PayDirectInstitutionId": 0, "Name": "Oando Gas", "ShortName": "OGAS", "Narration": "Oando Gas", "CustomerField1": "Customer ID", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "<PERSON>and<PERSON><PERSON>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 808, "PayDirectProductId": 4287, "PayDirectInstitutionId": 0, "Name": "Ope and Sons Limited", "ShortName": "RC123456", "Narration": "Payments to Ope & Sons Limited", "CustomerField1": "Customer Reference", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ope-and-sons", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 344, "PayDirectProductId": 3766, "PayDirectInstitutionId": 0, "Name": "Paymente Limited", "ShortName": "PMTE", "Narration": "Paymente Limited", "CustomerField1": "Order ID", "LogoUrl": "paymente.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "paymente", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "22004e8b-4f8c-4930-bfcf-a1450661f491", "AmountType": 0}, {"Type": "", "Id": 999, "PayDirectProductId": 3519, "PayDirectInstitutionId": 0, "Name": "Peterson Items", "ShortName": "<PERSON>", "Narration": "he he", "CustomerField1": "mobilenumber", "CustomerField2": "customerid", "LogoUrl": "petermi", "Surcharge": "100", "CustomSectionUrl": "peter<PERSON>", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "peter", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 990, "PayDirectProductId": 3669, "PayDirectInstitutionId": 0, "Name": "QTA", "ShortName": "QTAPD", "Narration": "QTA Top-up", "CustomerField1": "QTA ID", "LogoUrl": "QT.GIF", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "qta", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 888, "PayDirectProductId": 4276, "PayDirectInstitutionId": 0, "Name": "SmartNet Hosting", "ShortName": "SNET", "Narration": "Pay for your hosting", "CustomerField1": "Order number", "LogoUrl": "SmartNet", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "smartnet", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 17, "CategoryName": "Products and Services", "AmountType": 0}, {"Type": "", "Id": 118, "PayDirectProductId": 3498, "PayDirectInstitutionId": 0, "Name": "Uk2Me", "ShortName": "U2M", "Narration": "retail procurement", "CustomerField1": "invoice number", "CustomerField2": "", "LogoUrl": "uk2me.jpeg", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "uk2me", "SupportEmail": "<EMAIL>, <EMAIL>", "CategoryId": 17, "CategoryName": "Products and Services", "MediumImageId": "0b25410a-99e0-4d40-bcf0-661762da139d", "AmountType": 0}]}, {"Id": 2, "Name": "Cable TV Bills", "Description": "Pay for your cable TV subscriptions here", "Billers": [{"Type": "", "Id": 1234, "PayDirectProductId": 4290, "PayDirectInstitutionId": 0, "Name": "<PERSON><PERSON>", "ShortName": "AYO", "Narration": "Creating test biller", "CustomerField1": "<PERSON><PERSON>", "LogoUrl": "q.gif", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ayo", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "MediumImageId": "cad63810-d771-4cce-9f89-284e87d53756", "AmountType": 0}, {"Type": "", "Id": 113, "PayDirectProductId": 130, "PayDirectInstitutionId": 0, "Name": "DAARSAT Communications", "ShortName": "DAARSAT", "Narration": "DAARSAT", "CustomerField1": "Decoder Number", "LogoUrl": "daarsat.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "daarsat", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "AmountType": 1}, {"Type": "", "Id": 480, "PayDirectProductId": 4290, "PayDirectInstitutionId": 0, "Name": "DSTV Mobile", "ShortName": "DSTVMobi", "Narration": "Mobile DSTV Payments", "CustomerField1": "Decoder Number", "LogoUrl": "q.gif", "Surcharge": "2500", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "dstvmobi", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "AmountType": 0}, {"Type": "", "Id": 121, "PayDirectProductId": 170, "PayDirectInstitutionId": 0, "Name": "HiTV", "ShortName": "HiTV", "Narration": "Pay your HiTV bills here", "CustomerField1": "Smart Card Number", "LogoUrl": "hitv.png", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "hitv", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16202", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "AmountType": 0}, {"Type": "", "Id": 16783, "PayDirectProductId": 4475, "PayDirectInstitutionId": 0, "Name": "IFIS DSTV", "ShortName": "IFISD", "Narration": "Testing Pricing Discount", "CustomerField1": "Decoder Number", "CustomerField2": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ifisd", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "AmountType": 0}, {"Type": "", "Id": 471, "PayDirectProductId": 3928, "PayDirectInstitutionId": 0, "Name": "iROKOtv", "ShortName": "IROKO", "Narration": "Iroko Partners UK Limited", "CustomerField1": "Email Address", "LogoUrl": "iroko.JPG", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "irokotv", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "MediumImageId": "d9fbe305-5b1d-4b51-afb6-9cfa522096da", "AmountType": 0}, {"Type": "", "Id": 104, "PayDirectProductId": 13, "PayDirectInstitutionId": 0, "Name": "Multichoice Limited", "ShortName": "MCU2", "Narration": "Pay DSTV bills", "CustomerField1": "IUC number/Customer number", "CustomerField2": "Phone Number", "LogoUrl": "mcn.gif", "Surcharge": "7700", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "dstv", "SupportEmail": "<EMAIL>", "CustomMessageUrl": "&lt;p&gt;&lt;img alt=\"Editor for ASP.NET MVC logo\" height=\"98\" src=\"http://www.kendoui.com/Image/kendo-logo.png\" style=\"display:block;margin-left:auto;margin-right:auto;\" width=\"283\" /&gt;&lt;/p&gt;&lt;p&gt;Kendo UI Editor allows your users to edit HTML in a familiar, user-friendly way.&lt;br /&gt;In this version, the Editor provides the core HTML editing engine, which includes basic text formatting, hyperlinks, lists, and image handling. The widget &lt;strong&gt;outputs identical HTML&lt;/strong&gt; across all major browsers, follows accessibility standards and provides API for content manipulation. &lt;/p&gt;&lt;p&gt;Features include:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Text formatting &amp;amp; alignment&lt;/li&gt;&lt;li&gt;Bulleted and numbered lists&lt;/li&gt;&lt;li&gt;Hyperlink and image dialogs&lt;/li&gt;&lt;li&gt;Cross-browser support&lt;/li&gt;&lt;li&gt;Identical HTML output across browsers&lt;/li&gt;&lt;li&gt;Gracefully degrades to a &lt;code&gt;textarea&lt;/code&gt; when JavaScript is turned off&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;Read &lt;a href=\"http://docs.kendoui.com\"&gt;more details&lt;/a&gt; or send us your &lt;a href=\"http://www.kendoui.com/forums.aspx\"&gt;feedback&lt;/a&gt;! &lt;/p&gt;", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "CustomMessageId": "104", "AmountType": 1}, {"Type": "", "Id": 16918, "PayDirectProductId": 4519, "PayDirectInstitutionId": 0, "Name": "Multichoice PAYDirect2", "ShortName": "PAYDirect2", "CustomerField1": "SmartCard No", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "MultichoicePAYDirect2", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "AmountType": 0}, {"Type": "", "Id": 112, "PayDirectProductId": 140, "PayDirectInstitutionId": 0, "Name": "MyTV", "ShortName": "MyTV", "Narration": "Pay MyTV bills", "CustomerField1": "Decoder numbers", "LogoUrl": "mytv.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mytv", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 2, "CategoryName": "Cable TV Bills", "SmallImageId": "d49ad941-bdc5-4b5d-8ca9-c46a26e912b7", "MediumImageId": "839cec5d-0f2e-4245-962e-827bc33f1198", "AmountType": 0}, {"Type": "", "Id": 128, "PayDirectProductId": 3525, "PayDirectInstitutionId": 0, "Name": "Pebble Gift ", "ShortName": "PGV", "Narration": "gift voucher payments", "CustomerField1": "Phone Number", "CustomerField2": "", "LogoUrl": "pglogo.jpeg", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "pebblegifts", "SupportEmail": "<EMAIL>, <EMAIL>", "RiskCategoryId": "16200", "CategoryId": 2, "CategoryName": "Cable TV Bills", "MediumImageId": "4a7e0c96-8cc1-4709-a338-81fc76d932af", "AmountType": 0}]}, {"Id": 19, "Name": "Microfinance", "Description": "Microfinance", "Billers": [{"Type": "", "Id": 205, "PayDirectProductId": 3447, "PayDirectInstitutionId": 0, "Name": "BankOne Microfinance Systems", "ShortName": "B1MFB", "Narration": "Fund Microfinance Accounts", "CustomerField1": "Microfinance Id", "CustomerField2": "", "LogoUrl": "qt.gif", "Url": "", "Surcharge": "0", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "bankone", "SupportEmail": "<EMAIL>", "CategoryId": 19, "CategoryName": "Microfinance", "AmountType": 0}, {"Type": "", "Id": 16959, "PayDirectProductId": 4539, "PayDirectInstitutionId": 0, "Name": "Ferratum", "ShortName": "FER", "Narration": "Pay Back Loan", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ferratum", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 19, "CategoryName": "Microfinance", "AmountType": 3}, {"Type": "", "Id": 16134, "PayDirectProductId": 4433, "PayDirectInstitutionId": 0, "Name": "Interswitch Lending Service", "ShortName": "ILS", "Narration": "Pay back your loan", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ils", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 19, "CategoryName": "Microfinance", "AmountType": 3}, {"Type": "", "Id": 16960, "PayDirectProductId": 4540, "PayDirectInstitutionId": 0, "Name": "MKT Loans", "ShortName": "MKT", "Narration": "Pay Back Loan", "CustomerField1": "Phone Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mkt", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 19, "CategoryName": "Microfinance", "AmountType": 3}, {"Type": "", "Id": 525, "PayDirectProductId": 4069, "PayDirectInstitutionId": 0, "Name": "Rencredit MFB Limited", "ShortName": "RCML", "Narration": "Rencredit MFB Limited", "CustomerField1": "Account Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "rencreditltd", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 19, "CategoryName": "Microfinance", "AmountType": 0}, {"Type": "", "Id": 8800, "PayDirectProductId": 4349, "PayDirectInstitutionId": 0, "Name": "Renmoney MFB Limited", "ShortName": "RENM", "Narration": "Renmoney MFB Limited", "CustomerField1": "Account Number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "renmoneyltd", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 19, "CategoryName": "Microfinance", "AmountType": 0}]}, {"Id": 7, "Name": "Donations", "Description": "Donate to a worthy cause", "Billers": [{"Type": "", "Id": 311, "PayDirectProductId": 3609, "PayDirectInstitutionId": 0, "Name": "Catholic Charismatic renewal of Nigeria (National ", "ShortName": "NST-CCRN", "Narration": "National Service Team - CCRN", "CustomerField1": "Payer Name", "LogoUrl": "ccrn.jpeg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ccrn", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 7, "CategoryName": "Donations", "AmountType": 0}, {"Type": "", "Id": 456, "PayDirectProductId": 3820, "PayDirectInstitutionId": 0, "Name": "Dawah Nigeria", "ShortName": "DAWAH", "Narration": "Dawah Nigeria", "CustomerField1": "Phone number", "LogoUrl": "dawah.png", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "dawah", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 7, "CategoryName": "Donations", "AmountType": 0}, {"Type": "", "Id": 305, "PayDirectProductId": 3342, "PayDirectInstitutionId": 0, "Name": "E-Religion", "ShortName": "EREG", "Narration": "E-Religion payments", "CustomerField1": "Mobile Phone", "CustomerField2": "", "LogoUrl": "qt.gif", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ereligion", "SupportEmail": "", "CategoryId": 7, "CategoryName": "Donations", "AmountType": 0}, {"Type": "", "Id": 800, "PayDirectProductId": 3311, "PayDirectInstitutionId": 0, "Name": "Freedom Foundation Donations", "ShortName": "FREEF", "Narration": "Freedom foundation donations", "CustomerField1": "Full Name", "CustomerField2": "Phone Number", "LogoUrl": "q.gif", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "freedomfoundation", "SupportEmail": "<EMAIL>", "CategoryId": 7, "CategoryName": "Donations", "MediumImageId": "968319ab-4f74-495f-a031-2db812f221da", "AmountType": 0}, {"Type": "", "Id": 291, "PayDirectProductId": 4077, "PayDirectInstitutionId": 0, "Name": "Grace Assembly", "ShortName": "Grace Assembly", "Narration": "Grace Assembly", "CustomerField1": "Phone Number", "LogoUrl": "GRACE", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "graceassembly", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Grace Assembly", "PaymentOptionsTitle": "Grace Assembly", "CategoryId": 7, "CategoryName": "Donations", "MediumImageId": "6c86b89a-0cd4-4377-bf5d-99fd5b8f265b", "AmountType": 0}, {"Type": "", "Id": 449, "PayDirectProductId": 3801, "PayDirectInstitutionId": 0, "Name": "Muslim Students Society of Nigeria (UI Branch)", "ShortName": "MSSN", "Narration": "Muslim students Society of Nigeria (UI Branch)", "CustomerField1": "Phone number", "LogoUrl": "mssn.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mssn", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 7, "CategoryName": "Donations", "AmountType": 0}, {"Type": "", "Id": 660, "PayDirectProductId": 4015, "PayDirectInstitutionId": 0, "Name": "Nigeria Flood Relief", "ShortName": "NFR", "Narration": "Nigeria Flood Relief", "CustomerField1": "Phone Number", "LogoUrl": "nfr.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "floodrelief", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 7, "CategoryName": "Donations", "AmountType": 0}, {"Type": "", "Id": 304, "PayDirectProductId": 3341, "PayDirectInstitutionId": 0, "Name": "Red Cross Society Of Nigeria", "ShortName": "RCS", "Narration": "Red Cross Donations", "CustomerField1": "Mobile Phone Number", "CustomerField2": "Full Name", "LogoUrl": "redcross_logo.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "redcross", "SupportEmail": "<EMAIL>", "CustomMessageUrl": "&lt;meta content=\"text/html; charset=utf-8\" http-equiv=\"Content-Type\" /&gt;&lt;title&gt;Newsletter&lt;/title&gt; &lt;!--[if gte mso 6]&gt; &lt;style&gt; table.mcnFollowContent {width:100% !important;} table.mcnShareContent {width:100% !important;} &lt;/style&gt; &lt;![endif]--&gt; &lt;style type=\"text/css\"&gt;#outlook a{ padding:0; } .ReadMsgBody{ width:100%; } .ExternalClass{ width:100%; } body{ margin:0; padding:0; } a{ word-wrap:break-word !important; } img{ border:0; height:auto !important; line-height:100%; outline:none; text-decoration:none; } table,td{ border-collapse:collapse; mso-table-lspace:0pt; mso-table-rspace:0pt; } #bodyTable,#bodyCell{ height:100% !important; margin:0; padding:0; width:100% !important; } #bodyCell{ padding:20px; } .mcnImage{ vertical-align:bottom; } .mcnTextContent img{ height:auto !important; } body,#bodyTable{ background-color:#F2F2F2; } #bodyCell{ border-top:0; } #templateContainer{ border:0; } h1{ color:#606060 !important; display:block; font-family:Helvetica; font-size:40px; font-style:normal; font-weight:bold; line-height:125%; letter-spacing:-1px; margin:0; text-align:left; } h2{ color:#404040 !important; display:block; font-family:Helvetica; font-size:26px; font-style:normal; font-weight:bold; line-height:125%; letter-spacing:-.75px; margin:0; text-align:left; } h3{ color:#606060 !important; display:block; font-family:Helvetica; font-size:18px; font-style:normal; font-weight:bold; line-height:125%; letter-spacing:-.5px; margin:0; text-align:left; } h4{ color:#808080 !important; display:block; font-family:Helvetica; font-size:16px; font-style:normal; font-weight:bold; line-height:125%; letter-spacing:normal; margin:0; text-align:left; } h1 a,h2 a,h3 a,h4 a{ color:#6DC6DD; font-weight:bold; text-decoration:none; } #templatePreheader{ background-color:#FFFFFF; border-top:0; border-bottom:2px solid #F2F2F2; } .preheaderContainer .mcnTextContent,.preheaderContainer .mcnTextContent p{ color:#606060; font-family:Helvetica; font-size:11px; line-height:125%; text-align:left; } .preheaderContainer .mcnTextContent a{ color:#606060; font-weight:normal; text-decoration:underline; } #templateHeader{ background-color:#FFFFFF; border-top:0; border-bottom:2px solid #F2F2F2; } .headerContainer .mcnTextContent,.headerContainer .mcnTextContent p{ color:#606060; font-family:Helvetica; font-size:15px; line-height:150%; text-align:left; } .headerContainer .mcnTextContent a{ color:#6DC6DD; font-weight:normal; text-decoration:underline; } #templateBody{ background-color:#FFFFFF; border-top:0; border-bottom:2px solid #F2F2F2; } .bodyContainer .mcnTextContent,.bodyContainer .mcnTextContent p{ color:#606060; font-family:Helvetica; font-size:15px; line-height:150%; text-align:left; } .bodyContainer .mcnTextContent a{ color:#6DC6DD; font-weight:normal; text-decoration:underline; } #templateFooter{ background-color:#FFFFFF; border-top:0; border-bottom:0; } .footerContainer .mcnTextContent,.footerContainer .mcnTextContent p{ color:#606060; font-family:Helvetica; font-size:11px; line-height:125%; text-align:left; } .footerContainer .mcnTextContent a{ color:#606060; font-weight:normal; text-decoration:underline; } @media only screen and (max-width: 480px){ body,table,td,p,a,li,blockquote{ -webkit-text-size-adjust:none !important; } } @media only screen and (max-width: 480px){ body{ width:100% !important; min-width:100% !important; } } @media only screen and (max-width: 480px){ td[id=bodyCell]{ padding:10px !important; } } @media only screen and (max-width: 480px){ table[class=mcnTextContentContainer]{ width:100% !important; } } @media only screen and (max-width: 480px){ table[class=mcnBoxedTextContentContainer]{ width:100% !important; } } @media only screen and (max-width: 480px){ table[class=mcpreview-image-uploader]{ width:100% !important; display:none !important; } } @media only screen and (max-width: 480px){ img[class=mcnImage]{ width:100% !important; } } @media only screen and (max-width: 480px){ table[class=mcnImageGroupContentContainer]{ width:100% !important; } } @media only screen and (max-width: 480px){ td[class=mcnImageGroupContent]{ padding:9px !important; } } @media only screen and (max-width: 480px){ td[class=mcnImageGroupBlockInner]{ padding-bottom:0 !important; padding-top:0 !important; } } @media only screen and (max-width: 480px){ tbody[class=mcnImageGroupBlockOuter]{ padding-bottom:9px !important; padding-top:9px !important; } } @media only screen and (max-width: 480px){ table[class=mcnCaptionTopContent],table[class=mcnCaptionBottomContent]{ width:100% !important; } } @media only screen and (max-width: 480px){ table[class=mcnCaptionLeftTextContentContainer],table[class=mcnCaptionRightTextContentContainer],table[class=mcnCaptionLeftImageContentContainer],table[class=mcnCaptionRightImageContentContainer],table[class=mcnImageCardLeftTextContentContainer],table[class=mcnImageCardRightTextContentContainer]{ width:100% !important; } } @media only screen and (max-width: 480px){ td[class=mcnImageCardLeftImageContent],td[class=mcnImageCardRightImageContent]{ padding-right:18px !important; padding-left:18px !important; padding-bottom:0 !important; } } @media only screen and (max-width: 480px){ td[class=mcnImageCardBottomImageContent]{ padding-bottom:9px !important; } } @media only screen and (max-width: 480px){ td[class=mcnImageCardTopImageContent]{ padding-top:18px !important; } } @media only screen and (max-width: 480px){ td[class=mcnImageCardLeftImageContent],td[class=mcnImageCardRightImageContent]{ padding-right:18px !important; padding-left:18px !important; padding-bottom:0 !important; } } @media only screen and (max-width: 480px){ td[class=mcnImageCardBottomImageContent]{ padding-bottom:9px !important; } } @media only screen and (max-width: 480px){ td[class=mcnImageCardTopImageContent]{ padding-top:18px !important; } } @media only screen and (max-width: 480px){ table[class=mcnCaptionLeftContentOuter] td[class=mcnTextContent],table[class=mcnCaptionRightContentOuter] td[class=mcnTextContent]{ padding-top:9px !important; } } @media only screen and (max-width: 480px){ td[class=mcnCaptionBlockInner] table[class=mcnCaptionTopContent]:last-child td[class=mcnTextContent]{ padding-top:18px !important; } } @media only screen and (max-width: 480px){ td[class=mcnBoxedTextContentColumn]{ padding-left:18px !important; padding-right:18px !important; } } @media only screen and (max-width: 480px){ table[id=templateContainer],table[id=templatePreheader],table[id=templateHeader],table[id=templateBody],table[id=templateFooter]{ max-width:600px !important; width:100% !important; } } @media only screen and (max-width: 480px){ h1{ font-size:24px !important; line-height:125% !important; } } @media only screen and (max-width: 480px){ h2{ font-size:20px !important; line-height:125% !important; } } @media only screen and (max-width: 480px){ h3{ font-size:18px !important; line-height:125% !important; } } @media only screen and (max-width: 480px){ h4{ font-size:16px !important; line-height:125% !important; } } @media only screen and (max-width: 480px){ table[class=mcnBoxedTextContentContainer] td[class=mcnTextContent]{ font-size:18px !important; line-height:125% !important; } } @media only screen and (max-width: 480px){ table[id=templatePreheader]{ display:block !important; } } @media only screen and (max-width: 480px){ td[class=preheaderContainer] td[class=mcnTextContent]{ font-size:14px !important; line-height:115% !important; padding-right:18px !important; padding-left:18px !important; } } @media only screen and (max-width: 480px){ td[class=headerContainer] td[class=mcnTextContent]{ font-size:18px !important; line-height:125% !important; padding-right:18px !important; padding-left:18px !important; } } @media only screen and (max-width: 480px){ td[class=bodyContainer] td[class=mcnTextContent]{ font-size:18px !important; line-height:125% !important; padding-right:18px !important; padding-left:18px !important; } } @media only screen and (max-width: 480px){ td[class=footerContainer] td[class=mcnTextContent]{ font-size:14px !important; line-height:115% !important; padding-right:18px !important; padding-left:18px !important; } } @media only screen and (max-width: 480px){ td[class=footerContainer] a[class=utilityLink]{ display:block !important; } }&lt;/style&gt; &lt;center&gt;&lt;table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" height=\"100%\" id=\"bodyTable\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;margin:0;padding:0;background-color:#F2F2F2;height:100% !important;width:100% !important;\" width=\"100%\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td align=\"center\" id=\"bodyCell\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;margin:0;padding:20px;border-top:0;height:100% !important;width:100% !important;\" valign=\"top\"&gt;&lt;!-- BEGIN TEMPLATE // --&gt; &lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" id=\"templateContainer\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;border:0;\" width=\"600\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td align=\"center\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;!-- BEGIN PREHEADER // --&gt; &lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" id=\"templatePreheader\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;background-color:#FFFFFF;border-top:0;border-bottom:2px solid #F2F2F2;\" width=\"600\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class=\"preheaderContainer\" style=\"padding-top:9px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;!-- // END PREHEADER --&gt; &lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td align=\"center\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;!-- BEGIN HEADER // --&gt; &lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" id=\"templateHeader\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;background-color:#FFFFFF;border-top:0;border-bottom:2px solid #F2F2F2;\" width=\"600\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class=\"headerContainer\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnImageBlock\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" width=\"100%\"&gt;&lt;tbody class=\"mcnImageBlockOuter\"&gt;&lt;tr&gt;&lt;td class=\"mcnImageBlockInner\" style=\"padding:9px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnImageContentContainer\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" width=\"100%\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class=\"mcnImageContent\" style=\"padding-right:9px;padding-left:9px;padding-top:0;padding-bottom:0;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;a class=\"\" href=\"https://www.quickteller.com/\" style=\"word-wrap:break-word !important;\" target=\"_blank\" title=\"\"&gt;&lt;img align=\"left\" alt=\"Quickteller Logo\" class=\"mcnImage\" height=\"NaN\" src=\"https://gallery.mailchimp.com/5dba363949b7ff2415401cdfc/images/QT_Logo_for_Newsletter.png\" style=\"max-width:178px;padding-bottom:0px;display:inline ! important;vertical-align:bottom;border:0px none;line-height:100%;outline:medium none;text-decoration:none;\" width=\"178\" /&gt;&lt;/a&gt; &lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnImageBlock\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" width=\"100%\"&gt;&lt;tbody class=\"mcnImageBlockOuter\"&gt;&lt;tr&gt;&lt;td class=\"mcnImageBlockInner\" style=\"padding:0px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnImageContentContainer\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" width=\"100%\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class=\"mcnImageContent\" style=\"padding-right:0px;padding-left:0px;padding-top:0;padding-bottom:0;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;br /&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td align=\"center\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" id=\"templateBody\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;background-color:#FFFFFF;border-top:0;border-bottom:2px solid #F2F2F2;\" width=\"600\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class=\"bodyContainer\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnTextBlock\" height=\"296\" style=\"border-collapse:collapse;\" width=\"319\"&gt;&lt;tbody class=\"mcnTextBlockOuter\"&gt;&lt;tr&gt;&lt;td class=\"mcnTextBlockInner\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnTextContentContainer\" height=\"273\" style=\"border-collapse:collapse;\" width=\"373\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class=\"mcnTextContent\" style=\"padding-top:9px;padding-right:18px;padding-bottom:9px;padding-left:18px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;color:#606060;font-family:Helvetica;font-size:15px;line-height:150%;text-align:left;\" valign=\"top\"&gt;&lt;h1 style=\"display:block;font-family:Helvetica;font-size:40px;font-style:normal;font-weight:bold;line-height:125%;letter-spacing:-1px;margin:0;text-align:left;color:#606060 !important;\"&gt;&lt;span style=\"font-size:12px;\"&gt;Free will donation&lt;/span&gt;&lt;span style=\"font-size:small;\"&gt; ,&lt;/span&gt;&lt;span style=\"font-size:12px;\"&gt;&lt;/span&gt;&lt;/h1&gt;&lt;p style=\"color:#606060;font-family:Helvetica;font-size:15px;line-height:150%;text-align:left;\"&gt;If you help God, God will help you.&lt;br /&gt;&lt;br /&gt;Life is give and take not just take.&lt;br /&gt;&lt;br /&gt;Contact <EMAIL> for any inquiries.&lt;br /&gt;&lt;br /&gt;Yours faithfully,&lt;br /&gt;Interswitch&lt;/p&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnImageCardBlock\" height=\"140\" style=\"border-collapse:collapse;\" width=\"339\"&gt;&lt;tbody class=\"mcnImageCardBlockOuter\"&gt;&lt;tr&gt;&lt;td class=\"mcnImageCardBlockInner\" style=\"padding-top:9px;padding-right:18px;padding-bottom:9px;padding-left:18px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnImageCardBottomContent\" height=\"122\" style=\"border:1px solid rgb(153, 153, 153);background-color:#ebebeb;border-collapse:collapse;\" width=\"314\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td align=\"left\" class=\"mcnImageCardBottomImageContent\" style=\"padding-top:0px;padding-right:0px;padding-bottom:0;padding-left:0px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;a class=\"\" href=\"https://myvervecard.com/\" style=\"word-wrap:break-word !important;\" target=\"_blank\" title=\"\"&gt;&lt;img alt=\"Have you stepped up to Verve?\" class=\"mcnImage\" height=\"163\" src=\"https://gallery.mailchimp.com/5dba363949b7ff2415401cdfc/images/Verve_Card_775_x_224_copy.jpg\" style=\"max-width:775px;border:0px none;line-height:100%;outline:medium none;text-decoration:none;vertical-align:bottom;\" width=\"274\" /&gt;&lt;/a&gt; &lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td class=\"mcnTextContent\" style=\"padding-top:9px;padding-right:18px;padding-bottom:9px;padding-left:18px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;color:#606060;font-family:Helvetica;font-size:15px;line-height:150%;text-align:left;\" valign=\"top\" width=\"546\"&gt;&lt;strong style=\"color:#808080;\"&gt;&lt;em&gt;Have you stepped up to Verve?&lt;/em&gt;&lt;/strong&gt; &lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;!-- // END BODY --&gt; &lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td align=\"center\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;!-- BEGIN FOOTER // --&gt; &lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" id=\"templateFooter\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;background-color:#FFFFFF;border-top:0;border-bottom:0;\" width=\"600\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class=\"footerContainer\" style=\"padding-bottom:9px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnTextBlock\" height=\"73\" style=\"border-collapse:collapse;\" width=\"330\"&gt;&lt;tbody class=\"mcnTextBlockOuter\"&gt;&lt;tr&gt;&lt;td class=\"mcnTextBlockInner\" style=\"border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;\" valign=\"top\"&gt;&lt;table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"mcnTextContentContainer\" height=\"73\" style=\"border-collapse:collapse;\" width=\"337\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class=\"mcnTextContent\" style=\"padding-top:9px;padding-right:18px;padding-bottom:9px;padding-left:18px;border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;color:#606060;font-family:Helvetica;font-size:11px;line-height:125%;text-align:left;\" valign=\"top\"&gt;&lt;em&gt;Copyright © 2013 Interswitch Limited, All rights reserved.&lt;/em&gt;&lt;br /&gt;&lt;br /&gt;&lt;br /&gt;&lt;br /&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;!-- // END FOOTER --&gt; &lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;!-- // END TEMPLATE --&gt; &lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/center&gt;&lt;center&gt;&lt;br /&gt;&lt;br /&gt;&lt;br /&gt;&lt;br /&gt;&lt;br /&gt;&lt;br /&gt;&lt;table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" id=\"canspamBarWrapper\" style=\"background-color:#FFFFFF;border-top:1px solid #E5E5E5;\" width=\"100%\"&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td align=\"center\" style=\"padding-top:20px;padding-bottom:20px;\" valign=\"top\"&gt;&lt;style type=\"text/css\"&gt;@media only screen and (max-width: 480px){ table[id=\"canspamBar\"] td{font-size:14px !important;} table[id=\"canspamBar\"] td a{display:block !important; margin-top:10px !important;} } &lt;/style&gt; &lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/center&gt;", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 7, "CategoryName": "Donations", "MediumImageId": "c21e7c2a-b440-48bc-8a3e-03a1086d23d7", "CustomMessageId": "304", "AmountType": 0}, {"Type": "PHV", "Id": 17596, "PayDirectProductId": 4600, "PayDirectInstitutionId": 0, "Name": "Smart Meter", "ShortName": "SM", "Narration": "Smart Meter", "CustomerField1": "CustomerId", "LogoUrl": "smartcard", "Surcharge": "1000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "cgmis", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 7, "CategoryName": "Donations", "AmountType": 0}, {"Type": "", "Id": 306, "PayDirectProductId": 3559, "PayDirectInstitutionId": 0, "Name": "United For Kids Foundation United For Kids Foundat", "ShortName": "UKF", "Narration": "Donations for United for Kids Foundation", "CustomerField1": "Payer ID", "LogoUrl": "ukf.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "UKF", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 7, "CategoryName": "Donations", "AmountType": 0}]}, {"Id": 22, "Name": "PayChoice", "Description": "Upperlink PayChoice Schools", "Billers": [{"Type": "", "Id": 523, "PayDirectProductId": 3563, "PayDirectInstitutionId": 0, "Name": "Church Collections", "ShortName": "CC", "Narration": "Church Collections", "CustomerField1": "Donor Name", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "churchcollections", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 22, "CategoryName": "PayChoice", "AmountType": 0}, {"Type": "", "Id": 309, "PayDirectProductId": 3542, "PayDirectInstitutionId": 0, "Name": "Covenant University", "ShortName": "CVU", "Narration": "Covenant University Fees Payment", "CustomerField1": "Transaction ID", "LogoUrl": "lcp.jpg", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "covenantuniversity", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 22, "CategoryName": "PayChoice", "AmountType": 0}, {"Type": "", "Id": 308, "PayDirectProductId": 3542, "PayDirectInstitutionId": 0, "Name": "Lagos City Polytechnic", "ShortName": "LCP", "Narration": "Lagos City Polytechnic School Fees Collections", "CustomerField1": "Transaction ID", "CustomerField2": "Agency Code", "LogoUrl": "lcp.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "lagoscitypolytechnic", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 22, "CategoryName": "PayChoice", "MediumImageId": "e53f3464-b6c5-4386-ba85-d709d4799e70", "AmountType": 0}, {"Type": "", "Id": 8000, "PayDirectProductId": 3932, "PayDirectInstitutionId": 0, "Name": "NovaSys Limited", "ShortName": "NovaSys", "Narration": "NovaSys Limited", "LogoUrl": "NovaSys", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "no<PERSON><PERSON>", "RiskCategoryId": "16203", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "NovaSys", "CategoryId": 22, "CategoryName": "PayChoice", "AmountType": 0}]}, {"Id": 16, "Name": "Transport", "Description": "Transport", "Billers": [{"Type": "", "Id": 126, "PayDirectProductId": 3506, "PayDirectInstitutionId": 0, "Name": "DHL shipping payments", "ShortName": "DHL", "Narration": "shipping payments", "CustomerField1": "shipping tracking number", "LogoUrl": "dhl.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "dhl", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16205", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 16, "CategoryName": "Transport", "MediumImageId": "47f34310-15d8-4f3c-a35a-e913829b2f1e", "AmountType": 0}, {"Type": "", "Id": 203, "PayDirectProductId": 3426, "PayDirectInstitutionId": 0, "Name": "EasyFuel", "ShortName": "EFL", "Narration": "token top up", "CustomerField1": "token number", "CustomerField2": "", "LogoUrl": "easyfuel.jpeg", "Url": "", "Surcharge": "10000", "CustomSectionUrl": "", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "easyfuel", "SupportEmail": "<EMAIL>", "CategoryId": 16, "CategoryName": "Transport", "MediumImageId": "c5ff0f03-47c2-4d4d-8ba2-098a2c1532b0", "AmountType": 0}, {"Type": "", "Id": 575, "PayDirectProductId": 3322, "PayDirectInstitutionId": 0, "Name": "Lekki ConcessionCompany Payments", "ShortName": "LCC", "CustomerField1": "Toll Account No,", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "lcc", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 16, "CategoryName": "Transport", "MediumImageId": "8c42cc75-e278-4157-bf3c-1d180787b619", "AmountType": 0}, {"Type": "", "Id": 17594, "PayDirectProductId": 4598, "PayDirectInstitutionId": 0, "Name": "Token Vending Platform", "ShortName": "TVP", "Narration": "Token vending limited", "CustomerField1": "Customer Id", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "tvp", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 16, "CategoryName": "Transport", "AmountType": 5}]}, {"Id": 20, "Name": "Mobile Money", "Description": "Load your mobile wallet", "Billers": [{"Type": "", "Id": 909, "PayDirectProductId": 3499, "PayDirectInstitutionId": 0, "Name": "FETS Mobile Money", "ShortName": "FETS", "Narration": "FETS Mobile Money - Load your wallet here", "CustomerField1": "MSISDN", "LogoUrl": "fets.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "FETS", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 20, "CategoryName": "Mobile Money", "MediumImageId": "3e18e529-2159-4f24-8ffa-d236588dbc44", "AmountType": 0}, {"Type": "", "Id": 459, "PayDirectProductId": 3817, "PayDirectInstitutionId": 0, "Name": "GTB Mobile Money", "ShortName": "GTBCI", "Narration": "GTB Mobile Money", "CustomerField1": "Wallet number", "LogoUrl": "gtb2.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "gtbcash-in", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16204", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 20, "CategoryName": "Mobile Money", "AmountType": 0}, {"Type": "MO", "Id": 271, "PayDirectProductId": 4071, "PayDirectInstitutionId": 0, "Name": "MTN Mobile Money", "ShortName": "MTNMM", "Narration": "Transfer To Wallet", "CustomerField1": "Phone Number", "LogoUrl": "mobile", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "mtnmobilemoney", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16201", "NetworkId": "6280510490", "ProductCode": "7777777", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "MTN Mobile Money", "PaymentOptionsTitle": "MTN Mobile Money", "CategoryId": 20, "CategoryName": "Mobile Money", "AmountType": 0}, {"Type": "", "Id": 135, "PayDirectProductId": 4220, "PayDirectInstitutionId": 0, "Name": "Paycom Mobile Money", "ShortName": "PCM", "Narration": "Paycom Mobile Money - Fund Wallet", "CustomerField1": "Wallet Account Number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "paycom", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 20, "CategoryName": "Mobile Money", "MediumImageId": "98844e93-a3b9-4999-a594-56d4cefd0ff4", "AmountType": 0}, {"Type": "", "Id": 404, "PayDirectProductId": 3422, "PayDirectInstitutionId": 0, "Name": "QikQik", "ShortName": "QikQik", "Narration": "QikQik Collections", "CustomerField1": "Wallet Account No", "LogoUrl": "q.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "qikqik", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 20, "CategoryName": "Mobile Money", "MediumImageId": "5321dd31-cf98-43a3-b3c0-4ae10a387501", "AmountType": 0}, {"Type": "", "Id": 316, "PayDirectProductId": 3704, "PayDirectInstitutionId": 0, "Name": "ReadyCash Mobile Money", "ShortName": "REC", "Narration": "Ready Cash Mobile Money Fund Wallet", "CustomerField1": "Wallet Account Number", "LogoUrl": "readycash.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "readycash", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 20, "CategoryName": "Mobile Money", "MediumImageId": "986d46bf-727a-47d1-987f-9026d717ae0b", "AmountType": 0}, {"Type": "", "Id": 911, "PayDirectProductId": 3595, "PayDirectInstitutionId": 0, "Name": "Stanbic IBTC MobileMoney", "ShortName": "StanbicMM", "Narration": "Stanbic Mobile Money - Fund your wallet", "CustomerField1": "Wallet Account No (234XXXXX)", "LogoUrl": "quiros.jpeg", "Surcharge": "100", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "quiros", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 20, "CategoryName": "Mobile Money", "AmountType": 0}, {"Type": "", "Id": 355, "PayDirectProductId": 4205, "PayDirectInstitutionId": 0, "Name": "Teasy Mobile ", "ShortName": "TSM", "Narration": "Teasy Mobile - Fund Wallet", "CustomerField1": "Wallet Number", "LogoUrl": "q.gif", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "teasymobile", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 20, "CategoryName": "Mobile Money", "AmountType": 0}]}, {"Id": 12, "Name": "Tax Payments", "Description": "Tax Payments", "Billers": [{"Type": "", "Id": 114, "PayDirectProductId": 3, "PayDirectInstitutionId": 0, "Name": "FIRS", "ShortName": "FIRS", "Narration": "Pay your taxes", "CustomerField1": "TIN", "LogoUrl": "firs.gif", "Surcharge": "666", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "firs", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 12, "CategoryName": "Tax Payments", "AmountType": 0}, {"Type": "", "Id": 17576, "PayDirectProductId": 4561, "PayDirectInstitutionId": 0, "Name": "TaxPro", "ShortName": "TaxPro", "Narration": "TaxPro", "CustomerField1": "Payment Code", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "taxpro", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 12, "CategoryName": "Tax Payments", "AmountType": 5}]}, {"Id": 27, "Name": "Religious Institutions", "Description": "Give for Religious cause", "Billers": [{"Type": "", "Id": 458, "PayDirectProductId": 3825, "PayDirectInstitutionId": 0, "Name": "Glory Impact Christian Centre", "ShortName": "GICC", "Narration": "Glory Impact Christian Centre", "CustomerField1": "Phone Number", "LogoUrl": "gicc.jpg", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "gicc", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 27, "CategoryName": "Religious Institutions", "AmountType": 0}, {"Type": "", "Id": 650, "PayDirectProductId": 3967, "PayDirectInstitutionId": 0, "Name": "Harvesters International Christian Centre", "ShortName": "HICC", "Narration": "Harvesters ICC", "CustomerField1": "Phone Number", "LogoUrl": "HICCnew", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "hicc", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Harvesters International Christian Centre", "CategoryId": 27, "CategoryName": "Religious Institutions", "MediumImageId": "10004f3c-61d4-4577-aa60-7515309cefe3", "AmountType": 0}, {"Type": "", "Id": 519, "PayDirectProductId": 3836, "PayDirectInstitutionId": 0, "Name": "HighLife Church", "ShortName": "HL", "Narration": "HighLife Church", "CustomerField1": "Membership Number", "LogoUrl": "highlife.JPEG", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "highlifechurch", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "HighLife Church", "PaymentOptionsTitle": "HighLife Church", "CategoryId": 27, "CategoryName": "Religious Institutions", "MediumImageId": "7f6cb249-7a25-4645-9579-78e7798755f5", "AmountType": 0}, {"Type": "", "Id": 310, "PayDirectProductId": 3599, "PayDirectInstitutionId": 0, "Name": "The Water Brook Church", "ShortName": "TWB", "CustomerField1": "Payer name", "LogoUrl": "thewaterbrook.jpg", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "thewaterbrook", "SupportEmail": "<EMAIL>", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 27, "CategoryName": "Religious Institutions", "MediumImageId": "e3984da9-3c58-4f5b-b698-5cd968b276b5", "AmountType": 5}, {"Type": "", "Id": 518, "PayDirectProductId": 3823, "PayDirectInstitutionId": 0, "Name": "Voice Of The Christian Martyrs Nigeria", "ShortName": "VOTCMN", "Narration": "Voice Of The Christian Martyrs Nigeria", "CustomerField1": "Location", "LogoUrl": "church", "Surcharge": "10000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "votcmn", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "PaymentOptionsPageHeader": "Voice Of The Christian Martyrs Nigeria", "PaymentOptionsTitle": "Voice Of The Christian Martyrs Nigeria", "CategoryId": 27, "CategoryName": "Religious Institutions", "MediumImageId": "37f70be2-dd67-4bfd-9b80-66756c996444", "AmountType": 0}]}, {"Id": 24, "Name": "Quickteller Business", "Description": "Revenue Collections", "Billers": [{"Type": "NCS", "Id": 17377, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Nigerian Custom Service", "ShortName": "NCS", "Narration": "NCS payment service", "CustomerField1": "customService", "LogoUrl": "q.gif", "Surcharge": "15000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "ncspayment", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 24, "CategoryName": "Quickteller Business", "AmountType": 5}, {"Type": "", "Id": 809, "PayDirectProductId": 4287, "PayDirectInstitutionId": 0, "Name": "Soup Kitchen", "ShortName": "890765", "Narration": "Payments to Soup Kitchen", "CustomerField1": "Customer reference", "LogoUrl": "Soup Kitchen", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "soup-kitchen", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16200", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 24, "CategoryName": "Quickteller Business", "AmountType": 0}]}, {"Id": 25, "Name": "Transfer Money", "Description": "Transfers money to various destinations", "Billers": [{"Type": "QF", "Id": 700, "PayDirectProductId": 149, "PayDirectInstitutionId": 0, "Name": "QT FundsTransfer", "ShortName": "QTFT", "Narration": "Tranfer Funds", "CustomerField1": "Phone number", "LogoUrl": "q.gif", "Surcharge": "0", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "fundstransfer", "PageFlowInfo": {"Elements": [], "FinishButtonName": "Finish", "StartPage": "DoPayment.aspx", "UsesPaymentItems": true, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 25, "CategoryName": "Transfer Money", "AmountType": 0}, {"Type": "QTLF", "Id": 17577, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Transfer Link", "ShortName": "QTFTLWB", "Narration": "Transfer Link ", "CustomerField1": "E-mail", "LogoUrl": "quickteller_logo.gif", "Surcharge": "5250", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "transferlink", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [{"Title": "Bank", "IsRequired": true, "SortOrder": 0, "ElementType": "SelectList", "DataType": "String", "SelectItemType": "Bank", "Length": 0, "SelectItems": [{"Name": " Mutual Trust Bank", "Value": "9270"}, {"Name": "ABOVE ONLY MICROFINANCE BANK LTD", "Value": "4329"}, {"Name": "Access Bank Nigeria Plcs", "Value": "31"}, {"Name": "Aso Savings Plc", "Value": "142"}, {"Name": "AutoPAY Test Bank", "Value": "9462"}, {"Name": "Bank of Baroda", "Value": "4237"}, {"Name": "CBA Kenya", "Value": "4230"}, {"Name": "Central Bank of The Gambia", "Value": "10501"}, {"Name": "CFC Stanbic Kenya", "Value": "4232"}, {"Name": "CREDIT BANK LIMITED", "Value": "4148"}, {"Name": "Diamond Bank Plc", "Value": "72"}, {"Name": "Dubai Islamic Bank", "Value": "4238"}, {"Name": "Ecobank Nigeria", "Value": "47"}, {"Name": "Ecobank Nigeria2", "Value": "89"}, {"Name": "Family Bank", "Value": "4227"}, {"Name": "Faulu Bank", "Value": "4234"}, {"Name": "Fidelity Bank Plc", "Value": "51"}, {"Name": "First Bank of Nigeria Plc", "Value": "8"}, {"Name": "First City Monument Bank", "Value": "76"}, {"Name": "GT Bank Kenya", "Value": "4130"}, {"Name": "Guaranty Trust Bank Plc", "Value": "10"}, {"Name": "IBTC Chartered Bank Plc", "Value": "17"}, {"Name": "Kenya Commercial Bank", "Value": "4233"}, {"Name": "Keystone Bank", "Value": "123"}, {"Name": "MONIEPOINT MICROFINANCE BANK", "Value": "10523"}, {"Name": "National Bank Kenya", "Value": "4231"}, {"Name": "NIC BANK KENYA", "Value": "4174"}, {"Name": "OPAY NIGERIA", "Value": "10519"}, {"Name": "QT Advice Bank", "Value": "146"}, {"Name": "Sidian Bank Kenya", "Value": "4235"}, {"Name": "Skye Bank Plc", "Value": "120"}, {"Name": "Spring Bank Plc", "Value": "125"}, {"Name": "Sterling Bank Plc", "Value": "121"}, {"Name": "Union Bank Nigeria Plc", "Value": "11"}, {"Name": "United Bank for Africa Plc", "Value": "7"}], "ElementName": "BankId"}, {"Title": "Account Number", "IsRequired": true, "SortOrder": 1, "ElementType": "TextBox", "DataType": "Numeric", "Length": 20, "SelectItems": [], "ElementName": "DestinationAccountNumber"}, {"Title": "Account Type", "IsRequired": true, "SortOrder": 2, "ElementType": "SelectList", "DataType": "String", "SelectItemType": "AccountType", "Length": 0, "SelectItems": [{"Name": "Current", "Value": "20"}, {"Name": "Savings", "Value": "10"}, {"Name": "Not Sure", "Value": "00"}], "ElementName": "DestinationAccountType"}, {"Title": "Amount", "IsRequired": true, "SortOrder": 3, "ElementType": "TextBox", "DataType": "Amount", "Length": 15, "SelectItems": [], "ElementName": "Amount"}, {"Title": "Receipient Name", "IsRequired": false, "SortOrder": 4, "ElementType": "TextBox", "DataType": "String", "Length": 40, "SelectItems": [], "ElementName": "ReciepientName"}, {"Title": "Receipient Email", "IsRequired": false, "SortOrder": 5, "ElementType": "TextBox", "DataType": "String", "Length": 40, "SelectItems": [], "ElementName": "ReciepientEmail"}, {"Title": "Remark (E.g Transfer to ABXC)", "IsRequired": false, "SortOrder": 7, "ElementType": "TextBox", "DataType": "String", "Length": 22, "SelectItems": [], "ElementName": "Remark"}, {"Title": "Receipient Phone No", "IsRequired": false, "SortOrder": 0, "ElementType": "TextBox", "DataType": "Numeric", "Length": 13, "SelectItems": [], "ElementName": "ReceipientPhoneNo"}, {"Title": "Receipient Phone No", "IsRequired": false, "SortOrder": 6, "ElementType": "TextBox", "DataType": "Numeric", "Length": 12, "SelectItems": [], "ElementName": "ReciepientPhone"}], "FinishButtonName": "Transfer", "StartPage": "Payment/PaymentDetails", "UsesPaymentItems": false, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 25, "CategoryName": "Transfer Money", "AmountType": 0}, {"Type": "QFTN", "Id": 701, "PayDirectProductId": 0, "PayDirectInstitutionId": 0, "Name": "Transfer to Account", "ShortName": "QTFTWB", "Narration": "You have account details", "CustomerField1": "E-Mail", "LogoUrl": "quickteller_logo.gif", "Surcharge": "5250", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "qtftweb", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16201", "PageFlowInfo": {"Elements": [{"Title": "Bank", "IsRequired": true, "SortOrder": 0, "ElementType": "SelectList", "DataType": "String", "SelectItemType": "Bank", "Length": 0, "SelectItems": [{"Name": " Mutual Trust Bank", "Value": "9270"}, {"Name": "ABOVE ONLY MICROFINANCE BANK LTD", "Value": "4329"}, {"Name": "Access Bank Nigeria Plcs", "Value": "31"}, {"Name": "Aso Savings Plc", "Value": "142"}, {"Name": "AutoPAY Test Bank", "Value": "9462"}, {"Name": "Bank of Baroda", "Value": "4237"}, {"Name": "CBA Kenya", "Value": "4230"}, {"Name": "Central Bank of The Gambia", "Value": "10501"}, {"Name": "CFC Stanbic Kenya", "Value": "4232"}, {"Name": "CREDIT BANK LIMITED", "Value": "4148"}, {"Name": "Diamond Bank Plc", "Value": "72"}, {"Name": "Dubai Islamic Bank", "Value": "4238"}, {"Name": "Ecobank Nigeria", "Value": "47"}, {"Name": "Ecobank Nigeria2", "Value": "89"}, {"Name": "Family Bank", "Value": "4227"}, {"Name": "Faulu Bank", "Value": "4234"}, {"Name": "Fidelity Bank Plc", "Value": "51"}, {"Name": "First Bank of Nigeria Plc", "Value": "8"}, {"Name": "First City Monument Bank", "Value": "76"}, {"Name": "GT Bank Kenya", "Value": "4130"}, {"Name": "Guaranty Trust Bank Plc", "Value": "10"}, {"Name": "IBTC Chartered Bank Plc", "Value": "17"}, {"Name": "Kenya Commercial Bank", "Value": "4233"}, {"Name": "Keystone Bank", "Value": "123"}, {"Name": "MONIEPOINT MICROFINANCE BANK", "Value": "10523"}, {"Name": "National Bank Kenya", "Value": "4231"}, {"Name": "NIC BANK KENYA", "Value": "4174"}, {"Name": "OPAY NIGERIA", "Value": "10519"}, {"Name": "QT Advice Bank", "Value": "146"}, {"Name": "Sidian Bank Kenya", "Value": "4235"}, {"Name": "Skye Bank Plc", "Value": "120"}, {"Name": "Spring Bank Plc", "Value": "125"}, {"Name": "Sterling Bank Plc", "Value": "121"}, {"Name": "Union Bank Nigeria Plc", "Value": "11"}, {"Name": "United Bank for Africa Plc", "Value": "7"}], "ElementName": "BankId"}, {"Title": "Account Number", "IsRequired": true, "SortOrder": 1, "ElementType": "TextBox", "DataType": "Numeric", "Length": 20, "SelectItems": [], "ElementName": "DestinationAccountNumber"}, {"Title": "Account Type", "IsRequired": true, "SortOrder": 2, "ElementType": "SelectList", "DataType": "String", "SelectItemType": "AccountType", "Length": 0, "SelectItems": [{"Name": "Current", "Value": "20"}, {"Name": "Savings", "Value": "10"}, {"Name": "Not Sure", "Value": "00"}], "ElementName": "DestinationAccountType"}, {"Title": "Amount", "IsRequired": true, "SortOrder": 3, "ElementType": "TextBox", "DataType": "Amount", "Length": 15, "SelectItems": [], "ElementName": "Amount"}, {"Title": "Remark (E.g Transfer to ABXC)", "IsRequired": false, "SortOrder": 7, "ElementType": "TextBox", "DataType": "String", "Length": 22, "SelectItems": [], "ElementName": "Remark"}, {"Title": "Receipient Email", "IsRequired": false, "SortOrder": 5, "ElementType": "TextBox", "DataType": "Email", "Length": 40, "SelectItems": [], "ElementName": "ReciepientEmail"}, {"Title": "Receipient Name", "IsRequired": false, "SortOrder": 4, "ElementType": "TextBox", "DataType": "String", "Length": 40, "SelectItems": [], "ElementName": "ReciepientName"}, {"Title": "Receipient Phone No", "IsRequired": false, "SortOrder": 6, "ElementType": "TextBox", "DataType": "Numeric", "Length": 12, "SelectItems": [], "ElementName": "ReciepientPhone"}], "FinishButtonName": "Transfer", "StartPage": "Payment/PaymentDetails", "UsesPaymentItems": false, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 25, "CategoryName": "Transfer Money", "SmallImageId": "42aab730-5bf5-4847-8b39-2c47f4b3503b", "LargeImageId": "42aab730-5bf5-4847-8b39-2c47f4b3503b", "MediumImageId": "42aab730-5bf5-4847-8b39-2c47f4b3503b", "AmountType": 0}, {"Type": "QMFT", "Id": 890, "PayDirectProductId": 3976, "PayDirectInstitutionId": 0, "Name": "Transfer to Phone Test", "ShortName": "QMT", "Narration": "You only know your friend’s GSM no", "CustomerField1": "E-mail", "LogoUrl": "q.gif", "Surcharge": "20000", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "transfertomobile", "SupportEmail": "<EMAIL>", "RiskCategoryId": "16204", "PageFlowInfo": {"Elements": [{"Title": "Beneficiary Phone Number", "IsRequired": true, "SortOrder": 0, "ElementType": "TextBox", "DataType": "Phone", "Length": 11, "SelectItems": [], "ElementName": "ReciepientPhone"}, {"Title": "Amount", "IsRequired": true, "SortOrder": 2, "ElementType": "TextBox", "DataType": "Amount", "Length": 12, "SelectItems": [], "ElementName": "Amount"}, {"Title": "4-digit transfer code", "IsRequired": true, "SortOrder": 3, "ElementType": "TextBox", "DataType": "Password", "Length": 4, "SelectItems": [], "ElementName": "TransferPin"}, {"Title": "Beneficiary <PERSON><PERSON>", "IsRequired": false, "SortOrder": 1, "ElementType": "TextBox", "DataType": "String", "Length": 70, "SelectItems": [], "ElementName": "ReciepientEmail"}], "FinishButtonName": "Transfer", "StartPage": "Payment/PaymentDetails", "UsesPaymentItems": false, "PerformInquiry": true, "AllowRetry": true}, "CategoryId": 25, "CategoryName": "Transfer Money", "SmallImageId": "42aab730-5bf5-4847-8b39-2c47f4b3503b", "AmountType": 0}]}, {"Id": 26, "Name": "Receive Money", "Description": "Receives money from various sources", "Billers": [{"Type": "REM", "Id": 502, "PayDirectProductId": 3490, "PayDirectInstitutionId": 0, "Name": "Western Union - Receive Money", "ShortName": "WU", "Narration": "Western Union - Receive money", "CustomerField1": "E-Mail", "LogoUrl": "wu.png", "Surcharge": "11122", "CurrencyCode": "566", "CurrencySymbol": "NGN", "QuickTellerSiteUrlName": "westernunion", "PageFlowInfo": {"Elements": [{"Title": "MTCN", "IsRequired": true, "SortOrder": 0, "ElementType": "TextBox", "DataType": "Numeric", "Length": 10, "SelectItems": [], "ElementName": "CustReference"}, {"Title": "Sender's Country", "IsRequired": true, "SortOrder": 1, "ElementType": "SelectList", "DataType": "String", "SelectItemType": "Country", "Length": 0, "SelectItems": [{"Name": "AFGHANISTAN", "Value": "AF"}, {"Name": "ALAND ISLANDS", "Value": "AX"}, {"Name": "ALBANIA", "Value": "AL"}, {"Name": "ALGERIA", "Value": "DZ"}, {"Name": "AMERICAN SAMOA", "Value": "AS"}, {"Name": "ANDORRA", "Value": "AD"}, {"Name": "ANGOLA", "Value": "AO"}, {"Name": "ANGUILLA", "Value": "AI"}, {"Name": "ANTARCTICA", "Value": "AQ"}, {"Name": "ANTIGUA AND BARBUDA", "Value": "AG"}, {"Name": "ARGENTINA", "Value": "AR"}, {"Name": "ARMENIA", "Value": "AM"}, {"Name": "ARUBA", "Value": "AW"}, {"Name": "AUSTRALIA", "Value": "AU"}, {"Name": "AUSTRIA", "Value": "AT"}, {"Name": "AZERBAIJAN", "Value": "AZ"}, {"Name": "BAHAMAS", "Value": "BS"}, {"Name": "BAHRAIN", "Value": "BH"}, {"Name": "BANGLADESH", "Value": "BD"}, {"Name": "BARBADOS", "Value": "BB"}, {"Name": "BELARUS", "Value": "BY"}, {"Name": "BELGIUM", "Value": "BE"}, {"Name": "BELIZE", "Value": "BZ"}, {"Name": "BENIN", "Value": "BJ"}, {"Name": "BERMUDA", "Value": "BM"}, {"Name": "BHUTAN", "Value": "BT"}, {"Name": "BOLIVIA", "Value": "BO"}, {"Name": "BOSNIA AND HERZEGOVINA", "Value": "BA"}, {"Name": "BOTSWANA", "Value": "BW"}, {"Name": "BOUVET ISLAND", "Value": "BV"}, {"Name": "BRAZIL", "Value": "BR"}, {"Name": "BRITISH INDIAN OCEAN TERRITORY", "Value": "IO"}, {"Name": "BRUNEI DARUSSALAM", "Value": "BN"}, {"Name": "BULGARIA", "Value": "BG"}, {"Name": "BURKINA FASO", "Value": "BF"}, {"Name": "BURUNDI", "Value": "BI"}, {"Name": "CAMBODIA", "Value": "KH"}, {"Name": "CAMEROON", "Value": "CM"}, {"Name": "CANADA", "Value": "CA"}, {"Name": "CAPE VERDE", "Value": "CV"}, {"Name": "CAYMAN ISLANDS", "Value": "KY"}, {"Name": "CENTRAL AFRICAN REPUBLIC", "Value": "CF"}, {"Name": "CHAD", "Value": "TD"}, {"Name": "CHILE", "Value": "CL"}, {"Name": "CHINA", "Value": "CN"}, {"Name": "CHRISTMAS ISLAND", "Value": "CX"}, {"Name": "COCOS (KEELING) ISLANDS", "Value": "CC"}, {"Name": "COLOMBIA", "Value": "CO"}, {"Name": "COMOROS", "Value": "KM"}, {"Name": "CONGO", "Value": "CG"}, {"Name": "CONGO - THE DEMOCRATIC REPUBLIC OF THE", "Value": "CD"}, {"Name": "COOK ISLANDS", "Value": "CK"}, {"Name": "COSTA RICA", "Value": "CR"}, {"Name": "CÔTE D'IVOIRE", "Value": "CI"}, {"Name": "CROATIA", "Value": "HR"}, {"Name": "CUBA", "Value": "CU"}, {"Name": "CYPRUS", "Value": "CY"}, {"Name": "CZECH REPUBLIC", "Value": "CZ"}, {"Name": "DENMARK", "Value": "DK"}, {"Name": "DJIBOUTI", "Value": "DJ"}, {"Name": "DOMINICA", "Value": "DM"}, {"Name": "DOMINICAN REPUBLIC", "Value": "DO"}, {"Name": "ECUADOR", "Value": "EC"}, {"Name": "EGYPT", "Value": "EG"}, {"Name": "EL SALVADOR", "Value": "SV"}, {"Name": "EQUATORIAL GUINEA", "Value": "GQ"}, {"Name": "ERITREA", "Value": "ER"}, {"Name": "ESTONIA", "Value": "EE"}, {"Name": "ETHIOPIA", "Value": "ET"}, {"Name": "FALKLAND ISLANDS (MALVINAS)", "Value": "FK"}, {"Name": "FAROE ISLANDS", "Value": "FO"}, {"Name": "FIJI", "Value": "FJ"}, {"Name": "FINLAND", "Value": "FI"}, {"Name": "FRANCE", "Value": "FR"}, {"Name": "FRENCH GUIANA", "Value": "GF"}, {"Name": "FRENCH POLYNESIA", "Value": "PF"}, {"Name": "FRENCH SOUTHERN TERRITORIES", "Value": "TF"}, {"Name": "GABON", "Value": "GA"}, {"Name": "GAMBIA", "Value": "GM"}, {"Name": "GEORGIA", "Value": "GE"}, {"Name": "GERMANY", "Value": "DE"}, {"Name": "GHANA", "Value": "GH"}, {"Name": "GIBRALTAR", "Value": "GI"}, {"Name": "GREECE", "Value": "GR"}, {"Name": "GREENLAND", "Value": "GL"}, {"Name": "GRENADA", "Value": "GD"}, {"Name": "GUADELOUPE", "Value": "GP"}, {"Name": "GUAM", "Value": "GU"}, {"Name": "GUATEMALA", "Value": "GT"}, {"Name": "GUERNSEY", "Value": "GG"}, {"Name": "GUINEA", "Value": "GN"}, {"Name": "GUINEA-BISSAU", "Value": "GW"}, {"Name": "GUYANA", "Value": "GY"}, {"Name": "HAITI", "Value": "HT"}, {"Name": "HEARD ISLAND AND MCDONALD ISLANDS", "Value": "HM"}, {"Name": "HONDURAS", "Value": "HN"}, {"Name": "HONG KONG", "Value": "HK"}, {"Name": "HUNGARY", "Value": "HU"}, {"Name": "ICELAND", "Value": "IS"}, {"Name": "INDIA", "Value": "IN"}, {"Name": "INDONESIA", "Value": "ID"}, {"Name": "IRAN - ISLAMIC REPUBLIC OF", "Value": "IR"}, {"Name": "IRAQ", "Value": "IQ"}, {"Name": "IRELAND", "Value": "IE"}, {"Name": "ISLE OF MAN", "Value": "IM"}, {"Name": "ISRAEL", "Value": "IL"}, {"Name": "ITALY", "Value": "IT"}, {"Name": "JAMAICA", "Value": "JM"}, {"Name": "JAPAN", "Value": "JP"}, {"Name": "JERSEY", "Value": "JE"}, {"Name": "JORDAN", "Value": "JO"}, {"Name": "KAZAKHSTAN", "Value": "KZ"}, {"Name": "KENYA", "Value": "KE"}, {"Name": "KIRIBATI", "Value": "KI"}, {"Name": "KOREA - DEMOCRAT<PERSON> PEOPLE'S REPUBLIC OF", "Value": "KP"}, {"Name": "KOREA - REPUBLIC OF", "Value": "KR"}, {"Name": "KUWAIT", "Value": "KW"}, {"Name": "KYRGYZSTAN", "Value": "KG"}, {"Name": "LAO PEOPLE'S DEMOCRATIC REPUBLIC", "Value": "LA"}, {"Name": "LATVIA", "Value": "LV"}, {"Name": "LEBANON", "Value": "LB"}, {"Name": "LESOTHO", "Value": "LS"}, {"Name": "LIBERIA", "Value": "LR"}, {"Name": "LIBYAN ARAB JAMAHIRIYA", "Value": "LY"}, {"Name": "LIECHTENSTEIN", "Value": "LI"}, {"Name": "LITHUANIA", "Value": "LT"}, {"Name": "LUXEMBOURG", "Value": "LU"}, {"Name": "MACAO", "Value": "MO"}, {"Name": "MACEDONIA - THE FORMER YUGOSLAV REPUBLIC OF", "Value": "MK"}, {"Name": "MADAGASCAR", "Value": "MG"}, {"Name": "MALAWI", "Value": "MW"}, {"Name": "MALAYSIA", "Value": "MY"}, {"Name": "MALDIVES", "Value": "MV"}, {"Name": "MALI", "Value": "ML"}, {"Name": "MALTA", "Value": "MT"}, {"Name": "MARSHALL ISLANDS", "Value": "MH"}, {"Name": "MARTINIQUE", "Value": "MQ"}, {"Name": "MAURITANIA", "Value": "MR"}, {"Name": "MAURITIUS", "Value": "MU"}, {"Name": "MAYOTTE", "Value": "YT"}, {"Name": "MEXICO", "Value": "MX"}, {"Name": "MICRONESI - FEDERATED STATES OF", "Value": "FM"}, {"Name": "MOLDOVA", "Value": "MD"}, {"Name": "MONACO", "Value": "MC"}, {"Name": "MONGOLIA", "Value": "MN"}, {"Name": "MONTENEGRO", "Value": "ME"}, {"Name": "MONTSERRAT", "Value": "MS"}, {"Name": "MOROCCO", "Value": "MA"}, {"Name": "MOZAMBIQUE", "Value": "MZ"}, {"Name": "MYANMAR", "Value": "MM"}, {"Name": "NAMIBIA", "Value": "NA"}, {"Name": "NAURU", "Value": "NR"}, {"Name": "NEPAL", "Value": "NP"}, {"Name": "NETHERLANDS", "Value": "NL"}, {"Name": "NETHERLANDS ANTILLES", "Value": "AN"}, {"Name": "NEW CALEDONIA", "Value": "NC"}, {"Name": "NEW ZEALAND", "Value": "NZ"}, {"Name": "NICARAGUA", "Value": "NI"}, {"Name": "NIGER", "Value": "NE"}, {"Name": "NIGERIA", "Value": "NG"}, {"Name": "NIUE", "Value": "NU"}, {"Name": "NORFOLK ISLAND", "Value": "NF"}, {"Name": "NORTHERN MARIANA ISLANDS", "Value": "MP"}, {"Name": "NORWAY", "Value": "NO"}, {"Name": "OMAN", "Value": "OM"}, {"Name": "PAKISTAN", "Value": "PK"}, {"Name": "PALAU", "Value": "PW"}, {"Name": "PALESTINIAN TERRITORY - OCCUPIED", "Value": "PS"}, {"Name": "PANAMA", "Value": "PA"}, {"Name": "PAPUA NEW GUINEA", "Value": "PG"}, {"Name": "PARAGUAY", "Value": "PY"}, {"Name": "PERU", "Value": "PE"}, {"Name": "PHILIPPINES", "Value": "PH"}, {"Name": "PITCAIRN", "Value": "PN"}, {"Name": "POLAND", "Value": "PL"}, {"Name": "PORTUGAL", "Value": "PT"}, {"Name": "PUERTO RICO", "Value": "PR"}, {"Name": "QATAR", "Value": "QA"}, {"Name": "RÉUNION", "Value": "RE"}, {"Name": "ROMANIA", "Value": "RO"}, {"Name": "RUSSIAN FEDERATION", "Value": "RU"}, {"Name": "RWANDA", "Value": "RW"}, {"Name": "SAINT BARTHÉLEMY", "Value": "BL"}, {"Name": "SAINT HELENA", "Value": "SH"}, {"Name": "SAINT KITTS AND NEVIS", "Value": "KN"}, {"Name": "SAINT LUCIA", "Value": "LC"}, {"Name": "SAINT MARTIN", "Value": "MF"}, {"Name": "SAINT PIERRE AND MIQUELON", "Value": "PM"}, {"Name": "SAINT VINCENT AND THE GRENADINES", "Value": "VC"}, {"Name": "SAMOA", "Value": "WS"}, {"Name": "SAN MARINO", "Value": "SM"}, {"Name": "SAO TOME AND PRINCIPE", "Value": "ST"}, {"Name": "SAUDI ARABIA", "Value": "SA"}, {"Name": "SENEGAL", "Value": "SN"}, {"Name": "SERBIA", "Value": "RS"}, {"Name": "SEYCHELLES", "Value": "SC"}, {"Name": "SIERRA LEONE", "Value": "SL"}, {"Name": "SINGAPORE", "Value": "SG"}, {"Name": "SLOVAKIA", "Value": "SK"}, {"Name": "SLOVENIA", "Value": "SI"}, {"Name": "SOLOMON ISLANDS", "Value": "SB"}, {"Name": "SOMALIA", "Value": "SO"}, {"Name": "SOUTH AFRICA", "Value": "ZA"}, {"Name": "SOUTH GEORGIA AND THE SOUTH SANDWICH ISLANDS", "Value": "GS"}, {"Name": "SPAIN", "Value": "ES"}, {"Name": "SRI LANKA", "Value": "LK"}, {"Name": "SUDAN", "Value": "SD"}, {"Name": "SURINAME", "Value": "SR"}, {"Name": "SVALBARD AND JAN MAYEN", "Value": "SJ"}, {"Name": "SWAZILAND", "Value": "SZ"}, {"Name": "SWEDEN", "Value": "SE"}, {"Name": "SWITZERLAND", "Value": "CH"}, {"Name": "SYRIAN ARAB REPUBLIC", "Value": "SY"}, {"Name": "TAIWAN -  PROVINCE OF CHINA", "Value": "TW"}, {"Name": "TAJIKISTAN", "Value": "TJ"}, {"Name": "TANZANIA -  UNITED REPUBLIC OF", "Value": "TZ"}, {"Name": "THAILAND", "Value": "TH"}, {"Name": "TIMOR-LESTE", "Value": "TL"}, {"Name": "TOGO", "Value": "TG"}, {"Name": "TOKELAU", "Value": "TK"}, {"Name": "TONGA", "Value": "TO"}, {"Name": "TRINIDAD AND TOBAGO", "Value": "TT"}, {"Name": "TUNISIA", "Value": "TN"}, {"Name": "TURKEY", "Value": "TR"}, {"Name": "TURKMENISTAN", "Value": "TM"}, {"Name": "TURKS AND CAICOS ISLANDS", "Value": "TC"}, {"Name": "TUVALU", "Value": "TV"}, {"Name": "UGANDA", "Value": "UG"}, {"Name": "UKRAINE", "Value": "UA"}, {"Name": "UNITED ARAB EMIRATES", "Value": "AE"}, {"Name": "UNITED KINGDOM", "Value": "GB"}, {"Name": "UNITED STATES", "Value": "US"}, {"Name": "UNITED STATES MINOR OUTLYING ISLANDS", "Value": "UM"}, {"Name": "URUGUAY", "Value": "UY"}, {"Name": "UZBEKISTAN", "Value": "UZ"}, {"Name": "VANUATU", "Value": "VU"}, {"Name": "VATICAN CITY STATE", "Value": "VA"}, {"Name": "VENEZUELA", "Value": "VE"}, {"Name": "VIET NAM", "Value": "VN"}, {"Name": "VIRGIN ISLANDS - BRITISH", "Value": "VG"}, {"Name": "VIRGIN ISLANDS - U.S.", "Value": "VI"}, {"Name": "WALLIS AND FUTUNA", "Value": "WF"}, {"Name": "WESTERN SAHARA", "Value": "EH"}, {"Name": "YEMEN", "Value": "YE"}, {"Name": "ZAMBIA", "Value": "ZM"}, {"Name": "ZIMBABWE", "Value": "ZW"}], "ElementName": "PayerCountryCode"}, {"Title": "Receiving Account Type", "IsRequired": true, "SortOrder": 3, "ElementType": "SelectList", "DataType": "String", "SelectItemType": "AccountType", "Length": 0, "SelectItems": [{"Name": "Current", "Value": "20"}, {"Name": "Savings", "Value": "10"}, {"Name": "Not Sure", "Value": "00"}], "ElementName": "PayeeAccountType"}, {"Title": "Expected Amount", "IsRequired": true, "SortOrder": 4, "ElementType": "TextBox", "DataType": "Amount", "Length": 12, "SelectItems": [], "ElementName": "Amount"}, {"Title": "Receiving Currency", "IsRequired": true, "SortOrder": 5, "ElementType": "SelectList", "DataType": "String", "SelectItemType": "<PERSON><PERSON><PERSON><PERSON>", "Length": 0, "SelectItems": [{"Name": "<PERSON><PERSON>", "Value": "566"}, {"Name": "US Dollars", "Value": "840"}], "ElementName": "ExpectedCurrency"}, {"Title": "Receiving Account Number (NUBAN Only)", "IsRequired": true, "SortOrder": 2, "ElementType": "TextBox", "DataType": "Numeric", "Length": 20, "SelectItems": [], "ElementName": "PayeeAccountNumber"}, {"Title": "Firstname (Receiving Account)", "IsRequired": true, "SortOrder": 6, "ElementType": "TextBox", "DataType": "String", "Length": 40, "SelectItems": [], "ElementName": "PayeeFirstname"}, {"Title": "Surname (Receiving Account)", "IsRequired": true, "SortOrder": 7, "ElementType": "TextBox", "DataType": "String", "Length": 40, "SelectItems": [], "ElementName": "PayeeSurname"}, {"Title": "Mobile Number", "IsRequired": true, "SortOrder": 12, "ElementType": "TextBox", "DataType": "Numeric", "Length": 15, "SelectItems": [], "ElementName": "ReceiverPhoneNumber"}, {"Title": "Answer to Test Question", "IsRequired": false, "SortOrder": 13, "ElementType": "TextBox", "DataType": "String", "Length": 40, "SelectItems": [], "ElementName": "TestAnswer"}], "FinishButtonName": "Receive Money", "StartPage": "Payment/PaymentDetails", "UsesPaymentItems": true, "PerformInquiry": false, "AllowRetry": false}, "PaymentOptionsPageHeader": "Western Union - Receive Money - Pick your bank", "PaymentOptionsTitle": "Banks offering to receive Western Union money transfers on bank accounts", "CategoryId": 26, "CategoryName": "Receive Money", "SmallImageId": "010995fa-b286-4d9f-b9d0-74a2cd2576d3", "LargeImageId": "e9447628-6094-4018-a285-1ed5b9b4d350", "AmountType": 0}]}]}, "ResponseCode": "90000", "ResponseCodeGrouping": "SUCCESSFUL"}