{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"src/*": ["./src/*"], "@app/baxi": ["libs/baxi/src"], "@app/baxi/*": ["libs/baxi/src/*"], "@app/rabbitmq": ["libs/rabbitmq/src"], "@app/rabbitmq/*": ["libs/rabbitmq/src/*"], "@app/flutterwave": ["libs/flutterwave/src"], "@app/flutterwave/*": ["libs/flutterwave/src/*"], "@app/quickteller": ["libs/quickteller/src"], "@app/quickteller/*": ["libs/quickteller/src/*"], "@app/credit-switch": ["libs/credit-switch/src"], "@app/credit-switch/*": ["libs/credit-switch/src/*"]}}}