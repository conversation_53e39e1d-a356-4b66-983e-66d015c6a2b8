import { Module } from '@nestjs/common';
import { FlutterwaveService } from './flutterwave.service';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
   imports: [
      HttpModule.register({
        timeout: 100000,
        maxRedirects: 5,
        baseURL: config.flutterwave.baseUrl,
        headers: {
          Authorization: config.flutterwave.apiKey,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      }),
    ],
  providers: [FlutterwaveService],
  exports: [FlutterwaveService],
})
export class FlutterwaveModule {}
