import { BillerCategory } from "@app/baxi/baxi.interface";

export const FLutterwaveBillerCategory: Record<BillerCategory|string, string> = {
  cabletv: 'CABLEBILLS',
  airtime: 'AIRTIME',
  databundle: 'MOBILEDATA',
  //   | 'epin'
  //   | 'payment'
  //   | 'education'
  //   | 'vehicle'
  //   | 'transfer'
  //   | 'insurance'
  //   | 'gaming'
  //   | 'betting'
  //   | 'mobile-money'
  //   | 'collections'
  electricity: 'UTILITYBILLS',
  tax: 'TAX',
  school: 'SCHPB',
  religious: 'RELINST',
  transport: 'TRANSLOG',
  donation: 'DONATIONS',
  others: 'OTHERS',
  dealers: 'DEALPAY',
  internet: 'INTSERVICE',
 
  //   | 'gov-payment';
};

export interface FlutterwaveBiller {
  id: number;
  name: string;
  logo: string;
  description: string;
  short_name: string;
  biller_code: string;
  country_code: string;
}

export interface FlutterwaveBillerPricing {
  id: number;
  biller_code: string;
  name: string;
  default_commission: number;
  date_added: string;
  country: string;
  is_airtime: boolean;
  biller_name: string;
  item_code: string;
  short_name: string;
  fee: number;
  commission_on_fee: boolean;
  reg_expression: string;
  label_name: string;
  amount: number;
  is_resolvable: boolean;
  group_name: string;
  category_name: string;
  is_data: boolean;
  default_commission_on_amount: number;
  commission_on_fee_or_amount: number;
  validity_period: string;
}
