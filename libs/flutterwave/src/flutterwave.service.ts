import { BillerCategory } from '@app/baxi/baxi.interface';
import { HttpService } from '@nestjs/axios';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import {
  FlutterwaveBiller,
  FLutterwaveBillerCategory,
  FlutterwaveBillerPricing,
} from './flutterwave.interface';
import { LookupDto } from './dto/lookup.dto';
import { RedisService } from '@crednet/utils';

@Injectable()
export class FlutterwaveService {
  constructor(
    private readonly httpService: HttpService,
    private readonly redisService: RedisService,
  ) {}

  async getBillers(serviceType: BillerCategory): Promise<FlutterwaveBiller[]> {
    const { data } = await firstValueFrom(
      this.httpService
        .get(`v3/bills/${FLutterwaveBillerCategory[serviceType]}/billers`, {
          params: {
            country: 'NG',
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error?.response?.data);

            throw error.response?.data ?? error;
          }),
        ),
    );
    // console.log(serviceType, data);
    return data?.data;
  }

  async verifyTransaction(reference: string): Promise<any> {
    const { data } = await firstValueFrom(
      this.httpService
        .get(`v3/bills/${reference}?verbose=1`, {
          params: {
            country: 'NG',
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error?.response?.data);

            throw (
              error.response?.data || new UnprocessableEntityException(error)
            );
          }),
        ),
    );
    console.log(reference, data);
    return data;
  }

  async getBillerItems(
    billerCode: string,
  ): Promise<FlutterwaveBillerPricing[]> {
    console.log(billerCode);
    const { data } = await firstValueFrom(
      this.httpService
        .get(`v3/billers/${billerCode}/items`, {
          params: {
            country: 'NG',
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error.response?.data);

            throw error.response?.data ?? error;
          }),
        ),
    );
    // console.log(billerCode, data);
    return data?.data;
  }

  async purchaseBill(payload): Promise<any> {
    const { data } = await firstValueFrom(
      this.httpService
        .post(
          `v3/billers/${payload.billerCode}/items/${payload.itemCode}/payment`,
          {
            country: 'NG',
            customer_id: payload.customerId,
            amount: payload.amount,
            reference: payload.reference,
          },
        )
        .pipe(
          catchError((error) => {
            console.log(error.response?.data);

            throw error.response?.data ?? error;
          }),
        ),
    );
    // console.log(data);
    return data?.data;
  }

  async lookup(
    itemCode: string,
    billerCode: string,
    customerId: string,
  ): Promise<LookupDto> {
    const fromCache = await this.redisService.get(
      `lookup-flutterwave-${itemCode}-${billerCode}-${customerId}`,
    );
    const cacheData = JSON.parse(fromCache);
    if (cacheData) {
      return cacheData;
    }
    const { data } = await firstValueFrom(
      this.httpService
        .get(`v3/bill-items/${itemCode}/validate`, {
          params: {
            code: billerCode,
            customer: customerId,
          },
        })
        .pipe(
          catchError((error) => {
            console.log(error.response?.data);

            throw error.response?.data ?? error;
          }),
        ),
    );
    console.log(data);
    await this.redisService.set(
      `lookup-flutterwave-${itemCode}-${billerCode}-${customerId}`,
      JSON.stringify(data?.data),
      { PX: 60 * 1000 },
    );
    return data?.data;
  }
}
