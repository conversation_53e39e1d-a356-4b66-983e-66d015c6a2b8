import { HttpService } from '@nestjs/axios';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { AxiosError } from 'axios';
import { firstValueFrom } from 'rxjs';
import { catchError } from 'rxjs/operators';
import crypto from 'node:crypto';
import config from 'src/config';
import { RedisService } from '@crednet/utils';

@Injectable()
export class QuickTellerService {
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly authURL: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly cacheService: RedisService,
  ) {
    this.clientId = config.quickteller.clientId;
    this.clientSecret = config.quickteller.clientSecret;
    this.authURL = config.quickteller.authUrl;
    // setTimeout(() => {
    //   this.getAccessToken();
    // }, 5000);
  }

  async getAccessToken(): Promise<string> {
    const cacheKey = 'quickteller-auth-token';
    const cachedToken = await this.cacheService.get(cacheKey);
    // if (cachedToken) return cachedToken;

    const authHeader = Buffer.from(
      `${this.clientId}:${this.clientSecret}`,
    ).toString('base64');
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Basic ${authHeader}`,
    };

    const body = 'grant_type=client_credentials&scope=profile';

    try {
      const { data } = await firstValueFrom(
        this.httpService.post(this.authURL, body, { headers }).pipe(
          catchError((error: AxiosError) => {
            console.error('Token acquisition failed', error.response?.data);
            throw new UnprocessableEntityException(
              'Failed to authenticate with Interswitch',
            );
          }),
        ),
      );

      console.log(data);

      // Cache token with 5-minute buffer before expiration
      await this.cacheService.set(cacheKey, data.access_token, {
        PX: (data.expires_in - 300) * 1000,
      });
      return data.access_token;
    } catch (error) {
      console.error('Failed to acquire access token', error.stack);
      throw new UnprocessableEntityException(
        'Authentication service unavailable',
      );
    }
  }

  private async getAuthHeaders() {
    const token = await this.getAccessToken();
    return {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
      terminalId: config.quickteller.terminalId,
    };
  }

  generateSignature(
    method: string,
    endpoint: string,
    timestamp: string,
  ): string {
    const data = `${method}&${endpoint}&${timestamp}`;
    const hmac = crypto.createHmac('sha256', this.clientSecret);
    return hmac.update(data).digest('base64');
  }

  async getBillerCategories(): Promise<any[]> {
    const cacheKey = 'quickteller-categories';
    const cached = await this.cacheService.get(cacheKey);
    if (cached) return JSON.parse(cached);

    const endpoint = '/api/v5/services/categories';
    const headers = await this.getAuthHeaders();

    const { data } = await firstValueFrom(
      this.httpService.get(`${endpoint}`, { headers }).pipe(
        catchError((error: AxiosError) => {
          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );

    // await this.cacheService.set(cacheKey, data.BillerCategories, {PX:3600 * 1000});
    return data.BillerCategories;
  }

  async getBillers(categoryId: string): Promise<any[]> {
    const endpoint = `/api/v5/services?categoryId=${categoryId}`;
    const headers = await this.getAuthHeaders();

    const { data } = await firstValueFrom(
      this.httpService.get(`${endpoint}`, { headers }).pipe(
        catchError((error: AxiosError) => {
          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );

    return data.BillerList.Category[0].Billers;
  }

  async getBillerItems(billerCode: string): Promise<any[]> {
    const endpoint = `/api/v5/services/options?serviceid=${billerCode}`;
    const headers = await this.getAuthHeaders();

    const { data } = await firstValueFrom(
      this.httpService.get(`${endpoint}`, { headers }).pipe(
        catchError((error: AxiosError) => {
          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );

    return data.PaymentItems;
  }

  async lookup(billerCode: string, customerId: string): Promise<any> {
    const fromCache = await this.cacheService.get(
      `lookup-quickteller-${billerCode}-${customerId}`,
    );
    if (fromCache) {
      return fromCache;
    }

    const endpoint = '/api/v5/Transactions/validatecustomers';
    const headers = await this.getAuthHeaders();

    const { data } = await firstValueFrom(
      this.httpService
        .post(
          `${endpoint}`,
          {
            customers: [{ CustomerId: customerId, PaymentCode: billerCode }],
            TerminalId: config.quickteller.terminalId,
          },
          { headers },
        )
        .pipe(
          catchError((error: AxiosError) => {
            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );

    await this.cacheService.set(
      `lookup-quickteller-${billerCode}-${customerId}`,
      data?.Customers[0],
      {
        PX: 60 * 1000 * 5,
      },
    );

    return data.Customers[0];
  }

  async purchaseAirtime(request: any): Promise<any> {
    const endpoint = '/api/v5/Transactions';
    const headers = await this.getAuthHeaders();

    const { data } = await firstValueFrom(
      this.httpService
        .post(
          `${endpoint}`,
          {
            PaymentCode: request.itemCode,
            CustomerId: request.phoneNumber,
            Amount: +request.amount * 100 + '',
            requestReference: request.reference,
            CustomerEmail: request.email,
            CustomerMobile: request.phoneNumber,
          },
          { headers },
        )
        .pipe(
          catchError((error: AxiosError) => {
            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );

    return data;
  }

  async purchaseBill(request: any): Promise<any> {
    const endpoint = '/api/v5/Transactions';
    const headers = await this.getAuthHeaders();

    const { data } = await firstValueFrom(
      this.httpService
        .post(
          `${endpoint}`,
          {
            PaymentCode: request.itemCode,
            CustomerId: request.customerId,
            Amount: +request.amount + '',
            requestReference: request.reference,
            CustomerEmail: request.email,
            CustomerMobile: request.phoneNumber,
          },
          { headers },
        )
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error);
            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );

    return data;
  }

  async requeryTransaction(reference: string): Promise<any> {
    const endpoint = `/api/v5/Transactions?requestRef=${reference}`;
    const headers = await this.getAuthHeaders();

    const { data } = await firstValueFrom(
      this.httpService.get(`${endpoint}`, { headers }).pipe(
        catchError((error: AxiosError) => {
          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );

    return data;
  }
}
