import { BillerCategory } from '@app/baxi/baxi.interface';

export interface BillersInterface {
  id: number;
  serviceId: string;
  serviceCode: string;
  serviceCategory: string;
  billerId?: number;
  serviceBiller?: string;
  serviceType: string;
  serviceName?: string;
  serviceDescription?: string;
  serviceHandler?: string;
  serviceProvider?: string;
  serviceEnabled?: string;
  serviceStatus?: string;
  serviceLogo?: string;
  deployed?: string;
  b2bDeployed?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface DataBundleInterface {
  name: string;
  price: number;
  datacode: string;
  validity: string;
  allowance: string;
}

export interface CablePriceInterface {
  _id: string;
  monthsPaidFor: number;
  price: number;
  invoicePeriod: number;
}

export interface CableBundleInterface {
  code: string;
  name: string;
  availablePricingOptions: CablePriceInterface[];
}

export interface AirtimePurchaseRequest {
  billerCode: string;
  email: string;
  phoneNumber: string;
  amount: string;
  reference: string;
}

export interface BillPaymentRequest extends AirtimePurchaseRequest {
  customerId: string;
}

export const QuickTellerBillerCategory: Record<
  BillerCategory | string,
  string
> = {
  cabletv: 'Cable TV Bills',
  airtime: 'Mobile/Recharge',
  databundle: 'Mobile/Recharge',
  electricity: 'Utility Bills',
  tax: 'Tax Payments',
  school: 'PayChoice',
  religious: 'Religious Institutions',
  transport: 'Transport',
  donation: 'Donations',
  others: 'Others',
  internet: 'Subscriptions',

  //   | 'gov-payment';
};

export const categories: BillerCategory[] = [
  'cabletv',
  'airtime',
  'databundle',
  'internet',
  'epin',
  'payment',
  'education',
  'vehicle',
  'transfer',
  'insurance',
  'gaming',
  'betting',
  'mobile-money',
  'collections',
  'electricity',
  'gov-payment',
  'tax',
  'school',
  'religious',
  'transport',
  'donation',
  'others',
  'dealers',
];
