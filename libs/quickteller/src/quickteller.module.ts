import { Module } from '@nestjs/common';
import { QuickTellerService } from './quickteller.service';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: config.quickteller.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    }),
  ],
  providers: [QuickTellerService],
  exports: [QuickTellerService],
})
export class QuickTellerModule {}
