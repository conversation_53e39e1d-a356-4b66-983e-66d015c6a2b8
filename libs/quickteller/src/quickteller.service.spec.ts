import { Test, TestingModule } from '@nestjs/testing';
import { QuickTellerService } from './quickteller.service';

describe('BaxiService', () => {
  let service: QuickTellerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [QuickTellerService],
    }).compile();

    service = module.get<QuickTellerService>(QuickTellerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
