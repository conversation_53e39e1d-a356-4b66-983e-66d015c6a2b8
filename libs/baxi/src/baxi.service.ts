import { HttpService } from '@nestjs/axios';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { AxiosError } from 'axios';
import { catchError, firstValueFrom } from 'rxjs';
import {
  <PERSON>erCategory,
  BillersInterface,
  CableBundleInterface,
  DataBundleInterface,
} from './baxi.interface';

@Injectable()
export class BaxiService {
  constructor(private readonly httpService: HttpService) {}

  async getBillers(serviceType: BillerCategory): Promise<BillersInterface[]> {
    const { data } = await firstValueFrom(
      this.httpService
        .post('services/billers/services/category', {
          service_type: serviceType,
        })
        .pipe(
          catchError((error) => {
            console.log(error);

            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );
    // console.log(serviceType, data);
    return data?.data;
  }

  async getDataBundles(serviceType: string): Promise<DataBundleInterface[]> {
    console.log(serviceType);
    const { data } = await firstValueFrom(
      this.httpService
        .post('services/databundle/bundles', {
          service_type: serviceType,
        })
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error?.response?.data);

            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );
    // console.log(data);
    return data?.data;
  }

  async getCableBundles(serviceType: string): Promise<CableBundleInterface[]> {
    const { data } = await firstValueFrom(
      this.httpService
        .post(
          serviceType == 'dstv'
            ? 'services/multichoice/addons'
            : 'services/multichoice/list',
          {
            service_type: serviceType,
          },
        )
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error?.response?.data);

            throw  error.response?.data ?? error;
          }),
        ),
    );
    // console.log(data);
    return data?.data;
  }

  async purchase(url: string, request: string) {
    const { data } = await firstValueFrom(
      this.httpService.post(url, request).pipe(
        catchError(async (error: AxiosError) => {
          console.error(error.response?.data);
          console.error(error.response?.status);
           

          return { data: error.response?.data };
        }),
      ),
    );
    return data;
  }

  async lookup(serviceType: string, accountNumber: string): Promise<any> {
    const { data } = await firstValueFrom(
      this.httpService
        .post('services/namefinder/query', {
          service_type: serviceType,
          account_number: accountNumber,
        })
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error?.response?.data);

            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );
    console.log(data);
    return data?.data;
  }

  /**
   * This endpoint is used to requery a biller for a specific
   * customer's bill details.
   *
   * @param {string} serviceType - The biller's service type
   * @param {string} accountNumber - The customer's account number
   * @returns {Promise<any>} - The bill details
   */
  async requery(reference: string): Promise<any> {
    const { data } = await firstValueFrom(
      this.httpService
        .get(
          `services/superagent/transaction/requery?agentReference=${reference}`,
        )
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error?.response?.data);

            throw  error.response?.data ?? new UnprocessableEntityException(error);
          }),
        ),
    );
    console.log(data);
    return data?.data;
  }
}
