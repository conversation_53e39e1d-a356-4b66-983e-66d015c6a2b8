import { Module } from '@nestjs/common';
import { BaxiService } from './baxi.service';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: config.baxi.baseUrl,
      headers: {
        Authorization: `Bearer ${config.baxi.apiKey}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    }),
  ],
  providers: [BaxiService],
  exports: [BaxiService],
})
export class BaxiModule {}
