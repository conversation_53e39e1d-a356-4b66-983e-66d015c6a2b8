import { Test, TestingModule } from '@nestjs/testing';
import { BaxiService } from './baxi.service';
import { HttpService } from '@nestjs/axios';

describe('BaxiService', () => {
  let service: BaxiService;
  let httpService: HttpService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BaxiService,
        {
          provide: HttpService,
          useValue: {
            post: jest.fn(),
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<BaxiService>(BaxiService);
    httpService = module.get<HttpService>(HttpService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
