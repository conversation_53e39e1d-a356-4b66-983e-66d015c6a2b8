export interface BillersInterface {
  id: number;
  serviceId: string;
  serviceCode: string;
  serviceCategory: string;
  biller_id?: number;
  serviceBiller?: string;
  serviceType: string;
  serviceName?: string;
  serviceDescription?: string;
  serviceHandler?: string;
  serviceProvider?: string;
  serviceEnabled?: string;
  serviceStatus?: string;
  serviceLogo?: string;
  deployed?: string;
  b2b_deployed?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface DataBundleInterface {
  name: string;
  price: number;
  datacode: string;
  validity: string;
  allowance: string;
}

export interface CablePriceInterface {
  _id: string;
  monthsPaidFor: number;
  price: number;
  invoicePeriod: number;
}

export interface CableBundleInterface {
  code: string;
  name: string;
  availablePricingOptions: CablePriceInterface[];
}

export type BillerCategory =
  | 'cabletv'
  | 'airtime'
  | 'databundle'
  | 'epin'
  | 'payment'
  | 'education'
  | 'vehicle'
  | 'transfer'
  | 'insurance'
  | 'gaming'
  | 'betting'
  | 'mobile-money'
  | 'collections'
  | 'electricity'
  | 'gov-payment'

  /// Flutterwave
  | 'tax'
  | 'school'
  | 'religious'
  | 'transport'
  | 'others'
  | 'dealers'
  | 'internet'
  | 'donation';

export const categories: BillerCategory[] = [
  'cabletv',
  'airtime',
  'databundle',
  'internet',
  'epin',
  'payment',
  'education',
  'vehicle',
  'transfer',
  'insurance',
  'gaming',
  'betting',
  'mobile-money',
  'collections',
  'electricity',
  'gov-payment',
  'tax',
  'school',
  'religious',
  'transport',
  'donation',
  'others',
  'dealers',
];
