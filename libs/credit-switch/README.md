# Credit Switch Library

A NestJS library for integrating with the Credit Switch API for bills payment services.

## Features

- ✅ Merchant account information retrieval
- ✅ Transaction requery functionality
- ✅ Airtime vending
- ✅ Data bundle vending
- ✅ Electricity bill payments
- ✅ Electricity meter validation
- ✅ Secure checksum generation and validation
- ✅ Comprehensive error handling
- ✅ Retry logic with exponential backoff
- ✅ Structured logging and monitoring
- ✅ TypeScript support
- ✅ Unit tests with high coverage

## Installation

This library is part of the bills payment service monorepo. It's automatically available when you install the main application dependencies.

## Quick Start

### 1. Import the Module

```typescript
import { CreditSwitchModule } from '@app/credit-switch';

@Module({
  imports: [CreditSwitchModule],
  // ...
})
export class YourModule {}
```

### 2. Inject the Service

```typescript
import { CreditSwitchService, CreditSwitchNetwork } from '@app/credit-switch';

@Injectable()
export class YourService {
  constructor(
    private readonly creditSwitchService: CreditSwitchService,
  ) {}
}
```

### 3. Use the Service

```typescript
// Purchase airtime
const result = await this.creditSwitchService.vendAirtime(
  CreditSwitchNetwork.MTN,
  100,
  '08012345678',
  'unique-request-id'
);

// Check if successful
if (this.creditSwitchService.isTransactionSuccessful(result.statusCode)) {
  console.log('Success!', result);
} else {
  console.log('Failed:', result.statusDescription);
}
```

## Configuration

Set the following environment variables:

```bash
CREDIT_SWITCH_BASE_URL=https://api.creditswitch.com
CREDIT_SWITCH_LOGIN_ID=your_login_id
CREDIT_SWITCH_PUBLIC_KEY=your_public_key
CREDIT_SWITCH_SECRET_KEY=your_secret_key
```

## API Reference

### CreditSwitchService

#### Methods

##### `getMerchantInfo(): Promise<MerchantInfoResponse>`
Retrieves merchant account information including balance and available services.

##### `requery(requestId: string, serviceId: string): Promise<RequeryResponse>`
Checks the status of an existing transaction.

##### `vendAirtime(network: CreditSwitchNetwork, amount: number, recipient: string, requestId: string): Promise<AirtimeVendResponse>`
Performs airtime top-up for the specified network and recipient.

##### `vendData(network: CreditSwitchNetwork, amount: number, recipient: string, requestId: string, datacode?: string): Promise<DataVendResponse>`
Performs data bundle purchase for the specified network and recipient.

##### `validateElectricityMeter(serviceId: string, accountNumber: string, requestId: string): Promise<ElectricityValidationResponse>`
Validates an electricity meter number before payment.

##### `vendElectricity(serviceId: string, amount: number, accountNumber: string, phone: string, requestId: string): Promise<ElectricityVendResponse>`
Performs electricity bill payment.

##### `isTransactionSuccessful(statusCode: string): boolean`
Checks if a transaction was successful based on the status code.

##### `mapNetworkToInternal(network: string): CreditSwitchNetwork`
Maps a network string to the internal network enum.

##### `getElectricityServiceId(provider: string): string`
Gets the service ID for an electricity provider.

### Enums

#### `CreditSwitchNetwork`
- `MTN`
- `AIRTEL`
- `GLO`
- `NINE_MOBILE`

#### `CreditSwitchServiceId`
- `MTN_AIRTIME = 'A01E'`
- `AIRTEL_AIRTIME = 'A02E'`
- `GLO_AIRTIME = 'A03E'`
- `NINE_MOBILE_AIRTIME = 'A04E'`
- `MTN_DATA = 'D01D'`
- `AIRTEL_DATA = 'D02D'`
- `GLO_DATA = 'D03D'`
- `NINE_MOBILE_DATA = 'D04D'`

#### `CreditSwitchElectricityServiceId`
- `AEDC = 'E01E'`
- `EKEDC = 'E02E'`
- `IKEDC = 'E03E'`
- `KEDCO = 'E04E'`
- `PHED = 'E05E'`

## Error Handling

The library includes comprehensive error handling with specific error codes:

```typescript
import { 
  getCreditSwitchErrorInfo,
  isCreditSwitchErrorRetryable,
  formatCreditSwitchError 
} from '@app/credit-switch';

try {
  const result = await this.creditSwitchService.vendAirtime(/* ... */);
} catch (error) {
  const errorInfo = getCreditSwitchErrorInfo(error.statusCode);
  
  if (errorInfo.isRetryable) {
    // Implement retry logic
  } else {
    // Handle final error
  }
}
```

## Logging

The library provides structured logging for all operations:

```typescript
import { CreditSwitchLogger } from '@app/credit-switch';

// Logs are automatically generated for all operations
// Custom logging can be added using:
CreditSwitchLogger.logSuccess({
  operation: 'customOperation',
  requestId: 'req-123',
  statusCode: '00',
  duration: 1000,
});
```

## Testing

Run the tests:

```bash
npm test libs/credit-switch
```

The library includes comprehensive unit tests covering:
- All service methods
- Error scenarios
- Checksum generation and validation
- Utility functions

## Security

### Checksum Generation

The library automatically handles checksum generation for all requests:

```typescript
import { CreditSwitchChecksumUtil } from '@app/credit-switch';

// Generate checksum for merchant details
const checksum = CreditSwitchChecksumUtil.generateMerchantDetailsChecksum(
  loginId,
  publicKey,
  secretKey
);

// Validate checksum
const isValid = CreditSwitchChecksumUtil.validateChecksum(
  data,
  secretKey,
  expectedChecksum
);
```

### Best Practices

1. **Never expose the secret key** in client-side code
2. **Use unique request IDs** for all transactions
3. **Validate all responses** before processing
4. **Implement proper error handling** for all scenarios
5. **Use HTTPS only** for all API communications

## Monitoring

The library includes built-in performance monitoring:

```typescript
import { CreditSwitchMonitor } from '@app/credit-switch';

// Get performance metrics
const metrics = CreditSwitchMonitor.getMetrics();
console.log('Average response time:', metrics.vendAirtime.average);
```

## Contributing

1. Follow the existing code style and patterns
2. Add unit tests for new functionality
3. Update documentation for API changes
4. Ensure all tests pass before submitting

## License

This library is part of the bills payment service and follows the same license terms.

## Support

For technical issues:
1. Check the troubleshooting guide in the main documentation
2. Review the error codes and their meanings
3. Enable debug logging for detailed information
4. Contact the development team for assistance

For Credit Switch API issues:
- Email: <EMAIL>
- Documentation: https://developers.creditswitch.com/
