import * as bcrypt from 'bcrypt';
import { ChecksumData } from '../credit-switch.interface';

/**
 * Credit Switch Checksum Utility
 *
 * Based on the Credit Switch API documentation, checksums are computed using
 * Bcrypt hashing with Base64 encoding. The algorithm is:
 * Checksum = Base64(Bcrypt(ConcatString))
 *
 * Different request types use different concatenation patterns with pipe ("|") separators.
 */
export class CreditSwitchChecksumUtil {
  /**
   * Generate checksum for merchant details request
   * Format: loginId + "|" + privateKey
   * Algorithm: Base64(Bcrypt(ConcatString))
   */
  static generateMerchantDetailsChecksum(
    loginId: number,
    key: string,
    secretKey: string,
  ): string {
    const concatString = `${loginId}|${secretKey}`;
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  /**
   * Generate checksum for airtime/data vend request
   * Format: loginId + "|" + requestId + "|" + serviceId + "|" + requestAmount + "|" + privateKey + "|" + recipient
   * Algorithm: Base64(Bcrypt(ConcatString))
   */
  static generateVendChecksum(
    loginId: number,
    key: string,
    requestId: string,
    serviceId: string,
    amount: number,
    recipient: string,
    secretKey: string,
  ): string {
    const concatString = `${loginId}|${requestId}|${serviceId}|${amount}|${secretKey}|${recipient}`;
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  /**
   * Generate checksum for electricity validation request
   * Format: loginId + "|" + serviceId + "|" + privateKey + "|" + customerAccountId
   * Algorithm: Base64(Bcrypt(ConcatString))
   */
  static generateElectricityValidationChecksum(
    loginId: number,
    key: string,
    serviceId: string,
    accountNumber: string,
    secretKey: string,
  ): string {
    const concatString = `${loginId}|${serviceId}|${secretKey}|${accountNumber}`;
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  /**
   * Generate checksum for electricity vend request
   * Format: loginId + "|" + serviceId + "|" + privateKey + "|" + customerAccountId + "|" + requestId + "|" + amount
   * Algorithm: Base64(Bcrypt(ConcatString))
   */
  static generateElectricityVendChecksum(
    loginId: number,
    requestId: string,
    serviceId: string,
    amount: number,
    accountNumber: string,
    secretKey: string,
  ): string {
    const concatString = `${loginId}|${serviceId}|${secretKey}|${accountNumber}|${requestId}|${amount}`;
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  static generateStartimesValidateChecksum(
    loginId: number,
    smartCardCode: string,
    secretKey: string,
  ): string {
    const concatString = `${loginId}|${secretKey}|${smartCardCode}`;
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  static generateStartimesVendChecksum(
    loginId: number,
    smartCardCode: string,
    fee: number,
    secretKey: string,
  ): string {
    const concatString = `${loginId}|${secretKey}|${smartCardCode}|${fee}`;
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  /**
   * Generate checksum for Multichoice SmartCard/Customer Number validation
   * Format: loginId + "|" + privateKey + "|" + customerNo
   * Algorithm: Base64(Bcrypt(ConcatString))
   */
  static generateMultichoiceValidationChecksum(
    loginId: number,
    customerNo: string,
    secretKey: string,
  ): string {
    const concatString = `${loginId}|${secretKey}|${customerNo}`;
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  /**
   * Generate checksum for Multichoice vend request
   * Format: loginId + "|" + privateKey + "|" + customerNo + "|" + transactionRef + "|" + amount
   * Algorithm: Base64(Bcrypt(ConcatString))
   */
  static generateMultichoiceVendChecksum(
    loginId: number,
    customerNo: string,
    transactionRef: string,
    amount: number,
    secretKey: string,
  ): string {
    const concatString = `${loginId}|${secretKey}|${customerNo}|${transactionRef}|${amount}`;
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  /**
   * Generic checksum generator that handles different request types
   * Uses Bcrypt with Base64 encoding and pipe separators
   */
  static generateChecksum(data: ChecksumData, secretKey: string): string {
    let concatString = '';

    // Merchant details request
    if (!data.requestId && !data.serviceId) {
      concatString = `${data.loginId}|${secretKey}`;
    }
    // Vend request (airtime/data)
    else if (
      data.requestId &&
      data.serviceId &&
      data.amount &&
      data.recipient &&
      data.date
    ) {
      concatString = `${data.loginId}|${data.requestId}|${data.serviceId}|${data.amount}|${secretKey}|${data.recipient}`;
    }
    // Validation request
    else if (
      data.requestId &&
      data.serviceId &&
      data.recipient &&
      !data.amount
    ) {
      concatString = `${data.loginId}|${data.serviceId}|${secretKey}|${data.recipient}`;
    } else {
      throw new Error('Invalid checksum data provided');
    }

    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(concatString, salt);
    return Buffer.from(hash).toString('base64');
  }

  /**
   * Validate checksum by comparing with expected value
   * Note: Due to Bcrypt's salt-based nature, validation requires special handling
   */
  static validateChecksum(
    data: ChecksumData,
    secretKey: string,
    expectedChecksum: string,
  ): boolean {
    try {
      // Decode the expected checksum from Base64
      const decodedHash = Buffer.from(expectedChecksum, 'base64').toString();

      // Generate the concat string based on request type
      let concatString = '';
      if (!data.requestId && !data.serviceId) {
        concatString = `${data.loginId}|${secretKey}`;
      } else if (
        data.requestId &&
        data.serviceId &&
        data.amount &&
        data.recipient &&
        data.date
      ) {
        concatString = `${data.loginId}|${data.requestId}|${data.serviceId}|${data.amount}|${secretKey}|${data.recipient}`;
      } else if (
        data.requestId &&
        data.serviceId &&
        data.recipient &&
        !data.amount
      ) {
        concatString = `${data.loginId}|${data.serviceId}|${secretKey}|${data.recipient}`;
      } else {
        return false;
      }

      // Use bcrypt.compareSync to validate
      return bcrypt.compareSync(concatString, decodedHash);
    } catch {
      return false;
    }
  }

  /**
   * Generate MD5 checksum (deprecated - kept for backward compatibility)
   * Note: Credit Switch now uses Bcrypt with Base64 encoding
   * @deprecated Use generateChecksum instead
   */
  static generateMD5Checksum(data: ChecksumData, secretKey: string): string {
    // Redirect to the new Bcrypt-based method
    return this.generateChecksum(data, secretKey);
  }
}
