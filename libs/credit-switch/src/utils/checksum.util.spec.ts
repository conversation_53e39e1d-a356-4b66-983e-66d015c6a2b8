import { CreditSwitchChecksumUtil } from './checksum.util';
import { ChecksumData } from '../credit-switch.interface';

describe('CreditSwitchChecksumUtil', () => {
  const testSecretKey = 'test-secret-key';

  describe('generateMerchantDetailsChecksum', () => {
    it('should generate correct checksum for merchant details', () => {
      const loginId = 12345;
      const key = 'test-public-key';

      const checksum = CreditSwitchChecksumUtil.generateMerchantDetailsChecksum(
        loginId,
        key,
        testSecretKey,
      );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      // Base64 encoded Bcrypt hash length varies, but should be reasonable
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });

    it('should generate different checksums for same input due to salt', () => {
      const loginId = 12345;
      const key = 'test-public-key';

      const checksum1 =
        CreditSwitchChecksumUtil.generateMerchantDetailsChecksum(
          loginId,
          key,
          testSecretKey,
        );

      const checksum2 =
        CreditSwitchChecksumUtil.generateMerchantDetailsChecksum(
          loginId,
          key,
          testSecretKey,
        );

      // Bcrypt uses salt, so checksums will be different each time
      expect(checksum1).not.toBe(checksum2);
      expect(checksum1.length).toBeGreaterThan(20);
      expect(checksum2.length).toBeGreaterThan(20);
    });

    it('should generate different checksums for different inputs', () => {
      const checksum1 =
        CreditSwitchChecksumUtil.generateMerchantDetailsChecksum(
          12345,
          'key1',
          testSecretKey,
        );

      const checksum2 =
        CreditSwitchChecksumUtil.generateMerchantDetailsChecksum(
          12345,
          'key2',
          testSecretKey,
        );

      expect(checksum1).not.toBe(checksum2);
    });
  });

  describe('generateVendChecksum', () => {
    it('should generate correct checksum for vend request', () => {
      const checksum = CreditSwitchChecksumUtil.generateVendChecksum(
        12345,
        'test-key',
        'req-123',
        'A01E',
        100,
        '08012345678',
        '2023-01-01 12:00:00 GMT',
        testSecretKey,
      );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });

    it('should generate different checksums for same vend input due to salt', () => {
      const params = [
        12345,
        'test-key',
        'req-123',
        'A01E',
        100,
        '08012345678',
        '2023-01-01 12:00:00 GMT',
        testSecretKey,
      ] as const;

      const checksum1 = CreditSwitchChecksumUtil.generateVendChecksum(
        ...params,
      );
      const checksum2 = CreditSwitchChecksumUtil.generateVendChecksum(
        ...params,
      );

      // Bcrypt uses salt, so checksums will be different each time
      expect(checksum1).not.toBe(checksum2);
      expect(checksum1.length).toBeGreaterThan(20);
      expect(checksum2.length).toBeGreaterThan(20);
    });
  });

  describe('generateElectricityValidationChecksum', () => {
    it('should generate correct checksum for electricity validation', () => {
      const checksum =
        CreditSwitchChecksumUtil.generateElectricityValidationChecksum(
          12345,
          'test-key',
          'req-123',
          'E01E',
          '12345678901',
          testSecretKey,
        );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });
  });

  describe('generateElectricityVendChecksum', () => {
    it('should generate correct checksum for electricity vend', () => {
      const checksum = CreditSwitchChecksumUtil.generateElectricityVendChecksum(
        12345,
        'test-key',
        'req-123',
        'E01E',
        1000,
        '12345678901',
        '08012345678',
        '2023-01-01 12:00:00 GMT',
        testSecretKey,
      );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });
  });

  describe('generateChecksum (generic)', () => {
    it('should generate checksum for merchant details request', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
      };

      const checksum = CreditSwitchChecksumUtil.generateChecksum(
        data,
        testSecretKey,
      );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });

    it('should generate checksum for vend request', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
        requestId: 'req-123',
        serviceId: 'A01E',
        amount: 100,
        recipient: '08012345678',
        date: '2023-01-01 12:00:00 GMT',
      };

      const checksum = CreditSwitchChecksumUtil.generateChecksum(
        data,
        testSecretKey,
      );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });

    it('should generate checksum for validation request', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
        requestId: 'req-123',
        serviceId: 'E01E',
        recipient: '12345678901',
      };

      const checksum = CreditSwitchChecksumUtil.generateChecksum(
        data,
        testSecretKey,
      );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });

    it('should throw error for invalid checksum data', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
        requestId: 'req-123',
        // Missing required fields for any valid request type
      };

      expect(() => {
        CreditSwitchChecksumUtil.generateChecksum(data, testSecretKey);
      }).toThrow('Invalid checksum data provided');
    });
  });

  describe('validateChecksum', () => {
    it('should validate correct checksum', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
      };

      const checksum = CreditSwitchChecksumUtil.generateChecksum(
        data,
        testSecretKey,
      );
      const isValid = CreditSwitchChecksumUtil.validateChecksum(
        data,
        testSecretKey,
        checksum,
      );

      expect(isValid).toBe(true);
    });

    it('should reject incorrect checksum', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
      };

      const isValid = CreditSwitchChecksumUtil.validateChecksum(
        data,
        testSecretKey,
        'invalid-checksum',
      );

      expect(isValid).toBe(false);
    });
  });

  describe('generateMD5Checksum', () => {
    it('should generate checksum for merchant details (redirects to Bcrypt)', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
      };

      const checksum = CreditSwitchChecksumUtil.generateMD5Checksum(
        data,
        testSecretKey,
      );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      // Now returns Base64 encoded Bcrypt hash, not MD5
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });

    it('should generate checksum for vend request (redirects to Bcrypt)', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
        requestId: 'req-123',
        serviceId: 'A01E',
        amount: 100,
        recipient: '08012345678',
        date: '2023-01-01 12:00:00 GMT',
      };

      const checksum = CreditSwitchChecksumUtil.generateMD5Checksum(
        data,
        testSecretKey,
      );

      expect(checksum).toBeDefined();
      expect(typeof checksum).toBe('string');
      // Now returns Base64 encoded Bcrypt hash, not MD5
      expect(checksum.length).toBeGreaterThan(20);
      expect(checksum.length).toBeLessThan(200);
    });

    it('should throw error for invalid checksum data (redirects to Bcrypt)', () => {
      const data: ChecksumData = {
        loginId: 12345,
        key: 'test-key',
        requestId: 'req-123',
        // Missing required fields
      };

      expect(() => {
        CreditSwitchChecksumUtil.generateMD5Checksum(data, testSecretKey);
      }).toThrow('Invalid checksum data provided');
    });
  });
});
