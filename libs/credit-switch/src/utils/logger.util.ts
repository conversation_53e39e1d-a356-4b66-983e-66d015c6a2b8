/**
 * Credit Switch Logging Utility
 *
 * Provides structured logging for Credit Switch operations with proper
 * context, correlation IDs, and monitoring capabilities.
 */

export interface CreditSwitchLogContext {
  operation: string;
  requestId?: string;
  serviceId?: string;
  amount?: number;
  recipient?: string;
  statusCode?: string;
  statusDescription?: string;
  duration?: number;
  error?: any;
  metadata?: Record<string, any>;
}

export class CreditSwitchLogger {
  private static readonly LOG_PREFIX = '[CREDIT_SWITCH]';

  /**
   * Log successful Credit Switch operation
   */
  static logSuccess(context: CreditSwitchLogContext): void {
    const logData = {
      level: 'info',
      message: `${this.LOG_PREFIX} Operation successful`,
      operation: context.operation,
      requestId: context.requestId,
      serviceId: context.serviceId,
      amount: context.amount,
      recipient: this.maskSensitiveData(context.recipient),
      statusCode: context.statusCode,
      duration: context.duration,
      timestamp: new Date().toISOString(),
      ...context.metadata,
    };

    console.log(JSON.stringify(logData));
  }

  /**
   * Log Credit Switch operation error
   */
  static logError(context: CreditSwitchLogContext): void {
    const logData = {
      level: 'error',
      message: `${this.LOG_PREFIX} Operation failed`,
      operation: context.operation,
      requestId: context.requestId,
      serviceId: context.serviceId,
      amount: context.amount,
      recipient: this.maskSensitiveData(context.recipient),
      statusCode: context.statusCode,
      statusDescription: context.statusDescription,
      error: context.error?.message || context.error,
      errorStack: context.error?.stack,
      duration: context.duration,
      timestamp: new Date().toISOString(),
      ...context.metadata,
    };

    console.error(JSON.stringify(logData));
  }

  /**
   * Log Credit Switch operation warning
   */
  static logWarning(context: CreditSwitchLogContext): void {
    const logData = {
      level: 'warn',
      message: `${this.LOG_PREFIX} Operation warning`,
      operation: context.operation,
      requestId: context.requestId,
      serviceId: context.serviceId,
      statusCode: context.statusCode,
      statusDescription: context.statusDescription,
      duration: context.duration,
      timestamp: new Date().toISOString(),
      ...context.metadata,
    };

    console.warn(JSON.stringify(logData));
  }

  /**
   * Log Credit Switch operation start
   */
  static logStart(context: CreditSwitchLogContext): void {
    const logData = {
      level: 'info',
      message: `${this.LOG_PREFIX} Operation started`,
      operation: context.operation,
      requestId: context.requestId,
      serviceId: context.serviceId,
      amount: context.amount,
      recipient: this.maskSensitiveData(context.recipient),
      timestamp: new Date().toISOString(),
      ...context.metadata,
    };

    console.log(JSON.stringify(logData));
  }

  /**
   * Log Credit Switch API request
   */
  static logRequest(
    endpoint: string,
    requestData: any,
    requestId?: string,
  ): void {
    const logData = {
      level: 'debug',
      message: `${this.LOG_PREFIX} API Request`,
      endpoint,
      requestId,
      requestData: this.sanitizeRequestData(requestData),
      timestamp: new Date().toISOString(),
    };

    console.log(JSON.stringify(logData));
  }

  /**
   * Log Credit Switch API response
   */
  static logResponse(
    endpoint: string,
    responseData: any,
    requestId?: string,
    duration?: number,
  ): void {
    const logData = {
      level: 'debug',
      message: `${this.LOG_PREFIX} API Response`,
      endpoint,
      requestId,
      statusCode: responseData?.statusCode,
      statusDescription: responseData?.statusDescription,
      duration,
      timestamp: new Date().toISOString(),
    };

    console.log(JSON.stringify(logData));
  }

  /**
   * Log Credit Switch retry attempt
   */
  static logRetry(
    operation: string,
    attempt: number,
    maxRetries: number,
    error: any,
    requestId?: string,
  ): void {
    const logData = {
      level: 'warn',
      message: `${this.LOG_PREFIX} Retry attempt`,
      operation,
      attempt,
      maxRetries,
      requestId,
      error: error?.message || error,
      timestamp: new Date().toISOString(),
    };

    console.warn(JSON.stringify(logData));
  }

  /**
   * Log Credit Switch escalation
   */
  static logEscalation(context: CreditSwitchLogContext): void {
    const logData = {
      level: 'error',
      message: `${this.LOG_PREFIX} Error escalated to support`,
      operation: context.operation,
      requestId: context.requestId,
      serviceId: context.serviceId,
      statusCode: context.statusCode,
      statusDescription: context.statusDescription,
      error: context.error?.message || context.error,
      timestamp: new Date().toISOString(),
      escalated: true,
      ...context.metadata,
    };

    console.error(JSON.stringify(logData));
  }

  /**
   * Mask sensitive data for logging
   */
  private static maskSensitiveData(data?: string): string | undefined {
    if (!data) return data;

    // Mask phone numbers (show first 3 and last 2 digits)
    if (data.match(/^\d{10,11}$/)) {
      return (
        data.substring(0, 3) +
        '*'.repeat(data.length - 5) +
        data.substring(data.length - 2)
      );
    }

    // Mask account numbers (show first 2 and last 2 digits)
    if (data.length > 4) {
      return (
        data.substring(0, 2) +
        '*'.repeat(data.length - 4) +
        data.substring(data.length - 2)
      );
    }

    return data;
  }

  /**
   * Sanitize request data for logging (remove sensitive fields)
   */
  private static sanitizeRequestData(data: any): any {
    if (!data || typeof data !== 'object') return data;

    const sanitized = { ...data };

    // Remove sensitive fields
    const sensitiveFields = ['checksum', 'key', 'secretKey', 'password', 'pin'];
    sensitiveFields.forEach((field) => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    // Mask recipient data
    if (sanitized.recipient) {
      sanitized.recipient = this.maskSensitiveData(sanitized.recipient);
    }

    return sanitized;
  }
}

/**
 * Performance monitoring utility for Credit Switch operations
 */
export class CreditSwitchMonitor {
  private static readonly metrics: Map<string, number[]> = new Map();

  /**
   * Record operation duration
   */
  static recordDuration(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }

    const durations = this.metrics.get(operation)!;
    durations.push(duration);

    // Keep only last 100 measurements
    if (durations.length > 100) {
      durations.shift();
    }
  }

  /**
   * Get average duration for an operation
   */
  static getAverageDuration(operation: string): number {
    const durations = this.metrics.get(operation);
    if (!durations || durations.length === 0) return 0;

    return (
      durations.reduce((sum, duration) => sum + duration, 0) / durations.length
    );
  }

  /**
   * Get performance metrics for all operations
   */
  static getMetrics(): Record<
    string,
    { average: number; count: number; max: number; min: number }
  > {
    const result: Record<string, any> = {};

    this.metrics.forEach((durations, operation) => {
      if (durations.length > 0) {
        result[operation] = {
          average: durations.reduce((sum, d) => sum + d, 0) / durations.length,
          count: durations.length,
          max: Math.max(...durations),
          min: Math.min(...durations),
        };
      }
    });

    return result;
  }

  /**
   * Clear metrics
   */
  static clearMetrics(): void {
    this.metrics.clear();
  }
}
