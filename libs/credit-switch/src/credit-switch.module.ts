import { Module } from '@nestjs/common';
import { CreditSwitchService } from './credit-switch.service';
import { HttpModule } from '@nestjs/axios';
import config from 'src/config';

@Module({
  imports: [
    HttpModule.register({
      timeout: 100000,
      maxRedirects: 5,
      baseURL: config.creditSwitch.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    }),
  ],
  providers: [CreditSwitchService],
  exports: [CreditSwitchService],
})
export class CreditSwitchModule {}
