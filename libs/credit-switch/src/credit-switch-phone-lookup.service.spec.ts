import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { UnprocessableEntityException } from '@nestjs/common';
import { of, throwError } from 'rxjs';
import { AxiosResponse } from 'axios';
import { CreditSwitchService } from './credit-switch.service';
import { PhoneNumberLookupResponse } from './credit-switch.interface';

describe('CreditSwitchService - Phone Number Lookup', () => {
  let service: CreditSwitchService;
  let httpService: HttpService;

  const mockHttpService = {
    post: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreditSwitchService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<CreditSwitchService>(CreditSwitchService);
    httpService = module.get<HttpService>(HttpService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('lookupPhoneNumber', () => {
    const mockPhoneNumber = '08012345678';
    const mockSuccessResponse: PhoneNumberLookupResponse = {
      statusCode: '00',
      statusDescription: 'Successful',
      network: 'MTN',
    };

    it('should successfully lookup phone number', async () => {
      const mockAxiosResponse: AxiosResponse = {
        data: mockSuccessResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.lookupPhoneNumber(mockPhoneNumber);

      expect(result).toEqual(mockSuccessResponse);
      expect(mockHttpService.post).toHaveBeenCalledWith('/api/v1/phonelookup', {
        loginId: expect.any(Number),
        msisdn: mockPhoneNumber,
        key: expect.any(String),
      });
    });

    it('should handle lookup error response', async () => {
      const mockErrorResponse = {
        statusCode: '01',
        statusDescription: 'Invalid phone number',
      };

      const mockAxiosResponse: AxiosResponse = {
        data: mockErrorResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.lookupPhoneNumber(mockPhoneNumber);

      expect(result).toEqual(mockErrorResponse);
    });

    it('should handle HTTP error', async () => {
      const mockError = {
        response: {
          data: { message: 'Network error' },
        },
      };

      mockHttpService.post.mockReturnValue(throwError(() => mockError));

      await expect(service.lookupPhoneNumber(mockPhoneNumber)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });

    it('should handle network timeout', async () => {
      const mockError = {
        code: 'ECONNABORTED',
        message: 'timeout of 30000ms exceeded',
      };

      mockHttpService.post.mockReturnValue(throwError(() => mockError));

      await expect(service.lookupPhoneNumber(mockPhoneNumber)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });

    it('should validate phone number format', async () => {
      const validPhoneNumbers = [
        '08012345678',
        '07012345678',
        '09012345678',
        '08112345678',
      ];

      for (const phoneNumber of validPhoneNumbers) {
        const mockAxiosResponse: AxiosResponse = {
          data: { ...mockSuccessResponse, msisdn: phoneNumber },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {} as any,
        };

        mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

        const result = await service.lookupPhoneNumber(phoneNumber);
        expect(result.msisdn).toBe(phoneNumber);
      }
    });

    it('should handle different network responses', async () => {
      const networkTests = [
        { network: 'MTN', operator: 'MTN Nigeria' },
        { network: 'AIRTEL', operator: 'Airtel Nigeria' },
        { network: 'GLO', operator: 'Globacom Nigeria' },
        { network: '9MOBILE', operator: '9mobile Nigeria' },
      ];

      for (const networkTest of networkTests) {
        const mockResponse = {
          ...mockSuccessResponse,
          network: networkTest.network,
          operator: networkTest.operator,
        };

        const mockAxiosResponse: AxiosResponse = {
          data: mockResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {} as any,
        };

        mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

        const result = await service.lookupPhoneNumber(mockPhoneNumber);
        expect(result.network).toBe(networkTest.network);
        expect(result.operator).toBe(networkTest.operator);
      }
    });

    it('should handle premium service unavailable', async () => {
      const mockErrorResponse = {
        statusCode: '99',
        statusDescription: 'Premium service unavailable',
      };

      const mockAxiosResponse: AxiosResponse = {
        data: mockErrorResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.lookupPhoneNumber(mockPhoneNumber);
      expect(result.statusCode).toBe('99');
      expect(result.statusDescription).toBe('Premium service unavailable');
    });

    it('should handle malformed response', async () => {
      const mockAxiosResponse: AxiosResponse = {
        data: null,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.lookupPhoneNumber(mockPhoneNumber);
      expect(result).toBeNull();
    });
  });
});
