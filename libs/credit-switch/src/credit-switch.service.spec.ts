import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { CreditSwitchService } from './credit-switch.service';
import { of, throwError } from 'rxjs';
import { UnprocessableEntityException } from '@nestjs/common';
import { CreditSwitchNetwork, CreditSwitchStatusCode } from './credit-switch.interface';

// Mock config
jest.mock('src/config', () => ({
  default: {
    creditSwitch: {
      baseUrl: 'https://api.creditswitch.com',
      loginId: 12345,
      publicKey: 'test-public-key',
      secretKey: 'test-secret-key',
    },
  },
}));

describe('CreditSwitchService', () => {
  let service: CreditSwitchService;
  let httpService: HttpService;

  const mockHttpService = {
    post: jest.fn(),
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreditSwitchService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<CreditSwitchService>(CreditSwitchService);
    httpService = module.get<HttpService>(HttpService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getMerchantInfo', () => {
    it('should successfully fetch merchant info', async () => {
      const mockResponse = {
        data: {
          statusCode: '00',
          statusDescription: {
            name: 'Test Merchant',
            balance: '1000.00',
            status: 'active',
            email: '<EMAIL>',
            allowed_ips: ['127.0.0.1'],
            serviceDetail: [['A01E', '5.00']],
            transactionsToday: 10,
          },
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await service.getMerchantInfo();

      expect(result).toEqual(mockResponse.data);
      expect(mockHttpService.post).toHaveBeenCalledWith('/api/v1/mdetails', expect.objectContaining({
        loginId: 12345,
        key: 'test-public-key',
        checksum: expect.any(String),
      }));
    });

    it('should handle merchant info error', async () => {
      const mockError = {
        response: {
          data: { statusCode: '01', statusDescription: 'Error occurred' },
        },
      };

      mockHttpService.post.mockReturnValue(throwError(() => mockError));

      await expect(service.getMerchantInfo()).rejects.toThrow(UnprocessableEntityException);
    });
  });

  describe('requery', () => {
    it('should successfully requery transaction', async () => {
      const mockResponse = {
        data: {
          statusCode: '00',
          statusDescription: 'Successful',
          mReference: 'test-ref',
          tranxReference: *********,
          recipient: '***********',
          amount: '100.00',
          confirmCode: 'ABC123',
          network: 'MTN',
          tranxDate: '2023-01-01 12:00:00',
        },
      };

      mockHttpService.get.mockReturnValue(of(mockResponse));

      const result = await service.requery('test-ref', 'A01E');

      expect(result).toEqual(mockResponse.data);
      expect(mockHttpService.get).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/requery?loginId=12345&key=test-public-key&requestId=test-ref&serviceId=A01E')
      );
    });

    it('should handle requery error', async () => {
      const mockError = {
        response: {
          data: { statusCode: '01', statusDescription: 'Transaction not found' },
        },
      };

      mockHttpService.get.mockReturnValue(throwError(() => mockError));

      await expect(service.requery('test-ref', 'A01E')).rejects.toThrow(UnprocessableEntityException);
    });
  });

  describe('vendAirtime', () => {
    it('should successfully vend airtime', async () => {
      const mockResponse = {
        data: {
          statusCode: '00',
          statusDescription: 'Successful',
          mReference: 'test-ref',
          tranxReference: *********,
          recipient: '***********',
          amount: '100.00',
          confirmCode: 'ABC123',
          network: 'MTN',
          tranxDate: '2023-01-01 12:00:00',
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await service.vendAirtime(
        CreditSwitchNetwork.MTN,
        100,
        '***********',
        'test-ref'
      );

      expect(result).toEqual(mockResponse.data);
      expect(mockHttpService.post).toHaveBeenCalledWith('/api/v1/mvend', expect.objectContaining({
        loginId: 12345,
        key: 'test-public-key',
        requestId: 'test-ref',
        serviceId: 'A01E',
        amount: 100,
        recipient: '***********',
        checksum: expect.any(String),
      }));
    });

    it('should handle airtime vend error', async () => {
      const mockError = {
        response: {
          data: { statusCode: '02', statusDescription: 'Insufficient balance' },
        },
      };

      mockHttpService.post.mockReturnValue(throwError(() => mockError));

      await expect(service.vendAirtime(
        CreditSwitchNetwork.MTN,
        100,
        '***********',
        'test-ref'
      )).rejects.toThrow(UnprocessableEntityException);
    });
  });

  describe('vendData', () => {
    it('should successfully vend data', async () => {
      const mockResponse = {
        data: {
          statusCode: '00',
          statusDescription: 'Successful',
          mReference: 'test-ref',
          tranxReference: *********,
          recipient: '***********',
          amount: '500.00',
          network: 'MTN',
          tranxDate: '2023-01-01 12:00:00',
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await service.vendData(
        CreditSwitchNetwork.MTN,
        500,
        '***********',
        'test-ref',
        'MTN_1GB'
      );

      expect(result).toEqual(mockResponse.data);
      expect(mockHttpService.post).toHaveBeenCalledWith('/api/v1/mvend', expect.objectContaining({
        serviceId: 'D01D',
        amount: 500,
        datacode: 'MTN_1GB',
      }));
    });
  });

  describe('validateElectricityMeter', () => {
    it('should successfully validate electricity meter', async () => {
      const mockResponse = {
        data: {
          statusCode: '00',
          statusDescription: 'Valid meter',
          customerName: 'John Doe',
          customerAddress: '123 Test Street',
          accountType: 'Prepaid',
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await service.validateElectricityMeter('E01E', '***********', 'test-ref');

      expect(result).toEqual(mockResponse.data);
      expect(mockHttpService.post).toHaveBeenCalledWith('/api/v1/validate', expect.objectContaining({
        serviceId: 'E01E',
        account_number: '***********',
      }));
    });
  });

  describe('vendElectricity', () => {
    it('should successfully vend electricity', async () => {
      const mockResponse = {
        data: {
          statusCode: '00',
          statusDescription: 'Successful',
          mReference: 'test-ref',
          tranxReference: *********,
          recipient: '***********',
          amount: '1000.00',
          token: '***********23456789',
          tranxDate: '2023-01-01 12:00:00',
        },
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await service.vendElectricity(
        'E01E',
        1000,
        '***********',
        '***********',
        'test-ref'
      );

      expect(result).toEqual(mockResponse.data);
      expect(mockHttpService.post).toHaveBeenCalledWith('/api/v1/mvend', expect.objectContaining({
        serviceId: 'E01E',
        amount: 1000,
        account_number: '***********',
        phone: '***********',
      }));
    });
  });

  describe('utility methods', () => {
    it('should correctly identify successful transactions', () => {
      expect(service.isTransactionSuccessful('00')).toBe(true);
      expect(service.isTransactionSuccessful('01')).toBe(false);
    });

    it('should correctly map network to internal enum', () => {
      expect(service.mapNetworkToInternal('mtn')).toBe(CreditSwitchNetwork.MTN);
      expect(service.mapNetworkToInternal('airtel')).toBe(CreditSwitchNetwork.AIRTEL);
      expect(service.mapNetworkToInternal('glo')).toBe(CreditSwitchNetwork.GLO);
      expect(service.mapNetworkToInternal('9mobile')).toBe(CreditSwitchNetwork.NINE_MOBILE);
      expect(service.mapNetworkToInternal('unknown')).toBe(CreditSwitchNetwork.MTN);
    });

    it('should get correct electricity service ID', () => {
      expect(service.getElectricityServiceId('aedc')).toBe('E01E');
      expect(service.getElectricityServiceId('ekedc')).toBe('E02E');
      expect(service.getElectricityServiceId('unknown')).toBe('E01E');
    });
  });
});
