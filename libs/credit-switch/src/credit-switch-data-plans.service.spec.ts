import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { UnprocessableEntityException } from '@nestjs/common';
import { of, throwError } from 'rxjs';
import { AxiosResponse } from 'axios';
import { CreditSwitchService } from './credit-switch.service';
import { DataPlansResponse, DataPlan } from './credit-switch.interface';

describe('CreditSwitchService - Data Plans', () => {
  let service: CreditSwitchService;

  const mockHttpService = {
    post: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreditSwitchService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<CreditSwitchService>(CreditSwitchService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDataPlans', () => {
    const mockServiceId = 'D02D';
    const mockDataPlans: DataPlan[] = [
      {
        amount: 50,
        databundle: '25MB',
        validity: '1day',
        productId: '9MO-25MB-15',
      },
      {
        amount: 100,
        databundle: '100MB',
        validity: '1day',
        productId: '9MO-100MB-1',
      },
      {
        amount: 1000,
        databundle: '1GB',
        validity: '30days',
        productId: '9MO-1GB-3',
      },
    ];

    const mockSuccessResponse: DataPlansResponse = {
      statusCode: '00',
      statusDescription: 'successful',
      serviceId: mockServiceId,
      dataPlan: mockDataPlans,
    };

    it('should successfully fetch data plans', async () => {
      const mockAxiosResponse: AxiosResponse = {
        data: mockSuccessResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.getDataPlans(mockServiceId);

      expect(result).toEqual(mockSuccessResponse);
      expect(mockHttpService.post).toHaveBeenCalledWith('/api/v1/mdataplans', {
        loginId: expect.any(Number),
        serviceId: mockServiceId,
        key: expect.any(String),
      });
    });

    it('should handle empty data plans response', async () => {
      const mockEmptyResponse: DataPlansResponse = {
        statusCode: '00',
        statusDescription: 'successful',
        serviceId: mockServiceId,
        dataPlan: [],
      };

      const mockAxiosResponse: AxiosResponse = {
        data: mockEmptyResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.getDataPlans(mockServiceId);

      expect(result).toEqual(mockEmptyResponse);
      expect(result.dataPlan).toHaveLength(0);
    });

    it('should handle error response', async () => {
      const mockErrorResponse: DataPlansResponse = {
        statusCode: '01',
        statusDescription: 'Invalid service ID',
        serviceId: mockServiceId,
        dataPlan: [],
      };

      const mockAxiosResponse: AxiosResponse = {
        data: mockErrorResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.getDataPlans(mockServiceId);

      expect(result).toEqual(mockErrorResponse);
      expect(result.statusCode).toBe('01');
    });

    it('should handle HTTP error', async () => {
      const mockError = {
        response: {
          data: { message: 'Service unavailable' },
        },
      };

      mockHttpService.post.mockReturnValue(throwError(() => mockError));

      await expect(service.getDataPlans(mockServiceId)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });

    it('should handle different service IDs', async () => {
      const serviceIds = ['D01D', 'D02D', 'D03D', 'D04D'];

      for (const serviceId of serviceIds) {
        const mockResponse = {
          ...mockSuccessResponse,
          serviceId,
        };

        const mockAxiosResponse: AxiosResponse = {
          data: mockResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {} as any,
        };

        mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

        const result = await service.getDataPlans(serviceId);
        expect(result.serviceId).toBe(serviceId);
      }
    });

    it('should handle large data plans response', async () => {
      const largeDataPlans: DataPlan[] = Array.from({ length: 50 }, (_, i) => ({
        amount: (i + 1) * 100,
        databundle: `${i + 1}GB`,
        validity: '30days',
        productId: `PLAN-${i + 1}`,
      }));

      const mockLargeResponse: DataPlansResponse = {
        statusCode: '00',
        statusDescription: 'successful',
        serviceId: mockServiceId,
        dataPlan: largeDataPlans,
      };

      const mockAxiosResponse: AxiosResponse = {
        data: mockLargeResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.getDataPlans(mockServiceId);

      expect(result.dataPlan).toHaveLength(50);
      expect(result.dataPlan[0].amount).toBe(100);
      expect(result.dataPlan[49].amount).toBe(5000);
    });

    it('should validate data plan structure', async () => {
      const mockAxiosResponse: AxiosResponse = {
        data: mockSuccessResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.getDataPlans(mockServiceId);

      expect(result.dataPlan).toBeDefined();
      expect(Array.isArray(result.dataPlan)).toBe(true);

      result.dataPlan.forEach((plan) => {
        expect(plan).toHaveProperty('amount');
        expect(plan).toHaveProperty('databundle');
        expect(plan).toHaveProperty('validity');
        expect(plan).toHaveProperty('productId');
        expect(typeof plan.amount).toBe('number');
        expect(typeof plan.databundle).toBe('string');
        expect(typeof plan.validity).toBe('string');
        expect(typeof plan.productId).toBe('string');
      });
    });

    it('should handle network timeout', async () => {
      const mockError = {
        code: 'ECONNABORTED',
        message: 'timeout of 30000ms exceeded',
      };

      mockHttpService.post.mockReturnValue(throwError(() => mockError));

      await expect(service.getDataPlans(mockServiceId)).rejects.toThrow(
        UnprocessableEntityException,
      );
    });

    it('should handle malformed JSON response', async () => {
      const mockAxiosResponse: AxiosResponse = {
        data: 'invalid json',
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.getDataPlans(mockServiceId);
      expect(result).toBe('invalid json');
    });

    it('should handle service maintenance response', async () => {
      const mockMaintenanceResponse: DataPlansResponse = {
        statusCode: '99',
        statusDescription: 'Service under maintenance',
        serviceId: mockServiceId,
        dataPlan: [],
      };

      const mockAxiosResponse: AxiosResponse = {
        data: mockMaintenanceResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as any,
      };

      mockHttpService.post.mockReturnValue(of(mockAxiosResponse));

      const result = await service.getDataPlans(mockServiceId);
      expect(result.statusCode).toBe('99');
      expect(result.statusDescription).toBe('Service under maintenance');
    });
  });
});
