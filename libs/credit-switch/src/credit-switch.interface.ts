// Credit Switch API Request/Response Interfaces

export interface CreditSwitchBaseRequest {
  loginId: number;
  key: string;
  checksum: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface MerchantInfoRequest extends CreditSwitchBaseRequest {}

export interface MerchantInfoResponse {
  statusCode: string;
  statusDescription: {
    name: string;
    balance: string;
    status: string;
    email: string;
    allowed_ips: string[];
    serviceDetail: string[][];
    transactionsToday: number;
  };
}

export interface RequeryRequest {
  loginId: number;
  key: string;
  requestId: string;
  serviceId: string;
}

export interface RequeryResponse {
  statusCode: string;
  statusDescription: string;
  mReference: string;
  tranxReference: number;
  recipient: string;
  amount: string;
  confirmCode?: string; // For airtime/data
  network?: string; // For airtime/data
  token?: string; // For electricity
  tranxDate: string;
}

export interface AirtimeVendRequest extends CreditSwitchBaseRequest {
  requestId: string;
  serviceId: string;
  amount: number;
  recipient: string;
  date: string;
}

export interface AirtimeVendResponse {
  statusCode: string;
  statusDescription: string;
  mReference: string;
  tranxReference: number;
  recipient: string;
  amount: string;
  confirmCode?: string;
  network?: string;
  tranxDate: string;
}

// Service ID mappings for different networks
export enum CreditSwitchServiceId {
  // Airtime
  AIRTEL_AIRTIME = 'A01E',
  NINE_MOBILE_AIRTIME = 'A02E',
  GLO_AIRTIME = 'A03E',
  MTN_AIRTIME = 'A04E',

  // Data
  AIRTEL_DATA = 'D01D',
  NINE_MOBILE_DATA = 'D02D',
  GLO_DATA = 'D03D',
  MTN_DATA = 'D04D',
  SMILE_DATA = 'D05D',
  NTEL_DATA = 'D06D',

  // Electricity
  IKEJA_ELECTRIC_PREPAID = 'E01E',
  IKEJA_ELECTRIC_POSTPAID = 'E02E',
  IBADAN_ELECTRIC_PREPAID = 'E03E',
  IBADAN_ELECTRIC_POSTPAID = 'E04E',
  EKO_ELECTRIC_PREPAID = 'E05E',
  EKO_ELECTRIC_POSTPAID = 'E06E',
  ABUJA_ELECTRIC_PREPAID = 'E07E',
  ABUJA_ELECTRIC_POSTPAID = 'E08E',
  PORT_HARCOURT_ELECTRIC_PREPAID = 'E09E',
  PORT_HARCOURT_ELECTRIC_POSTPAID = 'E10E',
  KADUNA_ELECTRIC_PREPAID = 'E11E',
  KADUNA_ELECTRIC_POSTPAID = 'E12E',
  JOS_ELECTRIC_PREPAID = 'E13E',
  JOS_ELECTRIC_POSTPAID = 'E14E',
  ENUGU_ELECTRIC_PREPAID = 'E15E',
  ENUGU_ELECTRIC_POSTPAID = 'E16E',
  KANO_ELECTRIC_PREPAID = 'E17E',
  KANO_ELECTRIC_POSTPAID = 'E18E',
}

// Network mapping for vendor types
export enum CreditSwitchNetwork {
  MTN = 'mtn',
  AIRTEL = 'airtel',
  GLO = 'glo',
  NINE_MOBILE = '9mobile',
}

// Status codes
export enum CreditSwitchStatusCode {
  SUCCESS = '00',
  FAILED = '01',
  INSUFFICIENT_BALANCE = '02',
  INVALID_RECIPIENT = '03',
  NETWORK_ERROR = '04',
  DUPLICATE_REQUEST = '05',
}

export interface CreditSwitchErrorResponse {
  statusCode: string;
  statusDescription: string;
  error?: string;
}

// Utility type for checksum calculation
export interface ChecksumData {
  loginId: number;
  key: string;
  requestId?: string;
  serviceId?: string;
  amount?: number;
  recipient?: string;
  date?: string;
}

// Extended interfaces for additional services

export interface DataVendRequest extends CreditSwitchBaseRequest {
  requestId: string;
  serviceId: string;
  amount: number;
  recipient: string;
  date: string;
  productId: string;
}

export interface DataVendResponse extends AirtimeVendResponse {
  productId?: string;
  validity?: string;
  allowance?: string;
}

export interface ElectricityValidationRequest extends CreditSwitchBaseRequest {
  serviceId: string;
  customerAccountId: string;
}

export interface ElectricityValidationResponse {
  statusCode: string;
  statusDescription: string;
  detail: {
    name: string;
    address: string;
    accountId: string;
    providerRef: string;
  };
}

export interface ElectricityVendRequest extends CreditSwitchBaseRequest {
  requestId: string;
  serviceId: string;
  amount: number;
  customerAccountId: string;
  phone: string;
  customerName: string;
  customerAddress: string;
}

export interface ElectricityVendResponse {
  statusCode: string;
  statusDescription: string;
  detail: ElectricityPrepaidDetail[] | ElectricityPostpaidDetail[];
}

export interface ElectricityPrepaidDetail {
  name: string;
  address: string;
  accountId: string;
  amount: number;
  units: string;
  token: number;
  tranxId: string;
}

export interface ElectricityPostpaidDetail {
  name: string;
  address: string;
  accountId: string;
  amount: string;
  tranxId: string;
}

// Service mappings for different bill types
export enum CreditSwitchElectricityServiceId {
  AEDC = 'E01E',
  EKEDC = 'E02E',
  IKEDC = 'E03E',
  KEDCO = 'E04E',
  PHED = 'E05E',
}

// Error handling interfaces
export interface CreditSwitchError {
  statusCode: string;
  statusDescription: string;
  error?: string;
  details?: string;
}

// Common error codes
export const CREDIT_SWITCH_ERROR_CODES = {
  SUCCESS: '00',
  GENERAL_ERROR: '01',
  INSUFFICIENT_BALANCE: '02',
  INVALID_RECIPIENT: '03',
  NETWORK_ERROR: '04',
  DUPLICATE_REQUEST: '05',
  INVALID_SERVICE_ID: '06',
  INVALID_AMOUNT: '07',
  MAINTENANCE: '08',
  TIMEOUT: '09',
  INVALID_CHECKSUM: '10',
} as const;

// Phone Number Lookup interfaces
export interface PhoneNumberLookupRequest {
  loginId: number;
  msisdn: string;
  key: string;
}

export interface PhoneNumberLookupResponse {
  statusCode: string;
  statusDescription: string;
  network: string;
}

// Data Plans interfaces
export interface DataPlansRequest {
  loginId: number;
  serviceId: string;
  key: string;
}

export interface DataPlan {
  amount: number;
  databundle: string;
  validity: string;
  productId: string;
}

export interface DataPlansResponse {
  statusCode: string;
  statusDescription: string;
  serviceId: string;
  dataPlan: DataPlan[];
}

// Cable TV interfaces
export interface ShowmaxPackagesRequest {
  loginId: number;
  key: string;
}

export interface ShowmaxPackage {
  packageId: string;
  packageName: string;
  amount: number;
  validity: string;
}

// export interface ShowmaxPackagesResponse {
//   statusCode: string;
//   statusDescription: string;
//   packages: ShowmaxPackage[];
// }

export interface ShowmaxPaymentRequest {
  loginId: number;
  key: string;
  requestId: string;
  packageId: string;
  amount: number;
  customerEmail: string;
  checksum: string;
}

export interface ShowmaxPaymentResponse {
  statusCode: string;
  statusDescription: string;
  requestId: string;
  transactionId?: string;
  customerEmail?: string;
  packageName?: string;
  amount?: number;
}

// Startimes interfaces
export interface StartimesProductListRequest {
  loginId: number;
  key: string;
}

export interface StartimesProduct {
  productId: string;
  productName: string;
  amount: number;
  validity: string;
}

export interface StartimesProductListResponse {
  statusCode: string;
  statusDescription: string;
  products: StartimesProduct[];
}

export interface StartimesValidationRequest {
  loginId: number;
  key: string;
  smartcardNumber: string;
  checksum: string;
}

export interface StartimesValidationResponse {
  statusCode: string;
  statusDescription: string;
  customerName?: string;
  smartcardNumber?: string;
  currentPackage?: string;
  balance?: string;
  dueDate?: string;
}

export interface StartimesVendRequest {
  loginId: number;
  key: string;
  smartCardCode: string;
  fee: number;
  checksum: string;
  transactionRef: string;
}

export interface StartimesVendResponse {
  statusCode: string;
  statusDescription: string;
  requestId: string;
  transactionId?: string;
  smartcardNumber?: string;
  productName?: string;
  amount?: number;
  token?: string;
}

// Multichoice (DSTV & GOTV) interfaces
export interface MultichoiceValidationRequest {
  loginId: number;
  key: string;
  customerNo: string;
  serviceId: string;
  checksum: string;
}

export interface MultichoiceValidationResponse {
  statusCode: string;
  statusDescription: {
    customerNo: string;
    firstname: string;
    lastname: string;
    customerType: string;
    invoicePeriod: string;
    dueDate: string;
    amount: number;
  };
}

export interface MultichoiceProductsRequest {
  loginId: number;
  key: string;
  serviceId: string;
}

export interface MultichoiceProduct {
  productId: string;
  productName: string;
  amount: number;
  validity: string;
  productType: string;
}

export interface MultichoiceProductsResponse {
  statusCode: string;
  statusDescription: string;
  serviceId: string;
  products: MultichoiceProduct[];
}

export interface MultichoiceAddonsRequest {
  loginId: number;
  key: string;
  serviceId: string;
  productId: string;
}

export interface MultichoiceAddon {
  addonId: string;
  addonName: string;
  amount: number;
  validity: string;
}

export interface MultichoiceAddonsResponse {
  statusCode: string;
  statusDescription: string;
  serviceId: string;
  productId: string;
  addons: MultichoiceAddon[];
}

export interface MultichoiceVendResponse {
  statusCode: string;
  statusDescription: string;
  requestId: string;
  transactionId?: string;
  customerNo?: string;
  productName?: string;
  amount?: number;
  token?: string;
}

export interface ShowmaxPackagesRequest {
  loginId: number;
  key: string;
}

export interface ShowmaxPackagesResponse {
  statusCode: string;
  statusDescription: {
    items: Array<{
      name: string;
      subscriptionPeriod: string;
      price: number;
      type: 'mobile_only' | 'full' | 'sports_mobile_only' | 'sports_full';
    }>;
  };
}

export interface ShowmaxPayRequest {
  loginId: number;
  key: string;
  serviceId: string; // SOMX
  customerNo: string;
  amount: number;
  requestId: string;
  subscriptionType: string;
  invoicePeriod: string;
  packageName: string;
}

export interface ShowmaxPayResponse {
  statusCode: string;
  statusDescription: string;
  status: boolean;
  result: {
    amount: string;
    message: string;
    transId: string;
    date: string;
    package: string;
    subscriptionPeriod: string;
    subscriptionType:
      | 'mobile_only'
      | 'full'
      | 'sports_mobile_only'
      | 'sports_full';
    validUntil: string;
    voucherCode: string;
    captureUrl: string;
  };
}

// =====================
// =====================
export interface StartimesFetchProductsRequest {
  loginId: number;
  key: string;
}

export interface StartimesFetchProductsResponse {
  statusCode: string;
  statusDescription: {
    items: Array<{
      code: string;
      name: string;
      description: string;
      availablePricingOptions: Array<{ price: string; invoicePeriod: string }>;
    }>;
  };
}

export interface StartimesValidateRequest {
  loginId: number;
  key: string;
  checksum: string;
  smartCardCode: string;
}

export interface StartimesValidateResponse {
  statusCode: string;
  statusDescription: string;
  balance: number;
  customerName: string;
  smartCardCode: string;
}

export interface StartimesVendRequest extends StartimesValidateRequest {
  fee: number;
  transactionRef: string;
}

export interface StartimesResponse {
  statusCode: number;
  [key: string]: any;
}

// =====================
// Multichoice (DSTV & GOTV)
// =====================
export interface MultichoiceValidateRequest {
  loginId: number;
  key: string;
  checksum: string;
  customerNo: string;
  serviceId: string; // dstv or gotv
}

export interface MultichoiceValidateResponse {
  statusCode: string;
  statusDescription: string;
}

export interface MultichoiceFetchProductsRequest {
  loginId: number;
  key: string;
  serviceId: string;
}

export interface MultichoiceFetchProductsResponse {
  statusCode: string;
  statusDescription: {
    items: Array<{
      availablePricingOptions: Array<{
        monthsPaidFor: number;
        price: number;
        invoicePeriod: number;
      }>;
      code: string;
      name: string;
      description: string;
    }>;
  };
}

export interface MultichoiceVendRequest {
  loginId: number;
  key: string;
  checksum: string;
  serviceId: string;
  transactionRef: string;
  customerNo: string;
  customerName: string;
  productsCodes: string[];
  amount: number;
  invoicePeriod: number;
}

export interface MultichoiceVendResponse {
  statusCode: string;
  statusDescription: string;
}

// Request/Response wrapper types
export type CreditSwitchRequest =
  | MerchantInfoRequest
  | AirtimeVendRequest
  | DataVendRequest
  | ElectricityValidationRequest
  | ElectricityVendRequest
  | PhoneNumberLookupRequest
  | DataPlansRequest
  | ShowmaxPackagesRequest
  | ShowmaxPayRequest;

export type CreditSwitchResponse =
  | MerchantInfoResponse
  | AirtimeVendResponse
  | DataVendResponse
  | ElectricityValidationResponse
  | ElectricityVendResponse
  | RequeryResponse
  | PhoneNumberLookupResponse
  | DataPlansResponse
  | ShowmaxPackagesResponse
  | ShowmaxPayResponse
  | StartimesFetchProductsResponse
  | StartimesValidateResponse
  | CreditSwitchError;
