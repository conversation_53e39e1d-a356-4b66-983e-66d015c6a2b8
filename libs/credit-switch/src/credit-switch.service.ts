import { HttpService } from '@nestjs/axios';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import {
  MerchantInfoRequest,
  MerchantInfoResponse,
  RequeryResponse,
  AirtimeVendRequest,
  AirtimeVendResponse,
  DataVendRequest,
  DataVendResponse,
  ElectricityValidationRequest,
  ElectricityValidationResponse,
  ElectricityVendRequest,
  ElectricityVendResponse,
  PhoneNumberLookupRequest,
  PhoneNumberLookupResponse,
  DataPlansRequest,
  DataPlansResponse,
  ShowmaxPackagesRequest,
  ShowmaxPackagesResponse,
  ShowmaxPayRequest,
  ShowmaxPayResponse,
  StartimesFetchProductsRequest,
  StartimesFetchProductsResponse,
  StartimesValidateRequest,
  StartimesValidateResponse,
  StartimesVendRequest,
  StartimesVendResponse,
  CreditSwitchServiceId,
  CreditSwitchElectricityServiceId,
  CreditSwitchNetwork,
  CreditSwitchStatusCode,
  ChecksumData,
  MultichoiceValidationResponse,
  MultichoiceValidationRequest,
  MultichoiceVendResponse,
  MultichoiceVendRequest,
  // MultichoiceProductsRequest,
  // MultichoiceProductsResponse,
  // MultichoiceAddonsRequest,
  // MultichoiceAddonsResponse,
  // MultichoiceFetchProductsRequest,
  MultichoiceFetchProductsResponse,
} from './credit-switch.interface';
import config from 'src/config';
import { CreditSwitchChecksumUtil } from './utils/checksum.util';
import { CreditSwitchLogger, CreditSwitchMonitor } from './utils/logger.util';

@Injectable()
export class CreditSwitchService {
  constructor(private readonly httpService: HttpService) {}

  /**
   * Generate checksum for Credit Switch API requests
   * Based on the security documentation, checksum is computed using specific fields
   */
  private generateChecksum(data: ChecksumData, secretKey: string): string {
    return CreditSwitchChecksumUtil.generateChecksum(data, secretKey);
  }

  /**
   * Get merchant account information including balance and available services
   */
  async getMerchantInfo(): Promise<MerchantInfoResponse> {
    const startTime = Date.now();
    const requestId = `merchant_info_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'getMerchantInfo',
      requestId,
    });

    const request: MerchantInfoRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      checksum: this.generateChecksum(
        {
          loginId: config.creditSwitch.loginId,
          key: config.creditSwitch.publicKey,
        },
        config.creditSwitch.secretKey,
      ),
    };

    CreditSwitchLogger.logRequest('/api/v1/mdetails', request, requestId);

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/mdetails', request).pipe(
          catchError((error: AxiosError) => {
            const duration = Date.now() - startTime;
            CreditSwitchLogger.logError({
              operation: 'getMerchantInfo',
              requestId,
              error,
              duration,
            });
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to fetch merchant info',
            );
          }),
        ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('getMerchantInfo', duration);

      CreditSwitchLogger.logResponse(
        '/api/v1/mdetails',
        data,
        requestId,
        duration,
      );
      CreditSwitchLogger.logSuccess({
        operation: 'getMerchantInfo',
        requestId,
        statusCode: data.statusCode,
        duration,
      });

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'getMerchantInfo',
        requestId,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Requery transaction status
   */
  async requery(
    requestId: string,
    serviceId: string,
  ): Promise<RequeryResponse> {
    const queryParams = new URLSearchParams({
      loginId: config.creditSwitch.loginId.toString(),
      key: config.creditSwitch.publicKey,
      requestId,
      serviceId,
    });

    try {
      const { data } = await firstValueFrom(
        this.httpService.get(`/api/v1/requery?${queryParams.toString()}`).pipe(
          catchError((error: AxiosError) => {
            console.error(
              'Credit Switch requery error:',
              error?.response?.data,
            );
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to requery transaction',
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      console.error('requery error:', error);
      throw error;
    }
  }

  /**
   * Perform airtime vend operation
   */
  async vendAirtime(
    network: CreditSwitchNetwork,
    amount: number,
    recipient: string,
    requestId: string,
  ): Promise<AirtimeVendResponse> {
    const serviceId = this.getServiceId(network, 'airtime');
    const date =
      new Date().toISOString().replace('T', ' ').substring(0, 19) + ' GMT';

    const request: AirtimeVendRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      requestId,
      serviceId,
      amount,
      recipient,
      date,
      checksum: this.generateChecksum(
        {
          loginId: config.creditSwitch.loginId,
          key: config.creditSwitch.publicKey,
          requestId,
          serviceId,
          amount,
          recipient,
          date,
        },
        config.creditSwitch.secretKey,
      ),
    };

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/mvend', request).pipe(
          catchError((error: AxiosError) => {
            console.error('Credit Switch vend error:', error?.response?.data);
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to vend airtime',
            );
          }),
        ),
      );

      console.log(data);

      return data;
    } catch (error) {
      console.error('vendAirtime error:', error);
      throw error;
    }
  }

  /**
   * Get the appropriate service ID for network and service type
   */
  private getServiceId(
    network: CreditSwitchNetwork,
    serviceType: 'airtime' | 'data',
  ): string {
    const serviceMap = {
      airtime: {
        [CreditSwitchNetwork.MTN]: CreditSwitchServiceId.MTN_AIRTIME,
        [CreditSwitchNetwork.AIRTEL]: CreditSwitchServiceId.AIRTEL_AIRTIME,
        [CreditSwitchNetwork.GLO]: CreditSwitchServiceId.GLO_AIRTIME,
        [CreditSwitchNetwork.NINE_MOBILE]:
          CreditSwitchServiceId.NINE_MOBILE_AIRTIME,
      },
      data: {
        [CreditSwitchNetwork.MTN]: CreditSwitchServiceId.MTN_DATA,
        [CreditSwitchNetwork.AIRTEL]: CreditSwitchServiceId.AIRTEL_DATA,
        [CreditSwitchNetwork.GLO]: CreditSwitchServiceId.GLO_DATA,
        [CreditSwitchNetwork.NINE_MOBILE]:
          CreditSwitchServiceId.NINE_MOBILE_DATA,
      },
    };

    return serviceMap[serviceType][network];
  }

  /**
   * Check if a transaction was successful based on status code
   */
  isTransactionSuccessful(statusCode: string): boolean {
    return statusCode === CreditSwitchStatusCode.SUCCESS;
  }

  /**
   * Map Credit Switch network to internal network enum
   */
  mapNetworkToInternal(network: string): CreditSwitchNetwork {
    const networkMap: Record<string, CreditSwitchNetwork> = {
      mtn: CreditSwitchNetwork.MTN,
      airtel: CreditSwitchNetwork.AIRTEL,
      glo: CreditSwitchNetwork.GLO,
      '9mobile': CreditSwitchNetwork.NINE_MOBILE,
    };

    return networkMap[network.toLowerCase()] || CreditSwitchNetwork.MTN;
  }

  /**
   * Perform data bundle vend operation
   */
  async vendData(
    network: CreditSwitchNetwork,
    amount: number,
    recipient: string,
    requestId: string,
    productId: string,
  ): Promise<DataVendResponse> {
    const serviceId = this.getServiceId(network, 'data');
    const date =
      new Date().toISOString().replace('T', ' ').substring(0, 19) + ' GMT';

    const request: DataVendRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      requestId,
      serviceId,
      amount,
      recipient,
      date,
      productId,
      checksum: this.generateChecksum(
        {
          loginId: config.creditSwitch.loginId,
          key: config.creditSwitch.publicKey,
          requestId,
          serviceId,
          amount,
          recipient,
          date,
        },
        config.creditSwitch.secretKey,
      ),
    };

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/dvend', request).pipe(
          catchError((error: AxiosError) => {
            console.error(
              'Credit Switch data vend error:',
              error?.response?.data,
            );
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to vend data',
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      console.error('vendData error:', error);
      throw error;
    }
  }

  /**
   * Validate electricity meter number
   */
  async validateElectricityMeter(
    serviceId: string,
    accountNumber: string,
  ): Promise<ElectricityValidationResponse> {
    const request: ElectricityValidationRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      serviceId,
      customerAccountId: accountNumber,
      checksum: this.generateChecksum(
        {
          loginId: config.creditSwitch.loginId,
          key: config.creditSwitch.publicKey,
          serviceId,
          recipient: accountNumber,
        },
        config.creditSwitch.secretKey,
      ),
    };

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/evalidate', request).pipe(
          catchError((error: AxiosError) => {
            console.error(
              'Credit Switch validation error:',
              error?.response?.data,
            );
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to validate meter',
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      console.error('validateElectricityMeter error:', error);
      throw error;
    }
  }

  /**
   * Perform electricity vend operation
   */
  async vendElectricity(
    serviceId: string,
    amount: number,
    accountNumber: string,
    phone: string,
    requestId: string,
    customerAddress: string,
    customerName: string,
  ): Promise<ElectricityVendResponse> {
    const request: ElectricityVendRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      requestId,
      serviceId,
      amount,
      customerAccountId: accountNumber,
      phone,
      customerName,
      customerAddress,
      checksum: CreditSwitchChecksumUtil.generateElectricityVendChecksum(
        config.creditSwitch.loginId,
        requestId,
        serviceId,
        amount,
        accountNumber,
        config.creditSwitch.secretKey,
      ),
    };

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/evend', request).pipe(
          catchError((error: AxiosError) => {
            console.error(
              'Credit Switch electricity vend error:',
              error?.response?.data,
            );
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to vend electricity',
            );
          }),
        ),
      );

      return data;
    } catch (error) {
      console.error('vendElectricity error:', error);
      throw error;
    }
  }

  /**
   * Get electricity service ID for a specific provider
   */
  getElectricityServiceId(provider: string): string {
    const providerMap: Record<string, string> = {
      aedc: CreditSwitchElectricityServiceId.AEDC,
      ekedc: CreditSwitchElectricityServiceId.EKEDC,
      ikedc: CreditSwitchElectricityServiceId.IKEDC,
      kedco: CreditSwitchElectricityServiceId.KEDCO,
      phed: CreditSwitchElectricityServiceId.PHED,
    };

    return (
      providerMap[provider.toLowerCase()] ||
      CreditSwitchElectricityServiceId.AEDC
    );
  }

  /**
   * Perform phone number lookup to verify network operator
   * This is a premium service that verifies the network operator of an 11-digit Nigerian mobile number
   */
  async lookupPhoneNumber(msisdn: string): Promise<PhoneNumberLookupResponse> {
    const startTime = Date.now();
    const requestId = `phone_lookup_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'lookupPhoneNumber',
      requestId,
      recipient: msisdn,
    });

    const request: PhoneNumberLookupRequest = {
      loginId: config.creditSwitch.loginId,
      msisdn: msisdn,
      key: config.creditSwitch.publicKey,
    };

    CreditSwitchLogger.logRequest('/api/v1/phonelookup', request, requestId);

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/phonelookup', request).pipe(
          catchError((error: AxiosError) => {
            const duration = Date.now() - startTime;
            CreditSwitchLogger.logError({
              operation: 'lookupPhoneNumber',
              requestId,
              recipient: msisdn,
              error,
              duration,
            });
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to lookup phone number',
            );
          }),
        ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('lookupPhoneNumber', duration);

      CreditSwitchLogger.logResponse(
        '/api/v1/phonelookup',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'lookupPhoneNumber',
          requestId,
          recipient: msisdn,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'lookupPhoneNumber',
        requestId,
        recipient: msisdn,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Get all available and active data plans based on the serviceId
   */
  async getDataPlans(serviceId: string): Promise<DataPlansResponse> {
    const startTime = Date.now();
    const requestId = `data_plans_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'getDataPlans',
      requestId,
      serviceId,
    });

    const request: DataPlansRequest = {
      loginId: config.creditSwitch.loginId,
      serviceId: serviceId,
      key: config.creditSwitch.publicKey,
    };

    CreditSwitchLogger.logRequest('/api/v1/mdataplans', request, requestId);

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/mdataplans', request).pipe(
          catchError((error: AxiosError) => {
            const duration = Date.now() - startTime;
            CreditSwitchLogger.logError({
              operation: 'getDataPlans',
              requestId,
              serviceId,
              error,
              duration,
            });
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to fetch data plans',
            );
          }),
        ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('getDataPlans', duration);

      CreditSwitchLogger.logResponse(
        '/api/v1/mdataplans',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'getDataPlans',
          requestId,
          serviceId,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'getDataPlans',
        requestId,
        serviceId,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Get Showmax subscription packages
   */
  async getShowmaxPackages(): Promise<ShowmaxPackagesResponse> {
    const startTime = Date.now();
    const requestId = `showmax_packages_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'getShowmaxPackages',
      requestId,
    });

    const request: ShowmaxPackagesRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
    };

    CreditSwitchLogger.logRequest('/showmax/packages', request, requestId);

    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get('api/v1/showmax/packages', { params: request })
          .pipe(
            catchError((error: AxiosError) => {
              const duration = Date.now() - startTime;
              CreditSwitchLogger.logError({
                operation: 'getShowmaxPackages',
                requestId,
                error,
                duration,
              });
              throw new UnprocessableEntityException(
                error.response?.data || 'Failed to fetch Showmax packages',
              );
            }),
          ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('getShowmaxPackages', duration);

      CreditSwitchLogger.logResponse(
        '/showmax/packages',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'getShowmaxPackages',
          requestId,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'getShowmaxPackages',
        requestId,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Process Showmax subscription payment
   */
  async payShowmaxSubscription(
    customerNo: string,
    amount: number,
    subscriptionType: string,
    invoicePeriod: string,
    packageName: string,
    requestId: string,
  ): Promise<ShowmaxPayResponse> {
    const startTime = Date.now();

    CreditSwitchLogger.logStart({
      operation: 'payShowmaxSubscription',
      requestId,
      recipient: customerNo,
      amount,
    });

    const request: ShowmaxPayRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      serviceId: 'SOMX',
      customerNo,
      amount,
      requestId,
      subscriptionType,
      invoicePeriod,
      packageName,
    };

    CreditSwitchLogger.logRequest('/showmax/pay', request, requestId);

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/showmax/pay', request).pipe(
          catchError((error: AxiosError) => {
            const duration = Date.now() - startTime;
            CreditSwitchLogger.logError({
              operation: 'payShowmaxSubscription',
              requestId,
              recipient: customerNo,
              amount,
              error,
              duration,
            });
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to process Showmax payment',
            );
          }),
        ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('payShowmaxSubscription', duration);

      CreditSwitchLogger.logResponse('/showmax/pay', data, requestId, duration);

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'payShowmaxSubscription',
          requestId,
          recipient: customerNo,
          amount,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'payShowmaxSubscription',
        requestId,
        recipient: customerNo,
        amount,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Fetch Startimes product list
   */
  async getStartimesProducts(): Promise<StartimesFetchProductsResponse> {
    const startTime = Date.now();
    const requestId = `startimes_products_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'getStartimesProducts',
      requestId,
    });

    const request: StartimesFetchProductsRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
    };

    CreditSwitchLogger.logRequest(
      '/api/v1/startimes/fetchProductList',
      request,
      requestId,
    );

    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post('/api/v1/startimes/fetchProductList', request)
          .pipe(
            catchError((error: AxiosError) => {
              const duration = Date.now() - startTime;
              CreditSwitchLogger.logError({
                operation: 'getStartimesProducts',
                requestId,
                error,
                duration,
              });
              throw new UnprocessableEntityException(
                error.response?.data || 'Failed to fetch Startimes products',
              );
            }),
          ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('getStartimesProducts', duration);

      CreditSwitchLogger.logResponse(
        '/api/v1/startimes/fetchProductList',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'getStartimesProducts',
          requestId,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'getStartimesProducts',
        requestId,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Validate Startimes smartcard
   */
  async validateStartimesSmartcard(
    smartcardNumber: string,
  ): Promise<StartimesValidateResponse> {
    const startTime = Date.now();
    const requestId = `startimes_validate_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'validateStartimesSmartcard',
      requestId,
      recipient: smartcardNumber,
    });

    const checksum = CreditSwitchChecksumUtil.generateStartimesValidateChecksum(
      config.creditSwitch.loginId,
      smartcardNumber,
      config.creditSwitch.secretKey,
    );

    const request: StartimesValidateRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      smartCardCode: smartcardNumber,
      checksum,
    };

    CreditSwitchLogger.logRequest('/api/v1/starvalidate1', request, requestId);

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/starvalidate1', request).pipe(
          catchError((error: AxiosError) => {
            const duration = Date.now() - startTime;
            CreditSwitchLogger.logError({
              operation: 'validateStartimesSmartcard',
              requestId,
              recipient: smartcardNumber,
              error,
              duration,
            });
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to validate Startimes smartcard',
            );
          }),
        ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration(
        'validateStartimesSmartcard',
        duration,
      );

      CreditSwitchLogger.logResponse(
        '/api/v1/starvalidate1',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'validateStartimesSmartcard',
          requestId,
          recipient: smartcardNumber,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'validateStartimesSmartcard',
        requestId,
        recipient: smartcardNumber,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Process Startimes payment
   */
  async vendStartimes(
    smartCardCode: string,
    fee: number,
    transactionRef: string,
  ): Promise<StartimesVendResponse> {
    const startTime = Date.now();
    const requestId = `startimes_vend_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'vendStartimes',
      requestId,
      recipient: smartCardCode,
      amount: fee,
    });

    const checksum = CreditSwitchChecksumUtil.generateStartimesVendChecksum(
      config.creditSwitch.loginId,
      smartCardCode,
      fee,
      config.creditSwitch.secretKey,
    );

    const request: StartimesVendRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      smartCardCode,
      checksum,
      fee,
      transactionRef,
    };

    CreditSwitchLogger.logRequest('/api/v1/starvend1', request, requestId);

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/starvend1', request).pipe(
          catchError((error: AxiosError) => {
            const duration = Date.now() - startTime;
            CreditSwitchLogger.logError({
              operation: 'vendStartimes',
              requestId,
              recipient: smartCardCode,
              amount: fee,
              error,
              duration,
            });
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to process Startimes payment',
            );
          }),
        ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('vendStartimes', duration);

      CreditSwitchLogger.logResponse(
        '/api/v1/starvend1',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'vendStartimes',
          requestId,
          recipient: smartCardCode,
          amount: fee,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'vendStartimes',
        requestId,
        recipient: smartCardCode,
        amount: fee,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Validate Multichoice customer number
   */
  async validateMultichoiceCustomerNumber(
    customerNo: string,
    serviceId: string,
  ): Promise<MultichoiceValidationResponse> {
    const startTime = Date.now();
    const requestId = `multichoice_validate_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'validateMultichoiceCustomerNumber',
      requestId,
      recipient: customerNo,
    });

    const request: MultichoiceValidationRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      customerNo,
      serviceId,
      checksum: CreditSwitchChecksumUtil.generateMultichoiceValidationChecksum(
        config.creditSwitch.loginId,
        customerNo,
        config.creditSwitch.secretKey,
      ),
    };

    CreditSwitchLogger.logRequest(
      '/api/v1/cabletv/multichoice/validate',
      request,
      requestId,
    );

    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post('/api/v1/cabletv/multichoice/validate', request)
          .pipe(
            catchError((error: AxiosError) => {
              const duration = Date.now() - startTime;
              CreditSwitchLogger.logError({
                operation: 'validateMultichoiceCustomerNumber',
                requestId,
                recipient: customerNo,
                error,
                duration,
              });
              throw new UnprocessableEntityException(
                error.response?.data ||
                  'Failed to validate Multichoice customer number',
              );
            }),
          ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration(
        'validateMultichoiceCustomerNumber',
        duration,
      );

      CreditSwitchLogger.logResponse(
        '/api/v1/cabletv/multichoice/validate',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'validateMultichoiceCustomerNumber',
          requestId,
          recipient: customerNo,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'validateMultichoiceCustomerNumber',
        requestId,
        recipient: customerNo,
        error,
        duration,
      });
      throw error;
    }
  }

  /**
   * Process Multichoice payment
   */
  async vendMultichoice(
    customerNo: string,
    amount: number,
    transactionRef: string,
    serviceId: 'dstv' | 'gotv',
    customerName: string,
    productsCodes: string[] = [],
    invoicePeriod: number = 1,
  ): Promise<MultichoiceVendResponse> {
    const startTime = Date.now();
    const requestId = `multichoice_pay_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'vendMultichoice',
      requestId,
      recipient: customerNo,
      amount,
    });

    const request: MultichoiceVendRequest = {
      loginId: config.creditSwitch.loginId,
      key: config.creditSwitch.publicKey,
      checksum: CreditSwitchChecksumUtil.generateMultichoiceVendChecksum(
        config.creditSwitch.loginId,
        customerNo,
        transactionRef,
        amount,
        config.creditSwitch.secretKey,
      ),
      serviceId,
      customerNo,
      customerName,
      transactionRef,
      amount,
      productsCodes,
      invoicePeriod,
    };

    CreditSwitchLogger.logRequest(
      '/api/v1/cabletv/multichoice/vend',
      request,
      requestId,
    );

    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/api/v1/cabletv/multichoice/vend', request).pipe(
          catchError((error: AxiosError) => {
            const duration = Date.now() - startTime;
            CreditSwitchLogger.logError({
              operation: 'vendMultichoice',
              requestId,
              recipient: customerNo,
              amount,
              error,
              duration,
            });
            throw new UnprocessableEntityException(
              error.response?.data || 'Failed to process Multichoice payment',
            );
          }),
        ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('vendMultichoice', duration);

      CreditSwitchLogger.logResponse(
        '/api/v1/cabletv/multichoice/vend',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'vendMultichoice',
          requestId,
          recipient: customerNo,
          amount,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'vendMultichoice',
        requestId,
        recipient: customerNo,
        amount,
        error,
        duration,
      });
      throw error;
    }
  }

  async fetchMultiChoiceProducts(
    serviceId: 'dstv' | 'gotv',
  ): Promise<MultichoiceFetchProductsResponse> {
    const startTime = Date.now();
    const requestId = `multichoice_fetch_products_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'fetchMultiChoiceProducts',
      requestId,
    });

    const request = {
      loginId: config.creditSwitch.loginId.toString(),
      key: config.creditSwitch.publicKey,
      serviceId,
    };

    CreditSwitchLogger.logRequest(
      '/api/v1/cabletv/multichoice/fetchproducts',
      request,
      requestId,
    );

    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post('/api/v1/cabletv/multichoice/fetchproducts', request)
          .pipe(
            catchError((error: AxiosError) => {
              const duration = Date.now() - startTime;
              CreditSwitchLogger.logError({
                operation: 'fetchMultiChoiceProducts',
                requestId,
                error,
                duration,
              });
              throw new UnprocessableEntityException(
                error.response?.data || 'Failed to fetch Multichoice products',
              );
            }),
          ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('fetchMultiChoiceProducts', duration);

      CreditSwitchLogger.logResponse(
        '/api/v1/cabletv/multichoice/fetchproducts',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'fetchMultiChoiceProducts',
          requestId,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'fetchMultiChoiceProducts',
        requestId,
        error,
        duration,
      });
      throw error;
    }
  }

  async fetchMultiChoiceProductsAddons(
    serviceId: 'dstv' | 'gotv',
  ): Promise<MultichoiceFetchProductsResponse> {
    const startTime = Date.now();
    const requestId = `multichoice_fetch_products_${Date.now()}`;

    CreditSwitchLogger.logStart({
      operation: 'fetchMultiChoiceProductsAddons',
      requestId,
    });

    const request = {
      loginId: config.creditSwitch.loginId.toString(),
      key: config.creditSwitch.publicKey,
      serviceId,
    };

    CreditSwitchLogger.logRequest(
      '/api/v1/cabletv/multichoice/productaddons',
      request,
      requestId,
    );

    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post('/api/v1/cabletv/multichoice/productaddons', request)
          .pipe(
            catchError((error: AxiosError) => {
              const duration = Date.now() - startTime;
              CreditSwitchLogger.logError({
                operation: 'fetchMultiChoiceProductsAddons',
                requestId,
                error,
                duration,
              });
              throw new UnprocessableEntityException(
                error.response?.data || 'Failed to fetch Multichoice products',
              );
            }),
          ),
      );

      const duration = Date.now() - startTime;
      CreditSwitchMonitor.recordDuration('fetchMultiChoiceProducts', duration);

      CreditSwitchLogger.logResponse(
        '/api/v1/cabletv/multichoice/productaddons',
        data,
        requestId,
        duration,
      );

      if (data && data.statusCode) {
        CreditSwitchLogger.logSuccess({
          operation: 'fetchMultiChoiceProducts',
          requestId,
          statusCode: data.statusCode,
          duration,
        });
      }

      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      CreditSwitchLogger.logError({
        operation: 'fetchMultiChoiceProducts',
        requestId,
        error,
        duration,
      });
      throw error;
    }
  }
}
