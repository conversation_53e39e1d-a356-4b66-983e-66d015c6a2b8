# Credit Switch API Integration

This document provides comprehensive information about the Credit Switch API integration for bills payment services.

## Overview

The Credit Switch integration enables the bills payment service to process airtime, data bundle, and electricity bill payments through the Credit Switch platform. The integration follows Credit Switch's recommended 4-step integration flow for reliable transaction processing.

## Architecture

The Credit Switch integration follows the same architectural patterns as other payment providers in the system:

```
libs/credit-switch/
├── src/
│   ├── credit-switch.module.ts          # NestJS module
│   ├── credit-switch.service.ts         # Main service class
│   ├── credit-switch.interface.ts       # TypeScript interfaces
│   ├── utils/
│   │   ├── checksum.util.ts            # Checksum generation utilities
│   │   └── logger.util.ts              # Logging and monitoring utilities
│   └── index.ts                        # Module exports
```

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Credit Switch API Configuration
CREDIT_SWITCH_BASE_URL=https://api.creditswitch.com
CREDIT_SWITCH_LOGIN_ID=your_login_id
CREDIT_SWITCH_PUBLIC_KEY=your_public_key
CREDIT_SWITCH_SECRET_KEY=your_secret_key
```

### Configuration Object

The configuration is automatically loaded in `src/config/index.ts`:

```typescript
creditSwitch: {
  baseUrl: process.env.CREDIT_SWITCH_BASE_URL,
  loginId: Number.parseInt(process.env.CREDIT_SWITCH_LOGIN_ID, 10),
  publicKey: process.env.CREDIT_SWITCH_PUBLIC_KEY,
  secretKey: process.env.CREDIT_SWITCH_SECRET_KEY,
}
```

## Available Services

Credit Switch provides the following services:

1. **Merchant Information** - Get merchant account details and balance
2. **Airtime Purchase** - Buy airtime for mobile networks (MTN, Airtel, Glo, 9mobile)
3. **Data Bundle Purchase** - Buy data bundles for mobile networks
4. **Electricity Bill Validation** - Validate electricity meter numbers
5. **Electricity Bill Payment** - Pay electricity bills for various DISCOs
6. **Phone Number Lookup** - Verify network operator of Nigerian mobile numbers (Premium service)
7. **Data Plans Retrieval** - Get all available and active data plans by service ID

## API Endpoints

### 1. Merchant Info (`/api/v1/mdetails`)

Fetches merchant account information including balance and available services.

**Request:**

```typescript
{
  loginId: number;
  key: string;
  checksum: string;
}
```

**Response:**

```typescript
{
  statusCode: string;
  statusDescription: {
    name: string;
    balance: string;
    status: string;
    email: string;
    allowed_ips: string[];
    serviceDetail: string[][];
    transactionsToday: number;
  };
}
```

### 2. Requery (`/api/v1/requery`)

Checks the status of an existing transaction.

**Request (Query Parameters):**

```
loginId=12345&key=public_key&requestId=transaction_id&serviceId=A01E
```

**Response:**

```typescript
{
  statusCode: string;
  statusDescription: string;
  mReference: string;
  tranxReference: number;
  recipient: string;
  amount: string;
  confirmCode?: string;  // For airtime/data
  network?: string;      // For airtime/data
  token?: string;        // For electricity
  tranxDate: string;
}
```

### 3. Airtime Vend (`/api/v1/mvend`)

Performs airtime top-up operations.

**Request:**

```typescript
{
  loginId: number;
  key: string;
  requestId: string;
  serviceId: string;
  amount: number;
  recipient: string;
  date: string;
  checksum: string;
}
```

## Service IDs

### Airtime Services

- `A01E` - MTN Airtime
- `A02E` - Airtel Airtime
- `A03E` - Glo Airtime
- `A04E` - 9mobile Airtime

### Data Services

- `D01D` - MTN Data
- `D02D` - Airtel Data
- `D03D` - Glo Data
- `D04D` - 9mobile Data

### Electricity Services

- `E01E` - AEDC
- `E02E` - EKEDC
- `E03E` - IKEDC
- `E04E` - KEDCO
- `E05E` - PHED

## Security - Checksum Implementation

Credit Switch uses Bcrypt hashing with Base64 encoding for request validation. The algorithm is:

**Algorithm:** `Checksum = Base64(Bcrypt(ConcatString))`

The checksum is computed differently for different request types using pipe ("|") separators:

### Merchant Details Request

```
concatString = loginId + "|" + privateKey
checksum = Base64(Bcrypt(concatString))
```

### Airtime/Data Vend Request

```
concatString = loginId + "|" + requestId + "|" + serviceId + "|" + requestAmount + "|" + privateKey + "|" + recipient
checksum = Base64(Bcrypt(concatString))
```

### Electricity Validation Request

```
concatString = loginId + "|" + serviceId + "|" + privateKey + "|" + customerAccountId
checksum = Base64(Bcrypt(concatString))
```

### Electricity Vend Request

```
concatString = loginId + "|" + serviceId + "|" + privateKey + "|" + customerAccountId + "|" + requestId + "|" + amount
checksum = Base64(Bcrypt(concatString))
```

## Error Handling

### Status Codes

| Code | Description          | Retryable | Final | Escalate |
| ---- | -------------------- | --------- | ----- | -------- |
| 00   | Successful           | No        | No    | No       |
| 01   | General error        | Yes       | No    | Yes      |
| 02   | Insufficient balance | No        | Yes   | Yes      |
| 03   | Invalid recipient    | No        | Yes   | No       |
| 04   | Network error        | Yes       | No    | No       |
| 05   | Duplicate request    | No        | Yes   | No       |
| 06   | Invalid service ID   | No        | Yes   | No       |
| 07   | Invalid amount       | No        | Yes   | No       |
| 08   | System maintenance   | Yes       | No    | No       |
| 09   | Request timeout      | Yes       | No    | No       |
| 10   | Invalid checksum     | No        | Yes   | Yes      |

### Retry Logic

The integration implements exponential backoff retry logic:

- **Max Retries:** 3
- **Base Delay:** 2 seconds
- **Max Delay:** 30 seconds
- **Backoff Multiplier:** 2
- **Jitter:** Enabled (±25%)

### 4-Step Integration Flow

1. **Validation:** Validate customer information (for electricity)
2. **Vend Request:** Send the payment request
3. **Error Handling:** Handle errors with appropriate retry logic
4. **Support Escalation:** Escalate to support for critical errors

## Usage Examples

### Basic Airtime Purchase

```typescript
import { CreditSwitchService, CreditSwitchNetwork } from '@app/credit-switch';

// Inject the service
constructor(private readonly creditSwitchService: CreditSwitchService) {}

// Purchase airtime
async purchaseAirtime() {
  try {
    const result = await this.creditSwitchService.vendAirtime(
      CreditSwitchNetwork.MTN,
      100,
      '***********',
      'unique-request-id'
    );

    if (this.creditSwitchService.isTransactionSuccessful(result.statusCode)) {
      console.log('Airtime purchase successful:', result);
    } else {
      console.log('Airtime purchase failed:', result.statusDescription);
    }
  } catch (error) {
    console.error('Error purchasing airtime:', error);
  }
}
```

### Electricity Bill Payment

```typescript
// Validate meter first
const validation = await this.creditSwitchService.validateElectricityMeter(
  'E01E', // AEDC
  '12345678901',
  'validation-request-id',
);

if (this.creditSwitchService.isTransactionSuccessful(validation.statusCode)) {
  // Proceed with payment
  const result = await this.creditSwitchService.vendElectricity(
    'E01E',
    1000,
    '12345678901',
    '***********',
    'payment-request-id',
  );
}
```

### Transaction Requery

```typescript
// Check transaction status
const status = await this.creditSwitchService.requery(
  'transaction-reference',
  'A01E',
);

console.log('Transaction status:', status.statusDescription);
```

## Logging and Monitoring

The integration includes comprehensive logging and monitoring:

### Log Levels

- **INFO:** Successful operations, operation start/end
- **WARN:** Retry attempts, non-critical issues
- **ERROR:** Failed operations, critical errors
- **DEBUG:** API requests/responses (development only)

### Monitoring Metrics

- Operation duration tracking
- Success/failure rates
- Average response times
- Error distribution

### Log Format

All logs are structured JSON with the following fields:

```json
{
  "level": "info",
  "message": "[CREDIT_SWITCH] Operation successful",
  "operation": "vendAirtime",
  "requestId": "req-123",
  "serviceId": "A01E",
  "amount": 100,
  "recipient": "080****678",
  "statusCode": "00",
  "duration": 1250,
  "timestamp": "2023-01-01T12:00:00.000Z"
}
```

## Testing

### Running Tests

```bash
# Run Credit Switch service tests
npm test libs/credit-switch/src/credit-switch.service.spec.ts

# Run checksum utility tests
npm test libs/credit-switch/src/utils/checksum.util.spec.ts

# Run error handling tests
npm test src/utils/credit-switch-errors.spec.ts
```

### Test Coverage

The test suite covers:

- All service methods (merchant info, requery, vend operations)
- Checksum generation and validation
- Error handling and retry logic
- Network mapping and service ID resolution
- Success and failure scenarios

## Troubleshooting

### Common Issues

1. **Invalid Checksum (Code: 10)**

   - Verify secret key configuration
   - Check request parameter order
   - Ensure date format is correct

2. **Insufficient Balance (Code: 02)**

   - Check merchant account balance
   - Contact Credit Switch support for funding

3. **Network Errors (Code: 04)**

   - Check internet connectivity
   - Verify Credit Switch API endpoint
   - Review firewall settings

4. **Invalid Service ID (Code: 06)**
   - Verify service ID mapping
   - Check if service is available for merchant

### Debug Mode

Enable debug logging by setting the log level to debug in your application configuration.

### Support Escalation

For errors that require escalation (codes: 01, 02, 10), the system automatically logs escalation events. Contact Credit Switch <NAME_EMAIL> with the following information:

- Request ID
- Error code and description
- Timestamp
- Merchant login ID

### 8. Phone Number Lookup (`/api/v1/phonelookup`)

**Premium Service** - Verifies the network operator of an 11-digit Nigerian mobile number.

**Request:**

```typescript
{
  loginId: number;
  msisdn: string; // 11-digit Nigerian phone number (e.g., "***********")
  key: string; // Public key
}
```

**Response:**

```typescript
{
  statusCode: string;
  statusDescription: string;
  network?: string;      // Network name (e.g., "MTN", "AIRTEL", "GLO", "9MOBILE")
  operator?: string;     // Full operator name (e.g., "MTN Nigeria")
  msisdn?: string;       // The queried phone number
}
```

**Usage Example:**

```typescript
const response = await creditSwitchService.lookupPhoneNumber('***********');
if (response.statusCode === '00') {
  console.log(`Phone number belongs to ${response.network}`);
}
```

### 9. Data Plans (`/api/v1/mdataplans`)

Retrieves all available and active data plans for a specific service ID.

**Request:**

```typescript
{
  loginId: number;
  serviceId: string; // Service ID (e.g., "D01D" for MTN Data)
  key: string; // Public key
}
```

**Response:**

```typescript
{
  statusCode: string;
  statusDescription: string;
  serviceId: string;
  dataPlan: Array<{
    amount: number; // Plan price in Naira
    databundle: string; // Data allowance (e.g., "1GB", "500MB")
    validity: string; // Validity period (e.g., "30days", "1day")
    productId: string; // Unique product identifier
  }>;
}
```

**Service IDs:**

- `D01D` - MTN Data
- `D02D` - Airtel Data
- `D03D` - Glo Data
- `D04D` - 9mobile Data

**Usage Example:**

```typescript
const response = await creditSwitchService.getDataPlans('D01D');
if (response.statusCode === '00') {
  response.dataPlan.forEach((plan) => {
    console.log(`${plan.databundle} - ₦${plan.amount} (${plan.validity})`);
  });
}
```

## Data Plans Management

The system includes automated data plans management with the following features:

### Automated Seeding

- **Schedule**: Daily at 2:00 AM via cron job
- **Networks**: MTN, Airtel, Glo, 9mobile
- **Storage**: Data plans are stored in the `data_bundles` table
- **Updates**: Existing plans are cleared and replaced with fresh data

### Manual Seeding

```typescript
// Trigger manual data plans refresh
await providerService.seedCreditSwitchDataPlans();
```

### Data Plans Retrieval

```typescript
// Get all Credit Switch data plans
const allPlans = await providerService.getCreditSwitchDataPlans();

// Get plans for specific network
const mtnPlans = await providerService.getCreditSwitchDataPlans('mtn');

// Search plans with criteria
const searchResults = await providerService.searchCreditSwitchDataPlans({
  network: 'mtn',
  minAmount: 100,
  maxAmount: 1000,
  validity: '30days',
});
```

### Provider Integration

The new services are integrated into the provider system:

**Phone Number Lookup:**

```bash
POST /provider/lookup-creditswitch-phone
{
  "phoneNumber": "***********"
}
```

**Data Plans Endpoints:**

```bash
# Get all Credit Switch data plans
GET /provider/creditswitch/dataplans

# Get plans for specific network
GET /provider/creditswitch/dataplans/mtn

# Manual seed trigger
POST /provider/creditswitch/seed-dataplans
```

## API Documentation

For complete API documentation, visit: https://developers.creditswitch.com/

## Security Considerations

1. **Secret Key Protection:** Never expose the secret key in client-side code
2. **Request ID Uniqueness:** Always use unique request IDs to prevent duplicate transactions
3. **Checksum Validation:** Always validate checksums for incoming webhook requests
4. **IP Whitelisting:** Configure allowed IP addresses in Credit Switch dashboard
5. **HTTPS Only:** All API communications must use HTTPS

## Performance Optimization

1. **Connection Pooling:** HTTP service uses connection pooling for better performance
2. **Timeout Configuration:** 100-second timeout for API requests
3. **Retry Logic:** Exponential backoff prevents API overload
4. **Caching:** Merchant info can be cached for short periods
5. **Monitoring:** Track response times and optimize slow operations

## Integration Checklist

- [ ] Environment variables configured
- [ ] Credit Switch credentials obtained
- [ ] IP addresses whitelisted
- [ ] Test transactions successful
- [ ] Error handling tested
- [ ] Logging configured
- [ ] Monitoring set up
- [ ] Documentation reviewed
- [ ] Security audit completed
- [ ] Production deployment approved
